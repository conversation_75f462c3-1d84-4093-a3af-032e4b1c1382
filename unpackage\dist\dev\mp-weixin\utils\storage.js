"use strict";
const common_vendor = require("../common/vendor.js");
const STORAGE_KEYS = {
  RECORDS: "bookkeeping_records",
  CATEGORIES: "bookkeeping_categories",
  SETTINGS: "bookkeeping_settings"
};
class Storage {
  /**
   * 设置存储数据
   * @param {string} key 存储键
   * @param {any} data 存储数据
   */
  static set(key, data) {
    try {
      const jsonData = JSON.stringify(data);
      common_vendor.index.setStorageSync(key, jsonData);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:23", "Storage set error:", error);
      return false;
    }
  }
  /**
   * 获取存储数据
   * @param {string} key 存储键
   * @param {any} defaultValue 默认值
   */
  static get(key, defaultValue = null) {
    try {
      const jsonData = common_vendor.index.getStorageSync(key);
      if (jsonData) {
        return JSON.parse(jsonData);
      }
      return defaultValue;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:41", "Storage get error:", error);
      return defaultValue;
    }
  }
  /**
   * 删除存储数据
   * @param {string} key 存储键
   */
  static remove(key) {
    try {
      common_vendor.index.removeStorageSync(key);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:55", "Storage remove error:", error);
      return false;
    }
  }
  /**
   * 清空所有存储数据
   */
  static clear() {
    try {
      common_vendor.index.clearStorageSync();
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:68", "Storage clear error:", error);
      return false;
    }
  }
  // 记账记录相关
  static getRecords() {
    return this.get(STORAGE_KEYS.RECORDS, []);
  }
  static setRecords(records) {
    return this.set(STORAGE_KEYS.RECORDS, records);
  }
  // 分类相关
  static getCategories() {
    return this.get(STORAGE_KEYS.CATEGORIES, this.getDefaultCategories());
  }
  static setCategories(categories) {
    return this.set(STORAGE_KEYS.CATEGORIES, categories);
  }
  // 设置相关
  static getSettings() {
    return this.get(STORAGE_KEYS.SETTINGS, this.getDefaultSettings());
  }
  static setSettings(settings) {
    return this.set(STORAGE_KEYS.SETTINGS, settings);
  }
  // 默认分类
  static getDefaultCategories() {
    return {
      expense: [
        { id: 1, name: "餐饮", icon: "🍽️", color: "#FF6B6B" },
        { id: 2, name: "交通", icon: "🚗", color: "#4ECDC4" },
        { id: 3, name: "购物", icon: "🛍️", color: "#45B7D1" },
        { id: 4, name: "娱乐", icon: "🎮", color: "#96CEB4" },
        { id: 5, name: "医疗", icon: "🏥", color: "#FFEAA7" },
        { id: 6, name: "教育", icon: "📚", color: "#DDA0DD" },
        { id: 7, name: "住房", icon: "🏠", color: "#98D8C8" },
        { id: 8, name: "其他", icon: "📝", color: "#F7DC6F" }
      ],
      income: [
        { id: 101, name: "工资", icon: "💰", color: "#2ECC71" },
        { id: 102, name: "奖金", icon: "🎁", color: "#3498DB" },
        { id: 103, name: "投资", icon: "📈", color: "#9B59B6" },
        { id: 104, name: "兼职", icon: "💼", color: "#E67E22" },
        { id: 105, name: "其他", icon: "📝", color: "#1ABC9C" }
      ]
    };
  }
  // 默认设置
  static getDefaultSettings() {
    return {
      currency: "¥",
      theme: "light",
      budgetAlert: true,
      monthlyBudget: 0,
      firstDayOfWeek: 1,
      // 1: 周一, 0: 周日
      autoBackup: false
    };
  }
}
exports.Storage = Storage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/storage.js.map
