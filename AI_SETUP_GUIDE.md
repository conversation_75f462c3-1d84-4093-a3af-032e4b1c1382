# AI智能记账功能配置指南

## 概述

本项目已集成阿里云百炼AI模型，支持通过语音、图片和文字进行智能记账。用户可以：

- 🎤 **语音记账**：说出消费内容，AI自动识别并填写记账信息
- 📷 **拍照记账**：拍摄发票、收据，AI自动提取金额、商家、分类等信息  
- ✏️ **文字记账**：输入消费描述，AI智能分析并生成结构化记账数据

## 功能特点

### 1. 多模态AI输入
- **语音识别**：支持微信小程序和抖音小程序的语音转文字
- **图像识别**：使用阿里云qwen-vl-plus模型识别发票、收据内容
- **文本分析**：使用阿里云qwen-plus模型分析自然语言描述

### 2. 智能数据提取
- **金额识别**：自动提取消费金额
- **商家识别**：识别商家名称和品牌
- **分类映射**：智能匹配到预设的记账分类
- **置信度评分**：提供AI识别结果的可信度评分

### 3. 用户体验优化
- **实时反馈**：显示AI处理进度和状态
- **结果确认**：用户可以编辑AI识别的结果
- **错误处理**：优雅处理AI服务异常和网络错误

## 配置步骤

### 第一步：获取阿里云百炼API密钥

1. 访问 [阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 登录您的阿里云账号
3. 开通百炼服务（如果尚未开通）
4. 在API密钥管理页面创建新的API Key
5. 复制生成的API Key

### 第二步：配置API密钥

在 `utils/ai-service.js` 文件中找到以下配置：

```javascript
class AIService {
  constructor() {
    // 配置阿里云百炼API
    this.apiKey = 'YOUR_BAILIAN_API_KEY_HERE' // 替换为您的API密钥
    this.baseURL = 'https://dashscope.aliyuncs.com/api/v1'
    
    // 模型配置
    this.textModel = 'qwen-plus'        // 文本分析模型
    this.imageModel = 'qwen-vl-plus'    // 图像分析模型
  }
  // ...
}
```

将 `YOUR_BAILIAN_API_KEY_HERE` 替换为您在第一步中获取的API密钥。

### 第三步：测试配置

1. 在微信开发者工具或HBuilderX中打开项目
2. 运行项目到微信小程序
3. 在首页点击"AI记账"按钮
4. 尝试使用语音、图片或文字功能

## 使用说明

### 语音记账使用方法

1. 点击首页的"AI记账"按钮
2. 选择"语音记账"标签
3. 按住"按住说话"按钮
4. 清晰地说出消费内容，例如：
   - "今天在麦当劳花了25块钱吃午餐"
   - "刚才打车到机场花了80元"
   - "在超市买菜花了45块钱"
5. 松开按钮，等待AI分析
6. 确认或编辑AI识别的结果
7. 点击"保存记录"

### 拍照记账使用方法

1. 点击首页的"AI记账"按钮
2. 选择"拍照记账"标签
3. 点击"拍照"或"从相册选择"
4. 选择包含消费信息的图片（发票、收据等）
5. 点击"分析"按钮
6. 等待AI识别图片内容
7. 确认或编辑AI识别的结果
8. 点击"保存记录"

### 文字记账使用方法

1. 点击首页的"AI记账"按钮
2. 选择"文字记账"标签
3. 在文本框中输入消费描述
4. 点击"AI分析"按钮
5. 等待AI分析文本内容
6. 确认或编辑AI识别的结果
7. 点击"保存记录"

## 支持的分类

AI会自动将识别的消费内容映射到以下分类：

- 🍔 **餐饮**：餐厅、外卖、咖啡、零食等
- 🚗 **交通**：打车、公交、地铁、加油等
- 🛍️ **购物**：服装、日用品、电子产品等
- 🎬 **娱乐**：电影、游戏、旅游等
- 🏥 **医疗**：看病、买药、体检等
- 📚 **教育**：学费、培训、书籍等
- 🏠 **居住**：房租、水电、物业等
- 📝 **其他**：无法分类的其他消费

## 注意事项

### 隐私保护
- 语音和图片数据仅用于AI分析，不会存储在云端
- 所有记账数据存储在您的uniCloud空间中
- API调用使用HTTPS加密传输

### 使用限制
- 需要网络连接才能使用AI功能
- 语音录制需要麦克风权限
- 拍照功能需要相机权限
- API调用可能产生费用，请查看阿里云百炼的计费说明

### 最佳实践
- 语音录制时保持环境安静，说话清晰
- 拍照时确保图片清晰，文字可见
- 文字描述尽量详细，包含金额、商家、用途等信息
- 及时确认AI识别结果，避免错误记录

## 故障排除

### 常见问题

**Q: AI分析失败，提示"配置错误"**
A: 请检查API密钥是否正确配置，确保已开通阿里云百炼服务

**Q: 语音识别不准确**
A: 请在安静环境下录音，说话清晰，避免方言和口音过重

**Q: 图片识别失败**
A: 请确保图片清晰，文字可见，支持的图片格式为JPG、PNG

**Q: 网络请求超时**
A: 请检查网络连接，或稍后重试

### 技术支持

如果遇到其他问题，请：
1. 查看浏览器控制台的错误信息
2. 检查uniCloud云函数日志
3. 确认API密钥和网络配置

## 更新日志

### v1.0.0 (2025-07-03)
- ✅ 集成阿里云百炼AI模型
- ✅ 支持语音、图片、文字三种输入方式
- ✅ 智能分类和数据提取
- ✅ 置信度评分和结果确认
- ✅ 跨平台兼容（微信、抖音小程序）
