<view class="add-page data-v-e8d2fd40"><view class="type-tabs data-v-e8d2fd40"><view class="{{['type-tab', 'data-v-e8d2fd40', a && 'active']}}" bindtap="{{b}}"><text class="tab-text data-v-e8d2fd40">支出</text></view><view class="{{['type-tab', 'data-v-e8d2fd40', c && 'active']}}" bindtap="{{d}}"><text class="tab-text data-v-e8d2fd40">收入</text></view></view><view class="amount-section data-v-e8d2fd40"><view class="amount-display data-v-e8d2fd40"><text class="currency data-v-e8d2fd40">¥</text><input class="amount-input data-v-e8d2fd40" type="digit" value="{{e}}" bindinput="{{f}}" placeholder="0.00" focus="{{g}}"/></view></view><view class="category-section data-v-e8d2fd40"><view class="section-title data-v-e8d2fd40">选择分类</view><view class="category-grid data-v-e8d2fd40"><view wx:for="{{h}}" wx:for-item="category" wx:key="d" class="{{['category-item', 'data-v-e8d2fd40', category.e && 'active']}}" bindtap="{{category.f}}"><view class="category-icon data-v-e8d2fd40" style="{{'background-color:' + category.b}}">{{category.a}}</view><text class="category-name data-v-e8d2fd40">{{category.c}}</text></view></view></view><view class="datetime-section data-v-e8d2fd40"><view class="datetime-item data-v-e8d2fd40" bindtap="{{j}}"><text class="datetime-label data-v-e8d2fd40">日期</text><text class="datetime-value data-v-e8d2fd40">{{i}}</text></view><view class="datetime-item data-v-e8d2fd40" bindtap="{{l}}"><text class="datetime-label data-v-e8d2fd40">时间</text><text class="datetime-value data-v-e8d2fd40">{{k}}</text></view></view><view class="note-section data-v-e8d2fd40"><view class="section-title data-v-e8d2fd40">备注</view><block wx:if="{{r0}}"><textarea class="note-input data-v-e8d2fd40" placeholder="添加备注信息（可选）" maxlength="100" value="{{m}}" bindinput="{{n}}"/></block></view><view class="action-buttons data-v-e8d2fd40"><button class="btn btn-secondary data-v-e8d2fd40" bindtap="{{o}}">取消</button><button class="btn btn-primary data-v-e8d2fd40" bindtap="{{q}}" disabled="{{r}}">{{p}}</button></view><picker class="data-v-e8d2fd40" mode="date" value="{{s}}" bindchange="{{t}}" start="{{v}}" end="{{w}}" hidden="{{!false}}" ref="datePicker"><view class="data-v-e8d2fd40"></view></picker><picker class="data-v-e8d2fd40" mode="time" value="{{x}}" bindchange="{{y}}" hidden="{{!false}}" ref="timePicker"><view class="data-v-e8d2fd40"></view></picker></view>