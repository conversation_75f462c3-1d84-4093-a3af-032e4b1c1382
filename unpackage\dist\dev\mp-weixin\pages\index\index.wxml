<view class="home-page data-v-1cf27b2a"><view class="greeting-section data-v-1cf27b2a"><view class="greeting-content data-v-1cf27b2a"><text class="greeting-title data-v-1cf27b2a">{{a}}</text><text class="greeting-subtitle data-v-1cf27b2a">{{b}}</text></view><view class="profile-section data-v-1cf27b2a"><view class="{{['sync-status', 'data-v-1cf27b2a', d && 'syncing', e && 'offline']}}"><text class="sync-icon data-v-1cf27b2a">{{c}}</text></view><view wx:if="{{f}}" class="test-btn data-v-1cf27b2a" bindtap="{{g}}"><text class="test-text data-v-1cf27b2a">测试</text></view><view wx:if="{{h}}" class="test-page-btn data-v-1cf27b2a" bindtap="{{i}}"><text class="test-text data-v-1cf27b2a">测试页</text></view></view></view><view class="main-stats-card slide-in-down data-v-1cf27b2a"><view class="balance-section data-v-1cf27b2a"><view class="balance-header data-v-1cf27b2a"><text class="balance-label data-v-1cf27b2a">本月结余</text><view class="{{['trend-indicator', 'data-v-1cf27b2a', k]}}"><text class="trend-icon data-v-1cf27b2a">{{j}}</text></view></view><view class="balance-amount data-v-1cf27b2a"><text class="currency data-v-1cf27b2a">¥</text><text class="{{['amount', 'data-v-1cf27b2a', m && 'negative']}}">{{l}}</text></view><view wx:if="{{n}}" class="balance-change data-v-1cf27b2a"><text class="{{['change-text', 'data-v-1cf27b2a', q && 'positive', r && 'negative']}}">{{o}}{{p}}</text><text class="change-label data-v-1cf27b2a">较上月</text></view></view></view><view class="income-expense-cards data-v-1cf27b2a"><view class="stat-card income-card slide-in-left data-v-1cf27b2a"><view class="card-header data-v-1cf27b2a"><view class="card-icon income-icon data-v-1cf27b2a">📈</view><text class="card-title data-v-1cf27b2a">收入</text></view><view class="card-amount income data-v-1cf27b2a"><text class="amount-text data-v-1cf27b2a">{{s}}</text></view><view class="card-progress data-v-1cf27b2a"><view class="progress-bar data-v-1cf27b2a"><view class="progress-fill income-progress data-v-1cf27b2a" style="{{'width:' + t}}"></view></view><text class="progress-text data-v-1cf27b2a">{{v}}笔交易</text></view></view><view class="stat-card expense-card slide-in-right data-v-1cf27b2a"><view class="card-header data-v-1cf27b2a"><view class="card-icon expense-icon data-v-1cf27b2a">📉</view><text class="card-title data-v-1cf27b2a">支出</text></view><view class="card-amount expense data-v-1cf27b2a"><text class="amount-text data-v-1cf27b2a">{{w}}</text></view><view class="card-progress data-v-1cf27b2a"><view class="progress-bar data-v-1cf27b2a"><view class="progress-fill expense-progress data-v-1cf27b2a" style="{{'width:' + x}}"></view></view><text class="progress-text data-v-1cf27b2a">{{y}}笔交易</text></view></view></view><view class="quick-actions-section data-v-1cf27b2a"><view class="section-title data-v-1cf27b2a"><text class="title-text data-v-1cf27b2a">快速记账</text><text class="title-subtitle data-v-1cf27b2a">点击快速添加收支记录</text></view><view class="quick-actions bounce-in data-v-1cf27b2a"><view class="quick-btn expense-btn data-v-1cf27b2a" bindtap="{{z}}"><view class="btn-content data-v-1cf27b2a"><view class="btn-icon expense-icon data-v-1cf27b2a">💸</view><view class="btn-text data-v-1cf27b2a"><text class="btn-title data-v-1cf27b2a">记支出</text><text class="btn-subtitle data-v-1cf27b2a">日常消费</text></view></view><view class="btn-arrow data-v-1cf27b2a">→</view></view><view class="quick-btn income-btn data-v-1cf27b2a" bindtap="{{A}}"><view class="btn-content data-v-1cf27b2a"><view class="btn-icon income-icon data-v-1cf27b2a">💰</view><view class="btn-text data-v-1cf27b2a"><text class="btn-title data-v-1cf27b2a">记收入</text><text class="btn-subtitle data-v-1cf27b2a">工资奖金</text></view></view><view class="btn-arrow data-v-1cf27b2a">→</view></view></view></view><view class="recent-section fade-in-up data-v-1cf27b2a"><view class="section-header data-v-1cf27b2a"><view class="header-left data-v-1cf27b2a"><text class="section-title data-v-1cf27b2a">最近记录</text><text class="section-subtitle data-v-1cf27b2a">{{B}}条记录</text></view><view class="header-right data-v-1cf27b2a" bindtap="{{C}}"><text class="more-text data-v-1cf27b2a">查看全部</text><text class="more-arrow data-v-1cf27b2a">→</text></view></view><view wx:if="{{D}}" class="recent-list data-v-1cf27b2a"><view wx:for="{{E}}" wx:for-item="record" wx:key="k" class="record-item data-v-1cf27b2a" style="{{'animation-delay:' + record.l}}" bindtap="{{record.m}}"><view class="record-left data-v-1cf27b2a"><view class="record-icon-wrapper data-v-1cf27b2a"><view class="record-icon data-v-1cf27b2a" style="{{'background-color:' + record.b}}"><text class="icon-text data-v-1cf27b2a">{{record.a}}</text></view><view class="{{['type-indicator', 'data-v-1cf27b2a', record.c]}}"></view></view><view class="record-info data-v-1cf27b2a"><text class="record-category data-v-1cf27b2a">{{record.d}}</text><text wx:if="{{record.e}}" class="record-note data-v-1cf27b2a">{{record.f}}</text><text class="record-time data-v-1cf27b2a">{{record.g}}</text></view></view><view class="record-right data-v-1cf27b2a"><text class="{{['record-amount', 'data-v-1cf27b2a', record.j]}}">{{record.h}}{{record.i}}</text><view class="edit-indicator data-v-1cf27b2a"><text class="edit-icon data-v-1cf27b2a">✏️</text></view></view></view></view><view wx:else class="empty-state data-v-1cf27b2a"><view class="empty-icon data-v-1cf27b2a">📝</view><text class="empty-text data-v-1cf27b2a">暂无记录</text><text class="empty-desc data-v-1cf27b2a">点击下方按钮开始记账吧</text></view></view></view>