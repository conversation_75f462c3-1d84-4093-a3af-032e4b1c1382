{"version": 3, "file": "test.js", "sources": ["pages/test/test.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC90ZXN0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">启动测试页面</text>\n    </view>\n    \n    <view class=\"content\">\n      <view class=\"test-item\">\n        <text class=\"label\">基础功能测试:</text>\n        <text class=\"status\" :class=\"{ success: basicTest }\">{{ basicTest ? '✅ 正常' : '❌ 异常' }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">云函数连接:</text>\n        <text class=\"status\" :class=\"{ success: cloudTest }\">{{ cloudTestStatus }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">存储功能:</text>\n        <text class=\"status\" :class=\"{ success: storageTest }\">{{ storageTest ? '✅ 正常' : '❌ 异常' }}</text>\n      </view>\n    </view>\n    \n    <view class=\"actions\">\n      <button @click=\"runTests\" class=\"test-btn\">运行测试</button>\n      <button @click=\"goHome\" class=\"home-btn\">返回首页</button>\n    </view>\n    \n    <view class=\"logs\">\n      <text class=\"log-title\">测试日志:</text>\n      <view v-for=\"(log, index) in logs\" :key=\"index\" class=\"log-item\">\n        <text>{{ log }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      basicTest: false,\n      cloudTest: false,\n      storageTest: false,\n      cloudTestStatus: '未测试',\n      logs: []\n    }\n  },\n  \n  onLoad() {\n    this.addLog('测试页面加载完成');\n    this.runBasicTests();\n  },\n  \n  methods: {\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString();\n      this.logs.push(`[${timestamp}] ${message}`);\n    },\n    \n    runBasicTests() {\n      try {\n        // 测试基础JavaScript功能\n        const testObj = { test: true };\n        const testArray = [1, 2, 3];\n        const testString = `测试字符串 ${Date.now()}`;\n        \n        this.basicTest = true;\n        this.addLog('✅ 基础功能测试通过');\n        \n        // 测试存储功能\n        uni.setStorageSync('test_key', 'test_value');\n        const stored = uni.getStorageSync('test_key');\n        this.storageTest = stored === 'test_value';\n        this.addLog(this.storageTest ? '✅ 存储功能测试通过' : '❌ 存储功能测试失败');\n        \n      } catch (error) {\n        this.addLog(`❌ 基础测试失败: ${error.message}`);\n      }\n    },\n    \n    async runTests() {\n      this.addLog('开始运行完整测试...');\n      \n      // 测试云函数连接\n      try {\n        this.cloudTestStatus = '测试中...';\n        this.addLog('正在测试云函数连接...');\n        \n        const result = await uniCloud.callFunction({\n          name: 'test',\n          data: { message: 'hello from test page' }\n        });\n        \n        if (result.result && result.result.code === 200) {\n          this.cloudTest = true;\n          this.cloudTestStatus = '✅ 连接正常';\n          this.addLog('✅ 云函数连接测试通过');\n        } else {\n          this.cloudTestStatus = '❌ 连接失败';\n          this.addLog('❌ 云函数返回异常');\n        }\n      } catch (error) {\n        this.cloudTest = false;\n        this.cloudTestStatus = '❌ 连接失败';\n        this.addLog(`❌ 云函数连接失败: ${error.message}`);\n      }\n      \n      this.addLog('测试完成');\n    },\n    \n    goHome() {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.content {\n  background-color: white;\n  border-radius: 10px;\n  padding: 20px;\n  margin-bottom: 20px;\n}\n\n.test-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.test-item:last-child {\n  border-bottom: none;\n}\n\n.label {\n  font-size: 16px;\n  color: #333;\n}\n\n.status {\n  font-size: 14px;\n  color: #999;\n}\n\n.status.success {\n  color: #4CAF50;\n}\n\n.actions {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.test-btn, .home-btn {\n  flex: 1;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n}\n\n.test-btn {\n  background-color: #4CAF50;\n  color: white;\n}\n\n.home-btn {\n  background-color: #2196F3;\n  color: white;\n}\n\n.logs {\n  background-color: white;\n  border-radius: 10px;\n  padding: 20px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.log-item {\n  padding: 5px 0;\n  border-bottom: 1px solid #f0f0f0;\n  font-size: 14px;\n  color: #666;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/test/test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "uniCloud"], "mappings": ";;AAsCA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,MAAM,CAAC;AAAA,IACT;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,OAAO,UAAU;AACtB,SAAK,cAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAAA,IACP,OAAO,SAAS;AACd,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAkB;AAC/C,WAAK,KAAK,KAAK,IAAI,SAAS,KAAK,OAAO,EAAE;AAAA,IAC3C;AAAA,IAED,gBAAgB;AACd,UAAI;AAEF,cAAM,UAAU,EAAE,MAAM;AACxB,cAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,cAAM,aAAa,SAAS,KAAK,IAAG,CAAE;AAEtC,aAAK,YAAY;AACjB,aAAK,OAAO,YAAY;AAGxBA,sBAAAA,MAAI,eAAe,YAAY,YAAY;AAC3C,cAAM,SAASA,cAAAA,MAAI,eAAe,UAAU;AAC5C,aAAK,cAAc,WAAW;AAC9B,aAAK,OAAO,KAAK,cAAc,eAAe,YAAY;AAAA,MAE1D,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AAAA,MAC1C;AAAA,IACD;AAAA,IAED,MAAM,WAAW;AACf,WAAK,OAAO,aAAa;AAGzB,UAAI;AACF,aAAK,kBAAkB;AACvB,aAAK,OAAO,cAAc;AAE1B,cAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM,EAAE,SAAS,uBAAuB;AAAA,QAC1C,CAAC;AAED,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAC/C,eAAK,YAAY;AACjB,eAAK,kBAAkB;AACvB,eAAK,OAAO,aAAa;AAAA,eACpB;AACL,eAAK,kBAAkB;AACvB,eAAK,OAAO,WAAW;AAAA,QACzB;AAAA,MACA,SAAO,OAAO;AACd,aAAK,YAAY;AACjB,aAAK,kBAAkB;AACvB,aAAK,OAAO,cAAc,MAAM,OAAO,EAAE;AAAA,MAC3C;AAEA,WAAK,OAAO,MAAM;AAAA,IACnB;AAAA,IAED,SAAS;AACPD,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;ACpHA,GAAG,WAAW,eAAe;"}