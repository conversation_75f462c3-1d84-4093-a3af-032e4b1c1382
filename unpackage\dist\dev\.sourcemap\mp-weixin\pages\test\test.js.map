{"version": 3, "file": "test.js", "sources": ["pages/test/test.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC90ZXN0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">启动测试页面</text>\n    </view>\n    \n    <view class=\"content\">\n      <view class=\"test-item\">\n        <text class=\"label\">基础功能测试:</text>\n        <text class=\"status\" :class=\"{ success: basicTest }\">{{ basicTest ? '✅ 正常' : '❌ 异常' }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">云函数连接:</text>\n        <text class=\"status\" :class=\"{ success: cloudTest }\">{{ cloudTestStatus }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">存储功能:</text>\n        <text class=\"status\" :class=\"{ success: storageTest }\">{{ storageTest ? '✅ 正常' : '❌ 异常' }}</text>\n      </view>\n    </view>\n    \n    <view class=\"actions\">\n      <button @click=\"runTests\" class=\"test-btn\">运行测试</button>\n      <button @click=\"runDiagnosis\" class=\"diagnosis-btn\">完整诊断</button>\n      <button @click=\"goHome\" class=\"home-btn\">返回首页</button>\n    </view>\n    \n    <view class=\"logs\">\n      <text class=\"log-title\">测试日志:</text>\n      <view v-for=\"(log, index) in logs\" :key=\"index\" class=\"log-item\">\n        <text>{{ log }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport CloudDebug from '../../utils/cloud-debug.js'\n\nexport default {\n  data() {\n    return {\n      basicTest: false,\n      cloudTest: false,\n      storageTest: false,\n      cloudTestStatus: '未测试',\n      logs: []\n    }\n  },\n  \n  onLoad() {\n    this.addLog('测试页面加载完成');\n    this.runBasicTests();\n  },\n  \n  methods: {\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString();\n      this.logs.push(`[${timestamp}] ${message}`);\n    },\n    \n    runBasicTests() {\n      try {\n        // 测试基础JavaScript功能\n        const testObj = { test: true };\n        const testArray = [1, 2, 3];\n        const testString = `测试字符串 ${Date.now()}`;\n        \n        this.basicTest = true;\n        this.addLog('✅ 基础功能测试通过');\n        \n        // 测试存储功能\n        uni.setStorageSync('test_key', 'test_value');\n        const stored = uni.getStorageSync('test_key');\n        this.storageTest = stored === 'test_value';\n        this.addLog(this.storageTest ? '✅ 存储功能测试通过' : '❌ 存储功能测试失败');\n        \n      } catch (error) {\n        this.addLog(`❌ 基础测试失败: ${error.message}`);\n      }\n    },\n    \n    async runTests() {\n      this.addLog('开始运行完整测试...');\n\n      // 1. 测试基础云函数连接\n      await this.testBasicCloudFunction();\n\n      // 2. 测试 bookkeeping 云函数\n      await this.testBookkeepingFunction();\n\n      // 3. 测试数据库初始化\n      await this.testDatabaseInit();\n\n      // 4. 测试添加记录\n      await this.testAddRecord();\n\n      this.addLog('所有测试完成');\n    },\n\n    async testBasicCloudFunction() {\n      try {\n        this.cloudTestStatus = '测试基础连接...';\n        this.addLog('正在测试基础云函数连接...');\n\n        const result = await uniCloud.callFunction({\n          name: 'test',\n          data: { message: 'hello from test page' }\n        });\n\n        if (result.result && result.result.code === 200) {\n          this.cloudTest = true;\n          this.cloudTestStatus = '✅ 基础连接正常';\n          this.addLog('✅ 基础云函数连接测试通过');\n        } else {\n          this.cloudTestStatus = '❌ 基础连接失败';\n          this.addLog('❌ 基础云函数返回异常:', JSON.stringify(result));\n        }\n      } catch (error) {\n        this.cloudTest = false;\n        this.cloudTestStatus = '❌ 基础连接失败';\n        this.addLog(`❌ 基础云函数连接失败: ${error.message}`);\n      }\n    },\n\n    async testBookkeepingFunction() {\n      try {\n        this.addLog('正在测试 bookkeeping 云函数...');\n\n        const result = await uniCloud.callFunction({\n          name: 'bookkeeping',\n          data: {\n            action: 'getCategories',\n            data: {}\n          }\n        });\n\n        this.addLog('bookkeeping 云函数响应:', JSON.stringify(result));\n\n        if (result.result && result.result.code === 200) {\n          this.addLog('✅ bookkeeping 云函数测试通过');\n        } else {\n          this.addLog('❌ bookkeeping 云函数返回异常');\n        }\n      } catch (error) {\n        this.addLog(`❌ bookkeeping 云函数测试失败: ${error.message}`);\n      }\n    },\n\n    async testDatabaseInit() {\n      try {\n        this.addLog('正在测试数据库初始化...');\n\n        const result = await uniCloud.callFunction({\n          name: 'init-db',\n          data: {}\n        });\n\n        this.addLog('数据库初始化响应:', JSON.stringify(result));\n\n        if (result.result && result.result.code === 200) {\n          this.addLog('✅ 数据库初始化测试通过');\n        } else {\n          this.addLog('❌ 数据库初始化失败');\n        }\n      } catch (error) {\n        this.addLog(`❌ 数据库初始化测试失败: ${error.message}`);\n      }\n    },\n\n    async testAddRecord() {\n      try {\n        this.addLog('正在测试添加记录...');\n\n        const testRecord = {\n          type: 'expense',\n          amount: 10.5,\n          categoryId: 'cat1',\n          categoryName: '餐饮',\n          categoryIcon: '🍽️',\n          categoryColor: '#FF9800',\n          note: '测试记录',\n          date: new Date().toISOString().split('T')[0],\n          time: new Date().toTimeString().split(' ')[0].substring(0, 5)\n        };\n\n        const result = await uniCloud.callFunction({\n          name: 'bookkeeping',\n          data: {\n            action: 'addRecord',\n            data: testRecord\n          }\n        });\n\n        this.addLog('添加记录响应:', JSON.stringify(result));\n\n        if (result.result && result.result.code === 200) {\n          this.addLog('✅ 添加记录测试通过');\n        } else {\n          this.addLog('❌ 添加记录失败');\n        }\n      } catch (error) {\n        this.addLog(`❌ 添加记录测试失败: ${error.message}`);\n      }\n    },\n\n    async runDiagnosis() {\n      this.addLog('开始完整诊断...');\n      this.logs = []; // 清空之前的日志\n\n      try {\n        const results = await CloudDebug.runFullDiagnosis();\n        const report = CloudDebug.generateReport(results);\n\n        // 将报告按行添加到日志中\n        const reportLines = report.split('\\n');\n        reportLines.forEach(line => {\n          if (line.trim()) {\n            this.addLog(line);\n          }\n        });\n\n        // 更新测试状态\n        this.cloudTest = results.basicConnection?.success || false;\n        this.cloudTestStatus = results.addRecord?.success ? '✅ 完全正常' : '❌ 存在问题';\n\n      } catch (error) {\n        this.addLog(`❌ 诊断过程失败: ${error.message}`);\n      }\n    },\n\n    goHome() {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 20px;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.title {\n  font-size: 24px;\n  font-weight: bold;\n  color: #333;\n}\n\n.content {\n  background-color: white;\n  border-radius: 10px;\n  padding: 20px;\n  margin-bottom: 20px;\n}\n\n.test-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #eee;\n}\n\n.test-item:last-child {\n  border-bottom: none;\n}\n\n.label {\n  font-size: 16px;\n  color: #333;\n}\n\n.status {\n  font-size: 14px;\n  color: #999;\n}\n\n.status.success {\n  color: #4CAF50;\n}\n\n.actions {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 20px;\n}\n\n.test-btn, .diagnosis-btn, .home-btn {\n  flex: 1;\n  padding: 15px;\n  border-radius: 8px;\n  font-size: 16px;\n  margin: 0 5px;\n}\n\n.test-btn {\n  background-color: #4CAF50;\n  color: white;\n}\n\n.diagnosis-btn {\n  background-color: #FF9800;\n  color: white;\n}\n\n.home-btn {\n  background-color: #2196F3;\n  color: white;\n}\n\n.logs {\n  background-color: white;\n  border-radius: 10px;\n  padding: 20px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n  display: block;\n}\n\n.log-item {\n  padding: 5px 0;\n  border-bottom: 1px solid #f0f0f0;\n  font-size: 14px;\n  color: #666;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/test/test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "uniCloud", "CloudDebug"], "mappings": ";;;AAyCA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,aAAa;AAAA,MACb,iBAAiB;AAAA,MACjB,MAAM,CAAC;AAAA,IACT;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,OAAO,UAAU;AACtB,SAAK,cAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAAA,IACP,OAAO,SAAS;AACd,YAAM,aAAY,oBAAI,KAAM,GAAC,mBAAkB;AAC/C,WAAK,KAAK,KAAK,IAAI,SAAS,KAAK,OAAO,EAAE;AAAA,IAC3C;AAAA,IAED,gBAAgB;AACd,UAAI;AAEF,cAAM,UAAU,EAAE,MAAM;AACxB,cAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AAC1B,cAAM,aAAa,SAAS,KAAK,IAAG,CAAE;AAEtC,aAAK,YAAY;AACjB,aAAK,OAAO,YAAY;AAGxBA,sBAAAA,MAAI,eAAe,YAAY,YAAY;AAC3C,cAAM,SAASA,cAAAA,MAAI,eAAe,UAAU;AAC5C,aAAK,cAAc,WAAW;AAC9B,aAAK,OAAO,KAAK,cAAc,eAAe,YAAY;AAAA,MAE1D,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AAAA,MAC1C;AAAA,IACD;AAAA,IAED,MAAM,WAAW;AACf,WAAK,OAAO,aAAa;AAGzB,YAAM,KAAK;AAGX,YAAM,KAAK;AAGX,YAAM,KAAK;AAGX,YAAM,KAAK;AAEX,WAAK,OAAO,QAAQ;AAAA,IACrB;AAAA,IAED,MAAM,yBAAyB;AAC7B,UAAI;AACF,aAAK,kBAAkB;AACvB,aAAK,OAAO,gBAAgB;AAE5B,cAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM,EAAE,SAAS,uBAAuB;AAAA,QAC1C,CAAC;AAED,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAC/C,eAAK,YAAY;AACjB,eAAK,kBAAkB;AACvB,eAAK,OAAO,eAAe;AAAA,eACtB;AACL,eAAK,kBAAkB;AACvB,eAAK,OAAO,gBAAgB,KAAK,UAAU,MAAM,CAAC;AAAA,QACpD;AAAA,MACA,SAAO,OAAO;AACd,aAAK,YAAY;AACjB,aAAK,kBAAkB;AACvB,aAAK,OAAO,gBAAgB,MAAM,OAAO,EAAE;AAAA,MAC7C;AAAA,IACD;AAAA,IAED,MAAM,0BAA0B;AAC9B,UAAI;AACF,aAAK,OAAO,yBAAyB;AAErC,cAAM,SAAS,MAAMA,cAAQ,GAAC,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,QAAQ;AAAA,YACR,MAAM,CAAC;AAAA,UACT;AAAA,QACF,CAAC;AAED,aAAK,OAAO,sBAAsB,KAAK,UAAU,MAAM,CAAC;AAExD,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAC/C,eAAK,OAAO,uBAAuB;AAAA,eAC9B;AACL,eAAK,OAAO,uBAAuB;AAAA,QACrC;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,0BAA0B,MAAM,OAAO,EAAE;AAAA,MACvD;AAAA,IACD;AAAA,IAED,MAAM,mBAAmB;AACvB,UAAI;AACF,aAAK,OAAO,eAAe;AAE3B,cAAM,SAAS,MAAMA,cAAQ,GAAC,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACT,CAAC;AAED,aAAK,OAAO,aAAa,KAAK,UAAU,MAAM,CAAC;AAE/C,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAC/C,eAAK,OAAO,cAAc;AAAA,eACrB;AACL,eAAK,OAAO,YAAY;AAAA,QAC1B;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,iBAAiB,MAAM,OAAO,EAAE;AAAA,MAC9C;AAAA,IACD;AAAA,IAED,MAAM,gBAAgB;AACpB,UAAI;AACF,aAAK,OAAO,aAAa;AAEzB,cAAM,aAAa;AAAA,UACjB,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,cAAc;AAAA,UACd,eAAe;AAAA,UACf,MAAM;AAAA,UACN,OAAM,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,UAC3C,OAAM,oBAAI,QAAO,aAAc,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA;AAG9D,cAAM,SAAS,MAAMA,cAAQ,GAAC,aAAa;AAAA,UACzC,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,QAAQ;AAAA,YACR,MAAM;AAAA,UACR;AAAA,QACF,CAAC;AAED,aAAK,OAAO,WAAW,KAAK,UAAU,MAAM,CAAC;AAE7C,YAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK;AAC/C,eAAK,OAAO,YAAY;AAAA,eACnB;AACL,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,MACA,SAAO,OAAO;AACd,aAAK,OAAO,eAAe,MAAM,OAAO,EAAE;AAAA,MAC5C;AAAA,IACD;AAAA,IAED,MAAM,eAAe;;AACnB,WAAK,OAAO,WAAW;AACvB,WAAK,OAAO;AAEZ,UAAI;AACF,cAAM,UAAU,MAAMC,4BAAW;AACjC,cAAM,SAASA,iBAAAA,WAAW,eAAe,OAAO;AAGhD,cAAM,cAAc,OAAO,MAAM,IAAI;AACrC,oBAAY,QAAQ,UAAQ;AAC1B,cAAI,KAAK,QAAQ;AACf,iBAAK,OAAO,IAAI;AAAA,UAClB;AAAA,QACF,CAAC;AAGD,aAAK,cAAY,aAAQ,oBAAR,mBAAyB,YAAW;AACrD,aAAK,oBAAkB,aAAQ,cAAR,mBAAmB,WAAU,WAAW;AAAA,MAE/D,SAAO,OAAO;AACd,aAAK,OAAO,aAAa,MAAM,OAAO,EAAE;AAAA,MAC1C;AAAA,IACD;AAAA,IAED,SAAS;AACPF,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MACP,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;AC9OA,GAAG,WAAW,eAAe;"}