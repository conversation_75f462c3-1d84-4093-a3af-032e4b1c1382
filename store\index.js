/**
 * 状态管理 - 使用Vue3的reactive实现简单状态管理
 * 支持云端存储和本地存储双模式
 */
import { reactive, computed } from 'vue'
import Storage from '../utils/storage.js'
import CloudStorage from '../utils/cloud-storage.js'
import { Record, Statistics } from '../utils/models.js'
import { showToast } from '../utils/helpers.js'

// 创建响应式状态
const state = reactive({
  // 记账记录
  records: [],
  // 分类数据
  categories: {
    expense: [],
    income: []
  },
  // 用户设置
  settings: {},
  // 当前选中的日期范围
  dateRange: {
    start: '',
    end: ''
  },
  // 加载状态
  loading: false,
  // 云端同步状态
  syncing: false,
  // 是否使用云端存储
  useCloudStorage: true,
  // 最后同步时间
  lastSyncTime: null,
  // 网络状态
  isOnline: true
})

// 计算属性
const getters = {
  // 获取所有记录
  allRecords: computed(() => state.records),
  
  // 获取支出记录
  expenseRecords: computed(() => 
    state.records.filter(record => record.type === 'expense')
  ),
  
  // 获取收入记录
  incomeRecords: computed(() => 
    state.records.filter(record => record.type === 'income')
  ),
  
  // 获取支出分类
  expenseCategories: computed(() => state.categories.expense),
  
  // 获取收入分类
  incomeCategories: computed(() => state.categories.income),
  
  // 获取统计数据
  statistics: computed(() => new Statistics(state.records)),
  
  // 获取总收入
  totalIncome: computed(() => 
    state.records
      .filter(record => record.type === 'income')
      .reduce((total, record) => total + record.amount, 0)
  ),
  
  // 获取总支出
  totalExpense: computed(() => 
    state.records
      .filter(record => record.type === 'expense')
      .reduce((total, record) => total + record.amount, 0)
  ),
  
  // 获取净收入
  netIncome: computed(() => getters.totalIncome.value - getters.totalExpense.value),
  
  // 根据日期范围过滤记录
  filteredRecords: computed(() => {
    if (!state.dateRange.start || !state.dateRange.end) {
      return state.records
    }
    return state.records.filter(record => 
      record.date >= state.dateRange.start && record.date <= state.dateRange.end
    )
  })
}

// 操作方法
const actions = {
  // 初始化数据
  async init() {
    try {
      // 检查网络状态
      state.isOnline = await CloudStorage.isOnline()

      if (state.useCloudStorage && state.isOnline) {
        // 云端模式
        await this.initCloudMode()
      } else {
        // 本地模式
        await this.initLocalMode()
      }
    } catch (error) {
      console.error('初始化失败:', error)
      // 降级到本地模式
      await this.initLocalMode()
    }
  },

  // 初始化云端模式
  async initCloudMode() {
    try {
      state.loading = true

      // 初始化云端存储
      await CloudStorage.init()

      // 处理离线数据
      await CloudStorage.handleOfflineData()

      // 加载云端数据
      await this.loadCloudData()

      // 更新最后同步时间
      state.lastSyncTime = new Date()
      uni.setStorageSync('lastSyncTime', state.lastSyncTime)

    } catch (error) {
      console.error('云端模式初始化失败:', error)
      throw error
    } finally {
      state.loading = false
    }
  },

  // 初始化本地模式
  async initLocalMode() {
    try {
      state.loading = true
      state.useCloudStorage = false

      this.loadRecords()
      this.loadCategories()
      this.loadSettings()

    } finally {
      state.loading = false
    }
  },

  // 加载云端数据
  async loadCloudData() {
    try {
      // 并行加载数据
      const [recordsResult, categoriesResult, settingsResult] = await Promise.all([
        CloudStorage.getRecords(),
        CloudStorage.getCategories(),
        CloudStorage.getUserSettings()
      ])

      // 处理记录数据
      if (recordsResult.success) {
        state.records = recordsResult.data.records.map(record => new Record(record))
      }

      // 处理分类数据
      if (categoriesResult.success) {
        const categories = categoriesResult.data
        state.categories = {
          expense: categories.filter(cat => cat.type === 'expense'),
          income: categories.filter(cat => cat.type === 'income')
        }
      }

      // 处理设置数据
      if (settingsResult.success) {
        state.settings = settingsResult.data || CloudStorage.getDefaultSettings()
      }

    } catch (error) {
      console.error('加载云端数据失败:', error)
      throw error
    }
  },
  
  // 加载记账记录
  loadRecords() {
    try {
      const records = Storage.getRecords()
      state.records = records.map(record => new Record(record))
    } catch (error) {
      console.error('加载记录失败:', error)
      showToast('加载记录失败')
    }
  },
  
  // 保存记账记录
  saveRecords() {
    try {
      const records = state.records.map(record => record.toObject())
      Storage.setRecords(records)
      return true
    } catch (error) {
      console.error('保存记录失败:', error)
      showToast('保存记录失败')
      return false
    }
  },
  
  // 添加记录
  async addRecord(recordData) {
    try {
      const record = new Record(recordData)

      if (state.useCloudStorage && state.isOnline) {
        // 云端模式
        const result = await CloudStorage.addRecord(record.toObject())
        if (result.success) {
          // 更新本地状态
          record.id = result.data.id || record.id
          state.records.unshift(record)
          showToast('记录添加成功', 'success')
          return record
        } else {
          throw new Error(result.error)
        }
      } else {
        // 本地模式或离线模式
        state.records.unshift(record)
        this.saveRecords()

        // 如果是离线状态，保存到离线队列
        if (state.useCloudStorage && !state.isOnline) {
          CloudStorage.saveOfflineRecord(record.toObject(), 'add')
          showToast('记录已保存，将在联网后同步', 'success')
        } else {
          showToast('记录添加成功', 'success')
        }
        return record
      }
    } catch (error) {
      console.error('添加记录失败:', error)
      showToast('添加记录失败')
      return null
    }
  },
  
  // 更新记录
  async updateRecord(id, recordData) {
    try {
      const index = state.records.findIndex(record => record.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }

      if (state.useCloudStorage && state.isOnline) {
        // 云端模式
        const result = await CloudStorage.updateRecord(id, recordData)
        if (result.success) {
          state.records[index].update(recordData)
          showToast('记录更新成功', 'success')
          return state.records[index]
        } else {
          throw new Error(result.error)
        }
      } else {
        // 本地模式或离线模式
        state.records[index].update(recordData)
        this.saveRecords()

        // 如果是离线状态，保存到离线队列
        if (state.useCloudStorage && !state.isOnline) {
          CloudStorage.saveOfflineRecord({ id, ...recordData }, 'update')
          showToast('记录已更新，将在联网后同步', 'success')
        } else {
          showToast('记录更新成功', 'success')
        }
        return state.records[index]
      }
    } catch (error) {
      console.error('更新记录失败:', error)
      showToast('更新记录失败')
      return null
    }
  },
  
  // 删除记录
  async deleteRecord(id) {
    try {
      const index = state.records.findIndex(record => record.id === id)
      if (index === -1) {
        throw new Error('记录不存在')
      }

      if (state.useCloudStorage && state.isOnline) {
        // 云端模式
        const result = await CloudStorage.deleteRecord(id)
        if (result.success) {
          state.records.splice(index, 1)
          showToast('记录删除成功', 'success')
          return true
        } else {
          throw new Error(result.error)
        }
      } else {
        // 本地模式或离线模式
        const deletedRecord = state.records[index]
        state.records.splice(index, 1)
        this.saveRecords()

        // 如果是离线状态，保存到离线队列
        if (state.useCloudStorage && !state.isOnline) {
          CloudStorage.saveOfflineRecord({ id }, 'delete')
          showToast('记录已删除，将在联网后同步', 'success')
        } else {
          showToast('记录删除成功', 'success')
        }
        return true
      }
    } catch (error) {
      console.error('删除记录失败:', error)
      showToast('删除记录失败')
      return false
    }
  },
  
  // 根据ID获取记录
  getRecordById(id) {
    return state.records.find(record => record.id === id)
  },
  
  // 加载分类数据
  loadCategories() {
    try {
      const categories = Storage.getCategories()
      state.categories = categories
    } catch (error) {
      console.error('加载分类失败:', error)
      showToast('加载分类失败')
    }
  },
  
  // 保存分类数据
  saveCategories() {
    try {
      Storage.setCategories(state.categories)
      return true
    } catch (error) {
      console.error('保存分类失败:', error)
      showToast('保存分类失败')
      return false
    }
  },
  
  // 根据ID获取分类
  getCategoryById(id, type = 'expense') {
    return state.categories[type].find(category => category.id === id)
  },

  // 获取默认分类
  getDefaultCategories() {
    return Storage.getDefaultCategories()
  },
  
  // 加载设置
  loadSettings() {
    try {
      state.settings = Storage.getSettings()
    } catch (error) {
      console.error('加载设置失败:', error)
      showToast('加载设置失败')
    }
  },
  
  // 保存设置
  saveSettings() {
    try {
      Storage.setSettings(state.settings)
      showToast('设置保存成功', 'success')
      return true
    } catch (error) {
      console.error('保存设置失败:', error)
      showToast('保存设置失败')
      return false
    }
  },
  
  // 更新设置
  updateSettings(newSettings) {
    Object.assign(state.settings, newSettings)
    this.saveSettings()
  },
  
  // 设置日期范围
  setDateRange(start, end) {
    state.dateRange.start = start
    state.dateRange.end = end
  },
  
  // 清空日期范围
  clearDateRange() {
    state.dateRange.start = ''
    state.dateRange.end = ''
  },
  
  // 设置加载状态
  setLoading(loading) {
    state.loading = loading
  },

  // 云端同步
  async syncWithCloud() {
    if (!state.useCloudStorage) {
      return { success: false, message: '未启用云端存储' }
    }

    try {
      state.syncing = true

      // 检查网络状态
      state.isOnline = await CloudStorage.isOnline()
      if (!state.isOnline) {
        throw new Error('网络连接不可用')
      }

      // 处理离线数据
      const offlineResult = await CloudStorage.handleOfflineData()

      // 同步云端数据
      const syncResult = await CloudStorage.syncData(state.lastSyncTime)
      if (syncResult.success) {
        // 更新本地数据
        await this.loadCloudData()

        // 更新同步时间
        state.lastSyncTime = new Date()
        uni.setStorageSync('lastSyncTime', state.lastSyncTime)

        showToast('数据同步成功', 'success')
        return { success: true, message: '数据同步成功' }
      } else {
        throw new Error(syncResult.error)
      }
    } catch (error) {
      console.error('数据同步失败:', error)
      showToast('数据同步失败')
      return { success: false, message: error.message }
    } finally {
      state.syncing = false
    }
  },

  // 切换存储模式
  async switchStorageMode(useCloud = true) {
    try {
      state.loading = true

      if (useCloud && !state.useCloudStorage) {
        // 切换到云端模式
        state.useCloudStorage = true
        await this.initCloudMode()
        showToast('已切换到云端存储', 'success')
      } else if (!useCloud && state.useCloudStorage) {
        // 切换到本地模式
        state.useCloudStorage = false
        await this.initLocalMode()
        showToast('已切换到本地存储', 'success')
      }

      // 保存设置
      uni.setStorageSync('useCloudStorage', state.useCloudStorage)

    } catch (error) {
      console.error('切换存储模式失败:', error)
      showToast('切换存储模式失败')
    } finally {
      state.loading = false
    }
  },

  // 导出数据
  async exportData() {
    try {
      if (state.useCloudStorage && state.isOnline) {
        const result = await CloudStorage.exportData()
        if (result.success) {
          return result.data
        } else {
          throw new Error(result.error)
        }
      } else {
        // 本地导出
        return {
          records: state.records.map(record => record.toObject()),
          categories: state.categories,
          settings: state.settings,
          exportTime: new Date(),
          version: '1.0.0'
        }
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      showToast('导出数据失败')
      return null
    }
  },

  // 导入数据
  async importData(data) {
    try {
      if (state.useCloudStorage && state.isOnline) {
        const result = await CloudStorage.importData(data)
        if (result.success) {
          // 重新加载数据
          await this.loadCloudData()
          showToast('数据导入成功', 'success')
          return true
        } else {
          throw new Error(result.error)
        }
      } else {
        // 本地导入
        if (data.records) {
          state.records = data.records.map(record => new Record(record))
        }
        if (data.categories) {
          state.categories = data.categories
        }
        if (data.settings) {
          state.settings = data.settings
        }

        // 保存到本地
        this.saveRecords()
        this.saveCategories()
        this.saveSettings()

        showToast('数据导入成功', 'success')
        return true
      }
    } catch (error) {
      console.error('导入数据失败:', error)
      showToast('导入数据失败')
      return false
    }
  },

  // 清空所有数据
  async clearAllData() {
    try {
      // 清空本地状态
      state.records = []
      state.categories = { expense: [], income: [] }
      state.settings = {}

      if (state.useCloudStorage && state.isOnline) {
        // 云端清空（需要逐个删除）
        const recordsResult = await CloudStorage.getRecords()
        if (recordsResult.success) {
          for (const record of recordsResult.data.records) {
            await CloudStorage.deleteRecord(record._id)
          }
        }
      } else {
        // 本地清空
        uni.removeStorageSync('bookkeeping_records')
        uni.removeStorageSync('bookkeeping_categories')
        uni.removeStorageSync('bookkeeping_settings')
      }

      showToast('数据清空成功', 'success')
      return true
    } catch (error) {
      console.error('清空数据失败:', error)
      showToast('清空数据失败')
      return false
    }
  }
}

// 创建store实例
const store = {
  state,
  getters,
  actions
}

export default store
