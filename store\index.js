/**
 * 状态管理 - 使用Vue3的reactive实现简单状态管理
 */
import { reactive, computed } from 'vue'
import Storage from '../utils/storage.js'
import { Record, Statistics } from '../utils/models.js'
import { showToast } from '../utils/helpers.js'

// 创建响应式状态
const state = reactive({
  // 记账记录
  records: [],
  // 分类数据
  categories: {
    expense: [],
    income: []
  },
  // 用户设置
  settings: {},
  // 当前选中的日期范围
  dateRange: {
    start: '',
    end: ''
  },
  // 加载状态
  loading: false
})

// 计算属性
const getters = {
  // 获取所有记录
  allRecords: computed(() => state.records),
  
  // 获取支出记录
  expenseRecords: computed(() => 
    state.records.filter(record => record.type === 'expense')
  ),
  
  // 获取收入记录
  incomeRecords: computed(() => 
    state.records.filter(record => record.type === 'income')
  ),
  
  // 获取支出分类
  expenseCategories: computed(() => state.categories.expense),
  
  // 获取收入分类
  incomeCategories: computed(() => state.categories.income),
  
  // 获取统计数据
  statistics: computed(() => new Statistics(state.records)),
  
  // 获取总收入
  totalIncome: computed(() => 
    state.records
      .filter(record => record.type === 'income')
      .reduce((total, record) => total + record.amount, 0)
  ),
  
  // 获取总支出
  totalExpense: computed(() => 
    state.records
      .filter(record => record.type === 'expense')
      .reduce((total, record) => total + record.amount, 0)
  ),
  
  // 获取净收入
  netIncome: computed(() => getters.totalIncome.value - getters.totalExpense.value),
  
  // 根据日期范围过滤记录
  filteredRecords: computed(() => {
    if (!state.dateRange.start || !state.dateRange.end) {
      return state.records
    }
    return state.records.filter(record => 
      record.date >= state.dateRange.start && record.date <= state.dateRange.end
    )
  })
}

// 操作方法
const actions = {
  // 初始化数据
  init() {
    this.loadRecords()
    this.loadCategories()
    this.loadSettings()
  },
  
  // 加载记账记录
  loadRecords() {
    try {
      const records = Storage.getRecords()
      state.records = records.map(record => new Record(record))
    } catch (error) {
      console.error('加载记录失败:', error)
      showToast('加载记录失败')
    }
  },
  
  // 保存记账记录
  saveRecords() {
    try {
      const records = state.records.map(record => record.toObject())
      Storage.setRecords(records)
      return true
    } catch (error) {
      console.error('保存记录失败:', error)
      showToast('保存记录失败')
      return false
    }
  },
  
  // 添加记录
  addRecord(recordData) {
    try {
      const record = new Record(recordData)
      state.records.unshift(record)
      this.saveRecords()
      showToast('记录添加成功', 'success')
      return record
    } catch (error) {
      console.error('添加记录失败:', error)
      showToast('添加记录失败')
      return null
    }
  },
  
  // 更新记录
  updateRecord(id, recordData) {
    try {
      const index = state.records.findIndex(record => record.id === id)
      if (index !== -1) {
        state.records[index].update(recordData)
        this.saveRecords()
        showToast('记录更新成功', 'success')
        return state.records[index]
      }
      throw new Error('记录不存在')
    } catch (error) {
      console.error('更新记录失败:', error)
      showToast('更新记录失败')
      return null
    }
  },
  
  // 删除记录
  deleteRecord(id) {
    try {
      const index = state.records.findIndex(record => record.id === id)
      if (index !== -1) {
        state.records.splice(index, 1)
        this.saveRecords()
        showToast('记录删除成功', 'success')
        return true
      }
      throw new Error('记录不存在')
    } catch (error) {
      console.error('删除记录失败:', error)
      showToast('删除记录失败')
      return false
    }
  },
  
  // 根据ID获取记录
  getRecordById(id) {
    return state.records.find(record => record.id === id)
  },
  
  // 加载分类数据
  loadCategories() {
    try {
      const categories = Storage.getCategories()
      state.categories = categories
    } catch (error) {
      console.error('加载分类失败:', error)
      showToast('加载分类失败')
    }
  },
  
  // 保存分类数据
  saveCategories() {
    try {
      Storage.setCategories(state.categories)
      return true
    } catch (error) {
      console.error('保存分类失败:', error)
      showToast('保存分类失败')
      return false
    }
  },
  
  // 根据ID获取分类
  getCategoryById(id, type = 'expense') {
    return state.categories[type].find(category => category.id === id)
  },

  // 获取默认分类
  getDefaultCategories() {
    return Storage.getDefaultCategories()
  },
  
  // 加载设置
  loadSettings() {
    try {
      state.settings = Storage.getSettings()
    } catch (error) {
      console.error('加载设置失败:', error)
      showToast('加载设置失败')
    }
  },
  
  // 保存设置
  saveSettings() {
    try {
      Storage.setSettings(state.settings)
      showToast('设置保存成功', 'success')
      return true
    } catch (error) {
      console.error('保存设置失败:', error)
      showToast('保存设置失败')
      return false
    }
  },
  
  // 更新设置
  updateSettings(newSettings) {
    Object.assign(state.settings, newSettings)
    this.saveSettings()
  },
  
  // 设置日期范围
  setDateRange(start, end) {
    state.dateRange.start = start
    state.dateRange.end = end
  },
  
  // 清空日期范围
  clearDateRange() {
    state.dateRange.start = ''
    state.dateRange.end = ''
  },
  
  // 设置加载状态
  setLoading(loading) {
    state.loading = loading
  }
}

// 创建store实例
const store = {
  state,
  getters,
  actions
}

export default store
