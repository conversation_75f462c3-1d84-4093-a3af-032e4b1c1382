<template>
  <view class="add-page">
    <!-- 顶部标题栏 -->
    <view class="header-section">
      <view class="header-content">
        <text class="page-title">{{ isEdit ? '编辑记录' : '记一笔' }}</text>
        <text class="page-subtitle">{{ currentType === 'expense' ? '记录支出' : '记录收入' }}</text>
      </view>
      <view class="header-actions" v-if="isEdit">
        <view class="action-btn delete-btn" @click="deleteRecord">
          <text class="action-icon">🗑️</text>
        </view>
      </view>
    </view>

    <!-- 类型切换 -->
    <view class="type-section">
      <view class="type-tabs">
        <view
          class="type-tab"
          :class="{ active: currentType === 'expense' }"
          @click="switchType('expense')"
        >
          <view class="tab-icon">💸</view>
          <text class="tab-text">支出</text>
          <view class="tab-indicator"></view>
        </view>
        <view
          class="type-tab"
          :class="{ active: currentType === 'income' }"
          @click="switchType('income')"
        >
          <view class="tab-icon">💰</view>
          <text class="tab-text">收入</text>
          <view class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 金额输入区域 -->
    <view class="amount-section">
      <view class="amount-container">
        <view class="amount-header">
          <text class="amount-label">金额</text>
          <view class="quick-amounts">
            <view
              class="quick-amount"
              v-for="amount in quickAmounts"
              :key="amount"
              @click="setQuickAmount(amount)"
            >
              <text class="quick-text">{{ amount }}</text>
            </view>
          </view>
        </view>

        <view class="amount-display">
          <text class="currency">¥</text>
          <input
            class="amount-input"
            type="digit"
            :value="displayAmount"
            @input="onAmountInput"
            @focus="onAmountFocus"
            @blur="onAmountBlur"
            placeholder="0.00"
            :focus="amountFocused"
          />
        </view>

        <view class="amount-tools">
          <view class="calculator-btn" @click="showCalculator">
            <text class="calc-icon">🧮</text>
            <text class="calc-text">计算器</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分类选择 -->
    <view class="category-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">选择分类</text>
          <text class="section-subtitle">{{ currentCategories.length }}个分类</text>
        </view>
        <view class="header-right">
          <view class="manage-btn" @click="manageCategories">
            <text class="manage-text">管理</text>
          </view>
        </view>
      </view>

      <view class="category-grid">
        <view
          class="category-item"
          v-for="(category, index) in currentCategories"
          :key="category.id"
          :class="{ active: selectedCategory?.id === category.id }"
          :style="{ animationDelay: (index * 0.05) + 's' }"
          @click="selectCategory(category)"
        >
          <view class="category-icon-wrapper">
            <view class="category-icon" :style="{ backgroundColor: category.color }">
              <text class="icon-text">{{ category.icon }}</text>
            </view>
            <view class="selection-indicator" v-if="selectedCategory?.id === category.id">
              <text class="check-icon">✓</text>
            </view>
          </view>
          <text class="category-name">{{ category.name }}</text>
        </view>

        <!-- 添加新分类按钮 -->
        <view class="category-item add-category" @click="addNewCategory">
          <view class="category-icon-wrapper">
            <view class="category-icon add-icon">
              <text class="icon-text">+</text>
            </view>
          </view>
          <text class="category-name">添加分类</text>
        </view>
      </view>
    </view>

    <!-- 详细信息卡片 -->
    <view class="details-section">
      <!-- 日期时间 -->
      <view class="detail-card datetime-card">
        <view class="card-header">
          <text class="card-title">日期时间</text>
          <view class="quick-time-btns">
            <view class="quick-btn" @click="setCurrentTime">
              <text class="quick-text">现在</text>
            </view>
          </view>
        </view>

        <view class="datetime-row">
          <view class="datetime-item" @click="showDatePicker">
            <view class="datetime-icon">📅</view>
            <view class="datetime-content">
              <text class="datetime-label">日期</text>
              <text class="datetime-value">{{ formatDate(formData.date, 'YYYY年MM月DD日') }}</text>
            </view>
            <view class="datetime-arrow">→</view>
          </view>

          <view class="datetime-divider"></view>

          <view class="datetime-item" @click="showTimePicker">
            <view class="datetime-icon">🕐</view>
            <view class="datetime-content">
              <text class="datetime-label">时间</text>
              <text class="datetime-value">{{ formData.time }}</text>
            </view>
            <view class="datetime-arrow">→</view>
          </view>
        </view>
      </view>

      <!-- 备注输入 -->
      <view class="detail-card note-card">
        <view class="card-header">
          <text class="card-title">备注信息</text>
          <text class="char-count">{{ formData.note.length }}/100</text>
        </view>

        <view class="note-input-wrapper">
          <textarea
            class="note-input"
            v-model="formData.note"
            placeholder="添加备注信息，如：在哪里消费、和谁一起等（可选）"
            maxlength="100"
            @focus="onNoteFocus"
            @blur="onNoteBlur"
          />
          <view class="note-suggestions" v-if="noteSuggestions.length > 0">
            <view
              class="suggestion-item"
              v-for="suggestion in noteSuggestions"
              :key="suggestion"
              @click="selectNoteSuggestion(suggestion)"
            >
              <text class="suggestion-text">{{ suggestion }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" @click="cancel">取消</button>
      <button class="btn btn-primary" @click="save" :disabled="!canSave">
        {{ isEdit ? '更新' : '保存' }}
      </button>
    </view>

    <!-- 日期选择器 -->
    <picker
      mode="date"
      :value="formData.date"
      @change="onDateChange"
      :start="minDate"
      :end="maxDate"
      v-show="false"
      ref="datePicker"
    >
      <view></view>
    </picker>

    <!-- 时间选择器 -->
    <picker
      mode="time"
      :value="formData.time"
      @change="onTimeChange"
      v-show="false"
      ref="timePicker"
    >
      <view></view>
    </picker>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatDate, getToday, showToast } from '../../utils/helpers.js'

export default {
  name: 'AddPage',
  setup() {
    // 表单数据
    const formData = ref({
      type: 'expense',
      amount: 0,
      categoryId: null,
      categoryName: '',
      categoryIcon: '',
      categoryColor: '',
      note: '',
      date: getToday(),
      time: new Date().toTimeString().split(' ')[0].substring(0, 5)
    })

    // 界面状态
    const currentType = ref('expense')
    const selectedCategory = ref(null)
    const displayAmount = ref('')
    const amountFocused = ref(false)
    const isEdit = ref(false)
    const editId = ref(null)

    // 快速金额选项
    const quickAmounts = ref([10, 20, 50, 100, 200, 500])

    // 备注建议
    const noteSuggestions = computed(() => {
      if (!formData.value.note || formData.value.note.length < 2) return []

      // 根据分类提供建议
      const suggestions = {
        '餐饮': ['早餐', '午餐', '晚餐', '下午茶', '夜宵'],
        '交通': ['地铁', '公交', '打车', '加油', '停车费'],
        '购物': ['超市', '网购', '服装', '日用品'],
        '娱乐': ['电影', '游戏', 'KTV', '聚会'],
        '医疗': ['看病', '买药', '体检', '保健品'],
        '教育': ['培训', '书籍', '课程', '学费']
      }

      const categoryName = selectedCategory.value?.name || ''
      return suggestions[categoryName] || []
    })

    // 计算属性
    const currentCategories = computed(() => 
      store.state.categories[currentType.value] || []
    )

    const canSave = computed(() => 
      formData.value.amount > 0 && selectedCategory.value
    )

    const minDate = computed(() => '2020-01-01')
    const maxDate = computed(() => {
      const future = new Date()
      future.setFullYear(future.getFullYear() + 1)
      return formatDate(future)
    })

    // 方法
    const switchType = (type) => {
      currentType.value = type
      formData.value.type = type
      selectedCategory.value = null
      formData.value.categoryId = null
      formData.value.categoryName = ''
      formData.value.categoryIcon = ''
      formData.value.categoryColor = ''
    }

    const onAmountInput = (e) => {
      let value = e.detail.value
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '')
      
      // 确保只有一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
      }
      
      // 限制小数位数
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2)
      }
      
      displayAmount.value = value
      formData.value.amount = parseFloat(value) || 0
    }

    // 新增的UI支持方法
    const onAmountFocus = () => {
      amountFocused.value = true
    }

    const onAmountBlur = () => {
      amountFocused.value = false
    }

    const setQuickAmount = (amount) => {
      displayAmount.value = amount.toString()
      formData.value.amount = amount
    }

    const showCalculator = () => {
      // 显示计算器功能（可以后续实现）
      showToast('计算器功能开发中')
    }

    const manageCategories = () => {
      showToast('分类管理功能开发中')
    }

    const addNewCategory = () => {
      showToast('添加分类功能开发中')
    }

    const setCurrentTime = () => {
      const now = new Date()
      formData.value.date = getToday()
      formData.value.time = now.toTimeString().split(' ')[0].substring(0, 5)
      showToast('已设置为当前时间')
    }

    const onNoteFocus = () => {
      // 备注输入框获得焦点时的处理
    }

    const onNoteBlur = () => {
      // 备注输入框失去焦点时的处理
    }

    const selectNoteSuggestion = (suggestion) => {
      formData.value.note = suggestion
    }

    const deleteRecord = () => {
      if (!isEdit.value) return

      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        success: (res) => {
          if (res.confirm) {
            store.actions.deleteRecord(editId.value)
            uni.navigateBack()
          }
        }
      })
    }

    const selectCategory = (category) => {
      selectedCategory.value = category
      formData.value.categoryId = category.id
      formData.value.categoryName = category.name
      formData.value.categoryIcon = category.icon
      formData.value.categoryColor = category.color
    }

    const showDatePicker = () => {
      // 触发日期选择器
      uni.showModal({
        title: '提示',
        content: '请使用系统日期选择器',
        showCancel: false
      })
    }

    const showTimePicker = () => {
      // 触发时间选择器
      uni.showModal({
        title: '提示',
        content: '请使用系统时间选择器',
        showCancel: false
      })
    }

    const onDateChange = (e) => {
      formData.value.date = e.detail.value
    }

    const onTimeChange = (e) => {
      formData.value.time = e.detail.value
    }

    const save = () => {
      if (!canSave.value) {
        showToast('请填写完整信息')
        return
      }

      try {
        if (isEdit.value) {
          store.actions.updateRecord(editId.value, formData.value)
        } else {
          store.actions.addRecord(formData.value)
        }
        
        // 返回上一页
        uni.navigateBack()
      } catch (error) {
        console.error('保存失败:', error)
        showToast('保存失败')
      }
    }

    const cancel = () => {
      uni.navigateBack()
    }

    // 加载编辑记录
    const loadEditRecord = (recordId) => {
      const record = store.state.records.find(r => r.id === recordId)
      if (record) {
        isEdit.value = true
        editId.value = recordId

        // 设置表单数据
        formData.value = {
          type: record.type,
          amount: record.amount,
          categoryId: record.categoryId,
          categoryName: record.categoryName,
          categoryIcon: record.categoryIcon,
          categoryColor: record.categoryColor,
          note: record.note,
          date: record.date,
          time: record.time
        }

        // 设置界面状态
        currentType.value = record.type
        displayAmount.value = record.amount.toString()

        // 设置选中的分类
        const categories = store.state.categories[record.type] || []
        selectedCategory.value = categories.find(cat => cat.id === record.categoryId)
      }
    }

    // 页面加载
    onMounted(() => {
      // 加载分类数据
      store.actions.loadCategories()

      // 检查是否有快速记账类型
      const quickAddType = uni.getStorageSync('quickAddType')
      if (quickAddType) {
        switchType(quickAddType)
        // 清除存储的类型
        uni.removeStorageSync('quickAddType')
      }

      // 检查是否有编辑记录ID
      const editRecordId = uni.getStorageSync('editRecordId')
      if (editRecordId) {
        loadEditRecord(editRecordId)
        // 清除存储的ID
        uni.removeStorageSync('editRecordId')
      }

      // 注意：在实际的uniapp环境中，页面参数会通过onLoad生命周期获取
      // 这里暂时不处理编辑模式，可以在实际运行时通过onLoad获取参数
    })

    return {
      formData,
      currentType,
      selectedCategory,
      displayAmount,
      amountFocused,
      isEdit,
      editId,
      quickAmounts,
      noteSuggestions,
      currentCategories,
      canSave,
      minDate,
      maxDate,
      switchType,
      onAmountInput,
      onAmountFocus,
      onAmountBlur,
      setQuickAmount,
      showCalculator,
      manageCategories,
      addNewCategory,
      setCurrentTime,
      onNoteFocus,
      onNoteBlur,
      selectNoteSuggestion,
      deleteRecord,
      selectCategory,
      showDatePicker,
      showTimePicker,
      onDateChange,
      onTimeChange,
      save,
      cancel,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.add-page {
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);
  min-height: 100vh;
  padding: 0;
}

/* 顶部标题栏 */
.header-section {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  padding: 20px 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  display: block;
  margin-bottom: 4px;
}

.page-subtitle {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

.delete-btn {
  background: rgba(244, 67, 54, 0.3);
}

.action-icon {
  font-size: 18px;
}

/* 类型切换区域 */
.type-section {
  padding: 0 16px;
  margin-top: -12px;
  margin-bottom: 24px;
}

.type-tabs {
  background: white;
  border-radius: 16px;
  padding: 8px;
  display: flex;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.type-tab {
  flex: 1;
  padding: 16px 12px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.type-tab:active {
  transform: scale(0.98);
}

.type-tab.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.type-tab.active .tab-indicator {
  opacity: 1;
}

.tab-icon {
  font-size: 20px;
}

.tab-text {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.type-tab.active .tab-text {
  color: white;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: white;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 金额输入区域 */
.amount-section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.amount-container {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.amount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.amount-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quick-amounts {
  display: flex;
  gap: 8px;
}

.quick-amount {
  padding: 6px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.quick-amount:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.quick-text {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}

.amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.amount-display:focus-within {
  border-color: #4CAF50;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.currency {
  font-size: 32px;
  color: #666;
  margin-right: 8px;
  font-weight: 600;
}

.amount-input {
  font-size: 48px;
  font-weight: 300;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
  text-align: left;
  min-width: 200px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.amount-input::placeholder {
  color: #ccc;
}

.amount-tools {
  display: flex;
  justify-content: center;
}

.calculator-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.calculator-btn:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.calc-icon {
  font-size: 16px;
}

.calc-text {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}

/* 分类选择 */
.category-section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
}

.manage-btn {
  padding: 8px 16px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.manage-btn:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.manage-text {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  background: white;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeInUp 0.6s ease-out both;
}

.category-item:active {
  transform: scale(0.95);
}

.category-item.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
}

.add-category {
  border: 2px dashed #ddd;
  background: transparent;
}

.add-category:active {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}

.category-icon-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.category-item.active .category-icon {
  background: rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.add-icon {
  background: #f0f0f0 !important;
  color: #999;
}

.icon-text {
  font-size: 20px;
  color: white;
}

.add-category .icon-text {
  color: #999;
  font-size: 24px;
  font-weight: 300;
}

.selection-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.check-icon {
  font-size: 12px;
  color: #4CAF50;
  font-weight: bold;
}

.category-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.category-item.active .category-name {
  color: white;
}

.add-category .category-name {
  color: #999;
}

/* 详细信息区域 */
.details-section {
  padding: 0 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-card {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.quick-time-btns {
  display: flex;
  gap: 8px;
}

.quick-btn {
  padding: 6px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.quick-btn:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.quick-text {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}

.char-count {
  font-size: 12px;
  color: #999;
}

/* 日期时间卡片 */
.datetime-row {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.datetime-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.datetime-item:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: scale(0.98);
}

.datetime-icon {
  font-size: 20px;
}

.datetime-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.datetime-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.datetime-value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.datetime-arrow {
  font-size: 16px;
  color: #ccc;
  font-weight: bold;
}

.datetime-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  margin: 8px 0;
}

/* 备注输入卡片 */
.note-input-wrapper {
  position: relative;
}

.note-input {
  width: 100%;
  min-height: 80px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.note-input:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
  background: white;
}

.note-input::placeholder {
  color: #999;
}

.note-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggestion-item {
  padding: 8px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.suggestion-item:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.suggestion-text {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画 */
.header-section {
  animation: slideInDown 0.6s ease-out;
}

.type-section {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.amount-section {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.category-section {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.details-section {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .category-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .quick-amounts {
    flex-wrap: wrap;
    gap: 6px;
  }

  .quick-amount {
    padding: 4px 8px;
  }

  .amount-input {
    font-size: 36px;
  }
}
</style>
