<template>
  <view class="add-page">
    <!-- 类型切换 -->
    <view class="type-tabs">
      <view 
        class="type-tab" 
        :class="{ active: currentType === 'expense' }"
        @click="switchType('expense')"
      >
        <text class="tab-text">支出</text>
      </view>
      <view 
        class="type-tab" 
        :class="{ active: currentType === 'income' }"
        @click="switchType('income')"
      >
        <text class="tab-text">收入</text>
      </view>
    </view>

    <!-- 金额输入 -->
    <view class="amount-section">
      <view class="amount-display">
        <text class="currency">¥</text>
        <input 
          class="amount-input" 
          type="digit"
          :value="displayAmount"
          @input="onAmountInput"
          placeholder="0.00"
          :focus="amountFocused"
        />
      </view>
    </view>

    <!-- 分类选择 -->
    <view class="category-section">
      <view class="section-title">选择分类</view>
      <view class="category-grid">
        <view 
          class="category-item" 
          v-for="category in currentCategories" 
          :key="category.id"
          :class="{ active: selectedCategory?.id === category.id }"
          @click="selectCategory(category)"
        >
          <view class="category-icon" :style="{ backgroundColor: category.color }">
            {{ category.icon }}
          </view>
          <text class="category-name">{{ category.name }}</text>
        </view>
      </view>
    </view>

    <!-- 日期时间 -->
    <view class="datetime-section">
      <view class="datetime-item" @click="showDatePicker">
        <text class="datetime-label">日期</text>
        <text class="datetime-value">{{ formatDate(formData.date, 'YYYY年MM月DD日') }}</text>
      </view>
      <view class="datetime-item" @click="showTimePicker">
        <text class="datetime-label">时间</text>
        <text class="datetime-value">{{ formData.time }}</text>
      </view>
    </view>

    <!-- 备注输入 -->
    <view class="note-section">
      <view class="section-title">备注</view>
      <textarea 
        class="note-input" 
        v-model="formData.note"
        placeholder="添加备注信息（可选）"
        maxlength="100"
      />
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn btn-secondary" @click="cancel">取消</button>
      <button class="btn btn-primary" @click="save" :disabled="!canSave">
        {{ isEdit ? '更新' : '保存' }}
      </button>
    </view>

    <!-- 日期选择器 -->
    <picker
      mode="date"
      :value="formData.date"
      @change="onDateChange"
      :start="minDate"
      :end="maxDate"
      v-show="false"
      ref="datePicker"
    >
      <view></view>
    </picker>

    <!-- 时间选择器 -->
    <picker
      mode="time"
      :value="formData.time"
      @change="onTimeChange"
      v-show="false"
      ref="timePicker"
    >
      <view></view>
    </picker>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatDate, getToday, showToast } from '../../utils/helpers.js'

export default {
  name: 'AddPage',
  setup() {
    // 表单数据
    const formData = ref({
      type: 'expense',
      amount: 0,
      categoryId: null,
      categoryName: '',
      categoryIcon: '',
      categoryColor: '',
      note: '',
      date: getToday(),
      time: new Date().toTimeString().split(' ')[0].substring(0, 5)
    })

    // 界面状态
    const currentType = ref('expense')
    const selectedCategory = ref(null)
    const displayAmount = ref('')
    const amountFocused = ref(true)
    const isEdit = ref(false)
    const editId = ref('')

    // 计算属性
    const currentCategories = computed(() => 
      store.state.categories[currentType.value] || []
    )

    const canSave = computed(() => 
      formData.value.amount > 0 && selectedCategory.value
    )

    const minDate = computed(() => '2020-01-01')
    const maxDate = computed(() => {
      const future = new Date()
      future.setFullYear(future.getFullYear() + 1)
      return formatDate(future)
    })

    // 方法
    const switchType = (type) => {
      currentType.value = type
      formData.value.type = type
      selectedCategory.value = null
      formData.value.categoryId = null
      formData.value.categoryName = ''
      formData.value.categoryIcon = ''
      formData.value.categoryColor = ''
    }

    const onAmountInput = (e) => {
      let value = e.detail.value
      // 只允许数字和小数点
      value = value.replace(/[^\d.]/g, '')
      
      // 确保只有一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('')
      }
      
      // 限制小数位数
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2)
      }
      
      displayAmount.value = value
      formData.value.amount = parseFloat(value) || 0
    }

    const selectCategory = (category) => {
      selectedCategory.value = category
      formData.value.categoryId = category.id
      formData.value.categoryName = category.name
      formData.value.categoryIcon = category.icon
      formData.value.categoryColor = category.color
    }

    const showDatePicker = () => {
      // 触发日期选择器
      uni.showModal({
        title: '提示',
        content: '请使用系统日期选择器',
        showCancel: false
      })
    }

    const showTimePicker = () => {
      // 触发时间选择器
      uni.showModal({
        title: '提示',
        content: '请使用系统时间选择器',
        showCancel: false
      })
    }

    const onDateChange = (e) => {
      formData.value.date = e.detail.value
    }

    const onTimeChange = (e) => {
      formData.value.time = e.detail.value
    }

    const save = () => {
      if (!canSave.value) {
        showToast('请填写完整信息')
        return
      }

      try {
        if (isEdit.value) {
          store.actions.updateRecord(editId.value, formData.value)
        } else {
          store.actions.addRecord(formData.value)
        }
        
        // 返回上一页
        uni.navigateBack()
      } catch (error) {
        console.error('保存失败:', error)
        showToast('保存失败')
      }
    }

    const cancel = () => {
      uni.navigateBack()
    }

    // 页面加载
    onMounted(() => {
      // 加载分类数据
      store.actions.loadCategories()

      // 注意：在实际的uniapp环境中，页面参数会通过onLoad生命周期获取
      // 这里暂时不处理编辑模式，可以在实际运行时通过onLoad获取参数
    })

    return {
      formData,
      currentType,
      selectedCategory,
      displayAmount,
      amountFocused,
      isEdit,
      currentCategories,
      canSave,
      minDate,
      maxDate,
      switchType,
      onAmountInput,
      selectCategory,
      showDatePicker,
      showTimePicker,
      onDateChange,
      onTimeChange,
      save,
      cancel,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.add-page {
  background-color: $uni-bg-color-grey;
  min-height: 100vh;
}

/* 类型切换 */
.type-tabs {
  display: flex;
  background-color: white;
  margin-bottom: 16px;
}

.type-tab {
  flex: 1;
  padding: 16px;
  text-align: center;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;

  &.active {
    border-bottom-color: $uni-color-primary;

    .tab-text {
      color: $uni-color-primary;
      font-weight: 600;
    }
  }
}

.tab-text {
  font-size: 16px;
  color: $uni-text-color-grey;
}

/* 金额输入 */
.amount-section {
  background-color: white;
  padding: 40px 20px;
  margin-bottom: 16px;
  text-align: center;
}

.amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
}

.currency {
  font-size: 32px;
  color: $uni-text-color-grey;
  margin-right: 8px;
}

.amount-input {
  font-size: 48px;
  font-weight: 300;
  color: $uni-text-color;
  border: none;
  outline: none;
  background: transparent;
  text-align: left;
  min-width: 200px;
}

/* 分类选择 */
.category-section {
  background-color: white;
  padding: 20px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: $uni-text-color;
  margin-bottom: 16px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &.active {
    background-color: rgba(76, 175, 80, 0.1);

    .category-icon {
      transform: scale(1.1);
    }

    .category-name {
      color: $uni-color-primary;
      font-weight: 600;
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 8px;
  transition: transform 0.3s ease;
}

.category-name {
  font-size: 12px;
  color: $uni-text-color;
  text-align: center;
}

/* 日期时间 */
.datetime-section {
  background-color: white;
  margin-bottom: 16px;
}

.datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid $uni-border-color;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.datetime-label {
  font-size: 16px;
  color: $uni-text-color;
}

.datetime-value {
  font-size: 16px;
  color: $uni-text-color-grey;
}

/* 备注输入 */
.note-section {
  background-color: white;
  padding: 20px;
  margin-bottom: 32px;
}

.note-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid $uni-border-color;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: none;

  &:focus {
    border-color: $uni-color-primary;
    outline: none;
  }
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16px;
  padding: 0 20px 32px;

  .btn {
    flex: 1;
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
  }
}
</style>
