<template>
  <view class="settings-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <text class="avatar-text">记</text>
      </view>
      <view class="user-info">
        <text class="user-name">记账本用户</text>
        <text class="user-desc">让记账变得简单</text>
      </view>
    </view>

    <!-- 统计概览 -->
    <view class="stats-overview">
      <view class="stats-item">
        <text class="stats-number">{{ totalRecords }}</text>
        <text class="stats-label">总记录</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ totalDays }}</text>
        <text class="stats-label">记账天数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ formatAmount(totalAmount) }}</text>
        <text class="stats-label">累计金额</text>
      </view>
    </view>

    <!-- 设置选项 -->
    <view class="settings-section">
      <view class="section-title">基本设置</view>
      
      <view class="setting-item" @click="showCurrencyPicker">
        <view class="setting-left">
          <text class="setting-icon">💰</text>
          <text class="setting-name">货币符号</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{ settings.currency }}</text>
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="showBudgetSetting">
        <view class="setting-left">
          <text class="setting-icon">🎯</text>
          <text class="setting-name">月度预算</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{ formatAmount(settings.monthlyBudget) }}</text>
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="openAIConfig">
        <view class="setting-left">
          <text class="setting-icon">🤖</text>
          <text class="setting-name">AI配置</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{ aiConfigStatus }}</text>
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-left">
          <text class="setting-icon">🔔</text>
          <text class="setting-name">预算提醒</text>
        </view>
        <view class="setting-right">
          <switch 
            :checked="settings.budgetAlert" 
            @change="onBudgetAlertChange"
            color="#4CAF50"
          />
        </view>
      </view>
    </view>

    <!-- 分类管理 -->
    <view class="settings-section">
      <view class="section-title">分类管理</view>
      
      <view class="setting-item" @click="manageCategoriesExpense">
        <view class="setting-left">
          <text class="setting-icon">💸</text>
          <text class="setting-name">支出分类</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{ expenseCategories.length }}个</text>
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="manageCategoriesIncome">
        <view class="setting-left">
          <text class="setting-icon">💰</text>
          <text class="setting-name">收入分类</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">{{ incomeCategories.length }}个</text>
          <text class="setting-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 数据管理 -->
    <view class="settings-section">
      <view class="section-title">数据管理</view>
      
      <view class="setting-item" @click="exportData">
        <view class="setting-left">
          <text class="setting-icon">📤</text>
          <text class="setting-name">导出数据</text>
        </view>
        <view class="setting-right">
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="importData">
        <view class="setting-left">
          <text class="setting-icon">📥</text>
          <text class="setting-name">导入数据</text>
        </view>
        <view class="setting-right">
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="clearData">
        <view class="setting-left">
          <text class="setting-icon">🗑️</text>
          <text class="setting-name">清空数据</text>
        </view>
        <view class="setting-right">
          <text class="setting-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 关于 -->
    <view class="settings-section">
      <view class="section-title">关于</view>
      
      <view class="setting-item" @click="showAbout">
        <view class="setting-left">
          <text class="setting-icon">ℹ️</text>
          <text class="setting-name">关于记账本</text>
        </view>
        <view class="setting-right">
          <text class="setting-value">v1.0.0</text>
          <text class="setting-arrow">></text>
        </view>
      </view>

      <view class="setting-item" @click="showHelp">
        <view class="setting-left">
          <text class="setting-icon">❓</text>
          <text class="setting-name">使用帮助</text>
        </view>
        <view class="setting-right">
          <text class="setting-arrow">></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatAmount, showToast } from '../../utils/helpers.js'
import AIService from '../../utils/ai-service.js'

export default {
  name: 'SettingsPage',
  setup() {
    const settings = computed(() => store.state.settings)
    const expenseCategories = computed(() => store.state.categories.expense)
    const incomeCategories = computed(() => store.state.categories.income)

    // AI配置状态
    const aiConfigStatus = computed(() => {
      try {
        return AIService.isConfigValid() ? '已配置' : '未配置'
      } catch (error) {
        return '未配置'
      }
    })

    // 统计数据
    const totalRecords = computed(() => store.state.records.length)
    
    const totalDays = computed(() => {
      const dates = [...new Set(store.state.records.map(record => record.date))]
      return dates.length
    })
    
    const totalAmount = computed(() => 
      store.state.records.reduce((total, record) => total + record.amount, 0)
    )

    // 方法
    const showCurrencyPicker = () => {
      const currencies = ['¥', '$', '€', '£', '₹', '₩']
      uni.showActionSheet({
        itemList: currencies,
        success: (res) => {
          const newCurrency = currencies[res.tapIndex]
          store.actions.updateSettings({ currency: newCurrency })
        }
      })
    }

    const showBudgetSetting = () => {
      uni.showModal({
        title: '设置月度预算',
        editable: true,
        placeholderText: '请输入预算金额',
        success: (res) => {
          if (res.confirm && res.content) {
            const budget = parseFloat(res.content) || 0
            store.actions.updateSettings({ monthlyBudget: budget })
          }
        }
      })
    }

    const onBudgetAlertChange = (e) => {
      store.actions.updateSettings({ budgetAlert: e.detail.value })
    }

    const manageCategoriesExpense = () => {
      showToast('分类管理功能开发中')
    }

    const manageCategoriesIncome = () => {
      showToast('分类管理功能开发中')
    }

    const exportData = () => {
      try {
        const data = {
          records: store.state.records,
          categories: store.state.categories,
          settings: store.state.settings,
          exportTime: new Date().toISOString()
        }
        
        // 在实际应用中，这里应该生成文件并提供下载
        console.log('导出数据:', data)
        showToast('数据导出成功', 'success')
      } catch (error) {
        console.error('导出失败:', error)
        showToast('数据导出失败')
      }
    }

    const importData = () => {
      showToast('数据导入功能开发中')
    }

    const clearData = () => {
      uni.showModal({
        title: '确认清空',
        content: '此操作将清空所有记账数据，且无法恢复，确定要继续吗？',
        confirmColor: '#F44336',
        success: (res) => {
          if (res.confirm) {
            try {
              // 清空记录
              store.state.records = []
              store.actions.saveRecords()
              
              // 重置分类为默认
              store.state.categories = store.actions.getDefaultCategories()
              store.actions.saveCategories()
              
              showToast('数据清空成功', 'success')
            } catch (error) {
              console.error('清空失败:', error)
              showToast('数据清空失败')
            }
          }
        }
      })
    }

    const showAbout = () => {
      uni.showModal({
        title: '关于记账本',
        content: '记账本 v1.0.0\n\n一款简洁易用的个人记账小程序\n\n功能特色：\n• 快速记账\n• 分类管理\n• 统计分析\n• 数据导出',
        showCancel: false,
        confirmText: '知道了'
      })
    }

    // 打开AI配置页面
    const openAIConfig = () => {
      uni.navigateTo({
        url: '/pages/ai-config/ai-config'
      })
    }

    const showHelp = () => {
      uni.showModal({
        title: '使用帮助',
        content: '1. 点击首页的快速记账按钮开始记账\n2. 在记账页面选择分类和输入金额\n3. 在账单页面查看所有记录\n4. 在统计页面查看收支分析\n5. 在设置页面管理分类和导出数据',
        showCancel: false,
        confirmText: '知道了'
      })
    }

    onMounted(() => {
      store.actions.loadSettings()
      store.actions.loadCategories()
      store.actions.loadRecords()
    })

    return {
      settings,
      expenseCategories,
      incomeCategories,
      aiConfigStatus,
      totalRecords,
      totalDays,
      totalAmount,
      formatAmount,
      showCurrencyPicker,
      showBudgetSetting,
      onBudgetAlertChange,
      manageCategoriesExpense,
      manageCategoriesIncome,
      exportData,
      importData,
      clearData,
      openAIConfig,
      showAbout,
      showHelp
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-page {
  background-color: $uni-bg-color-grey;
  min-height: 100vh;
  padding: 16px;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.avatar-text {
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: $uni-text-color;
  display: block;
  margin-bottom: 4px;
}

.user-desc {
  font-size: 14px;
  color: $uni-text-color-grey;
}

/* 统计概览 */
.stats-overview {
  display: flex;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-item {
  flex: 1;
  padding: 20px;
  text-align: center;
  border-right: 1px solid $uni-border-color;

  &:last-child {
    border-right: none;
  }
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  color: $uni-color-primary;
  display: block;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 12px;
  color: $uni-text-color-grey;
}

/* 设置区域 */
.settings-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: $uni-text-color-grey;
  margin-bottom: 12px;
  padding: 0 4px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 16px 20px;
  border-bottom: 1px solid $uni-border-color;
  transition: background-color 0.3s ease;

  &:first-child {
    border-radius: 12px 12px 0 0;
  }

  &:last-child {
    border-bottom: none;
    border-radius: 0 0 12px 12px;
  }

  &:only-child {
    border-radius: 12px;
  }

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.setting-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 20px;
  margin-right: 12px;
}

.setting-name {
  font-size: 16px;
  color: $uni-text-color;
}

.setting-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 14px;
  color: $uni-text-color-grey;
  margin-right: 8px;
}

.setting-arrow {
  font-size: 12px;
  color: $uni-text-color-placeholder;
}
</style>
