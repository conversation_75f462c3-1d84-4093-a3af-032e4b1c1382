{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\nimport store from './store/index.js'\r\n\r\nexport default {\r\n  onLaunch: function() {\r\n    console.log('记账本启动')\r\n    // 初始化数据\r\n    store.actions.init()\r\n  },\r\n  onShow: function() {\r\n    console.log('记账本显示')\r\n  },\r\n  onHide: function() {\r\n    console.log('记账本隐藏')\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import './uni.scss';\r\n\r\n/* 全局样式重置 */\r\npage {\r\n  background-color: $uni-bg-color-grey;\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* 全局通用样式 */\r\n.safe-area-inset-bottom {\r\n  padding-bottom: constant(safe-area-inset-bottom);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n.safe-area-inset-top {\r\n  padding-top: constant(safe-area-inset-top);\r\n  padding-top: env(safe-area-inset-top);\r\n}\r\n\r\n/* 按钮样式 */\r\n.btn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border: none;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n\r\n  &.btn-primary {\r\n    background-color: $uni-color-primary;\r\n    color: white;\r\n\r\n    &:hover {\r\n      background-color: darken($uni-color-primary, 10%);\r\n    }\r\n  }\r\n\r\n  &.btn-secondary {\r\n    background-color: $uni-bg-color;\r\n    color: $uni-text-color;\r\n    border: 1px solid $uni-border-color;\r\n\r\n    &:hover {\r\n      background-color: $uni-bg-color-hover;\r\n    }\r\n  }\r\n\r\n  &.btn-danger {\r\n    background-color: $uni-color-error;\r\n    color: white;\r\n\r\n    &:hover {\r\n      background-color: darken($uni-color-error, 10%);\r\n    }\r\n  }\r\n\r\n  &.btn-small {\r\n    padding: 8px 16px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  &.btn-large {\r\n    padding: 16px 32px;\r\n    font-size: 18px;\r\n  }\r\n\r\n  &:disabled {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n  }\r\n}\r\n\r\n/* 输入框样式 */\r\n.input {\r\n  width: 100%;\r\n  padding: 12px 16px;\r\n  border: 1px solid $uni-border-color;\r\n  border-radius: 8px;\r\n  font-size: 16px;\r\n  background-color: $uni-bg-color;\r\n\r\n  &:focus {\r\n    border-color: $uni-color-primary;\r\n    outline: none;\r\n  }\r\n\r\n  &::placeholder {\r\n    color: $uni-text-color-placeholder;\r\n  }\r\n}\r\n\r\n/* 卡片样式 */\r\n.card {\r\n  background-color: $uni-bg-color;\r\n  border-radius: 12px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 16px;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 列表项样式 */\r\n.list-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background-color: $uni-bg-color;\r\n  border-bottom: 1px solid $uni-border-color;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  &:active {\r\n    background-color: $uni-bg-color-hover;\r\n  }\r\n}\r\n\r\n/* 加载动画 */\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n\r\n  .loading-spinner {\r\n    width: 20px;\r\n    height: 20px;\r\n    border: 2px solid $uni-border-color;\r\n    border-top: 2px solid $uni-color-primary;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n\r\n  .empty-icon {\r\n    font-size: 48px;\r\n    margin-bottom: 16px;\r\n    opacity: 0.5;\r\n  }\r\n\r\n  .empty-text {\r\n    color: $uni-text-color-grey;\r\n    font-size: 16px;\r\n    margin-bottom: 8px;\r\n  }\r\n\r\n  .empty-desc {\r\n    color: $uni-text-color-placeholder;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n/* 页面过渡动画 */\r\n.page-enter-active, .page-leave-active {\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.page-enter-from {\r\n  opacity: 0;\r\n  transform: translateX(100%);\r\n}\r\n\r\n.page-leave-to {\r\n  opacity: 0;\r\n  transform: translateX(-100%);\r\n}\r\n\r\n/* 卡片悬浮效果 */\r\n.card-hover {\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n  }\r\n}\r\n\r\n/* 按钮点击效果 */\r\n.btn-press {\r\n  transition: all 0.2s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.98);\r\n  }\r\n}\r\n\r\n/* 淡入动画 */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 滑入动画 */\r\n.slide-in-up {\r\n  animation: slideInUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 弹跳动画 */\r\n.bounce-in {\r\n  animation: bounceIn 0.6s ease-out;\r\n}\r\n\r\n@keyframes bounceIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.05);\r\n  }\r\n  70% {\r\n    transform: scale(0.9);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "store", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;AAGA,MAAK,YAAU;AAAA,EACb,UAAU,WAAW;AACnBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,OAAO;AAEnBC,gBAAK,MAAC,QAAQ,KAAK;AAAA,EACpB;AAAA,EACD,QAAQ,WAAW;AACjBD,kBAAAA,MAAA,MAAA,OAAA,iBAAY,OAAO;AAAA,EACpB;AAAA,EACD,QAAQ,WAAW;AACjBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,OAAO;AAAA,EACrB;AACF;ACAO,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}