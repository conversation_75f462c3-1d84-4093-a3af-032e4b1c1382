/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-fc23ec97 {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-fc23ec97 {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-fc23ec97 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-fc23ec97 {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-fc23ec97 {
  justify-content: space-between;
}
.flex-center.data-v-fc23ec97 {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-fc23ec97 {
  color: #4CAF50;
}
.text-expense.data-v-fc23ec97 {
  color: #F44336;
}
.text-bold.data-v-fc23ec97 {
  font-weight: bold;
}
.text-center.data-v-fc23ec97 {
  text-align: center;
}
.text-right.data-v-fc23ec97 {
  text-align: right;
}
.statistics-page.data-v-fc23ec97 {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 时间选择器 */
.time-selector.data-v-fc23ec97 {
  display: flex;
  background-color: white;
  padding: 16px;
  margin-bottom: 16px;
}
.time-tab.data-v-fc23ec97 {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;
}
.time-tab.active.data-v-fc23ec97 {
  background-color: #4CAF50;
}
.time-tab.active .tab-text.data-v-fc23ec97 {
  color: white;
  font-weight: 600;
}
.tab-text.data-v-fc23ec97 {
  font-size: 14px;
  color: #333;
}

/* 总览卡片 */
.overview-card.data-v-fc23ec97 {
  display: flex;
  background-color: white;
  padding: 24px 20px;
  margin: 0 16px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.overview-item.data-v-fc23ec97 {
  flex: 1;
  text-align: center;
}
.overview-label.data-v-fc23ec97 {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.overview-value.data-v-fc23ec97 {
  font-size: 20px;
  font-weight: bold;
}
.overview-value.income.data-v-fc23ec97 {
  color: #4CAF50;
}
.overview-value.expense.data-v-fc23ec97 {
  color: #F44336;
}
.overview-value.negative.data-v-fc23ec97 {
  color: #F44336;
}
.overview-divider.data-v-fc23ec97 {
  width: 1px;
  background-color: #e5e5e5;
  margin: 0 16px;
}

/* 图表区域 */
.chart-section.data-v-fc23ec97 {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.section-title.data-v-fc23ec97 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}
.simple-chart .chart-bar.data-v-fc23ec97 {
  margin-bottom: 16px;
}
.simple-chart .chart-bar.data-v-fc23ec97:last-child {
  margin-bottom: 0;
}
.bar-info.data-v-fc23ec97 {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.bar-icon.data-v-fc23ec97 {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
}
.bar-name.data-v-fc23ec97 {
  flex: 1;
  font-size: 14px;
  color: #333;
}
.bar-amount.data-v-fc23ec97 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.bar-container.data-v-fc23ec97 {
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}
.bar-fill.data-v-fc23ec97 {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 趋势分析 */
.trend-section.data-v-fc23ec97 {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.trend-chart.data-v-fc23ec97 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 120px;
  padding: 0 8px;
}
.trend-item.data-v-fc23ec97 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 2px;
}
.trend-date.data-v-fc23ec97 {
  font-size: 10px;
  color: #999;
  margin-bottom: 8px;
}
.trend-bars.data-v-fc23ec97 {
  display: flex;
  align-items: flex-end;
  height: 60px;
  margin-bottom: 8px;
}
.trend-bar.data-v-fc23ec97 {
  width: 8px;
  margin: 0 1px;
  border-radius: 4px 4px 0 0;
}
.trend-bar.income.data-v-fc23ec97 {
  background-color: rgba(76, 175, 80, 0.2);
}
.trend-bar.expense.data-v-fc23ec97 {
  background-color: rgba(244, 67, 54, 0.2);
}
.trend-bar-fill.data-v-fc23ec97 {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}
.trend-bar.income .trend-bar-fill.data-v-fc23ec97 {
  background-color: #4CAF50;
}
.trend-bar.expense .trend-bar-fill.data-v-fc23ec97 {
  background-color: #F44336;
}
.trend-values.data-v-fc23ec97 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.trend-income.data-v-fc23ec97, .trend-expense.data-v-fc23ec97 {
  font-size: 8px;
  line-height: 1.2;
}
.trend-income.data-v-fc23ec97 {
  color: #4CAF50;
}
.trend-expense.data-v-fc23ec97 {
  color: #F44336;
}

/* 分类详情 */
.category-section.data-v-fc23ec97 {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.category-tabs.data-v-fc23ec97 {
  display: flex;
  border-bottom: 1px solid #e5e5e5;
}
.category-tab.data-v-fc23ec97 {
  flex: 1;
  padding: 16px;
  text-align: center;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}
.category-tab.active.data-v-fc23ec97 {
  border-bottom-color: #4CAF50;
}
.category-tab.active .tab-text.data-v-fc23ec97 {
  color: #4CAF50;
  font-weight: 600;
}
.category-list.data-v-fc23ec97 {
  padding: 0 20px 20px;
}
.category-item.data-v-fc23ec97 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e5e5;
}
.category-item.data-v-fc23ec97:last-child {
  border-bottom: none;
}
.category-left.data-v-fc23ec97 {
  display: flex;
  align-items: center;
  flex: 1;
}
.category-icon.data-v-fc23ec97 {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}
.category-info.data-v-fc23ec97 {
  flex: 1;
}
.category-name.data-v-fc23ec97 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.category-count.data-v-fc23ec97 {
  font-size: 12px;
  color: #999;
}
.category-right.data-v-fc23ec97 {
  text-align: right;
}
.category-amount.data-v-fc23ec97 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.category-percent.data-v-fc23ec97 {
  font-size: 12px;
  color: #999;
}