{"version": 3, "file": "helpers.js", "sources": ["utils/helpers.js"], "sourcesContent": ["/**\n * 工具函数\n */\n\n/**\n * 格式化金额\n * @param {number} amount 金额\n * @param {string} currency 货币符号\n * @returns {string} 格式化后的金额\n */\nexport function formatAmount(amount, currency = '¥') {\n  if (typeof amount !== 'number') {\n    amount = parseFloat(amount) || 0\n  }\n  return `${currency}${amount.toFixed(2)}`\n}\n\n/**\n * 格式化日期\n * @param {string|Date} date 日期\n * @param {string} format 格式\n * @returns {string} 格式化后的日期\n */\nexport function formatDate(date, format = 'YYYY-MM-DD') {\n  const d = new Date(date)\n  const year = d.getFullYear()\n  const month = String(d.getMonth() + 1).padStart(2, '0')\n  const day = String(d.getDate()).padStart(2, '0')\n  \n  switch (format) {\n    case 'YYYY-MM-DD':\n      return `${year}-${month}-${day}`\n    case 'MM-DD':\n      return `${month}-${day}`\n    case 'YYYY年MM月DD日':\n      return `${year}年${month}月${day}日`\n    case 'MM月DD日':\n      return `${month}月${day}日`\n    default:\n      return `${year}-${month}-${day}`\n  }\n}\n\n/**\n * 格式化时间\n * @param {string|Date} time 时间\n * @returns {string} 格式化后的时间\n */\nexport function formatTime(time) {\n  if (typeof time === 'string' && time.includes(':')) {\n    return time\n  }\n  const d = new Date(time)\n  const hours = String(d.getHours()).padStart(2, '0')\n  const minutes = String(d.getMinutes()).padStart(2, '0')\n  return `${hours}:${minutes}`\n}\n\n/**\n * 获取今天的日期\n * @returns {string} YYYY-MM-DD格式的日期\n */\nexport function getToday() {\n  return formatDate(new Date())\n}\n\n/**\n * 获取本月第一天\n * @returns {string} YYYY-MM-DD格式的日期\n */\nexport function getFirstDayOfMonth() {\n  const now = new Date()\n  return formatDate(new Date(now.getFullYear(), now.getMonth(), 1))\n}\n\n/**\n * 获取本月最后一天\n * @returns {string} YYYY-MM-DD格式的日期\n */\nexport function getLastDayOfMonth() {\n  const now = new Date()\n  return formatDate(new Date(now.getFullYear(), now.getMonth() + 1, 0))\n}\n\n/**\n * 获取本周第一天\n * @param {number} firstDayOfWeek 一周的第一天 (0: 周日, 1: 周一)\n * @returns {string} YYYY-MM-DD格式的日期\n */\nexport function getFirstDayOfWeek(firstDayOfWeek = 1) {\n  const now = new Date()\n  const day = now.getDay()\n  const diff = day - firstDayOfWeek\n  const firstDay = new Date(now)\n  firstDay.setDate(now.getDate() - diff)\n  return formatDate(firstDay)\n}\n\n/**\n * 获取本周最后一天\n * @param {number} firstDayOfWeek 一周的第一天 (0: 周日, 1: 周一)\n * @returns {string} YYYY-MM-DD格式的日期\n */\nexport function getLastDayOfWeek(firstDayOfWeek = 1) {\n  const now = new Date()\n  const day = now.getDay()\n  const diff = day - firstDayOfWeek\n  const lastDay = new Date(now)\n  lastDay.setDate(now.getDate() - diff + 6)\n  return formatDate(lastDay)\n}\n\n/**\n * 获取相对日期描述\n * @param {string} date YYYY-MM-DD格式的日期\n * @returns {string} 相对日期描述\n */\nexport function getRelativeDateText(date) {\n  const today = getToday()\n  const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))\n  const tomorrow = formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000))\n  \n  if (date === today) {\n    return '今天'\n  } else if (date === yesterday) {\n    return '昨天'\n  } else if (date === tomorrow) {\n    return '明天'\n  } else {\n    return formatDate(date, 'MM月DD日')\n  }\n}\n\n/**\n * 生成唯一ID\n * @returns {string} 唯一ID\n */\nexport function generateId() {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2)\n}\n\n/**\n * 防抖函数\n * @param {Function} func 要防抖的函数\n * @param {number} wait 等待时间\n * @returns {Function} 防抖后的函数\n */\nexport function debounce(func, wait) {\n  let timeout\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout)\n      func(...args)\n    }\n    clearTimeout(timeout)\n    timeout = setTimeout(later, wait)\n  }\n}\n\n/**\n * 节流函数\n * @param {Function} func 要节流的函数\n * @param {number} limit 限制时间\n * @returns {Function} 节流后的函数\n */\nexport function throttle(func, limit) {\n  let inThrottle\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args)\n      inThrottle = true\n      setTimeout(() => inThrottle = false, limit)\n    }\n  }\n}\n\n/**\n * 深拷贝对象\n * @param {any} obj 要拷贝的对象\n * @returns {any} 拷贝后的对象\n */\nexport function deepClone(obj) {\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n  \n  if (obj instanceof Date) {\n    return new Date(obj.getTime())\n  }\n  \n  if (obj instanceof Array) {\n    return obj.map(item => deepClone(item))\n  }\n  \n  if (typeof obj === 'object') {\n    const clonedObj = {}\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        clonedObj[key] = deepClone(obj[key])\n      }\n    }\n    return clonedObj\n  }\n}\n\n/**\n * 显示提示信息\n * @param {string} title 提示标题\n * @param {string} icon 图标类型\n */\nexport function showToast(title, icon = 'none') {\n  uni.showToast({\n    title,\n    icon,\n    duration: 2000\n  })\n}\n\n/**\n * 显示加载中\n * @param {string} title 加载文字\n */\nexport function showLoading(title = '加载中...') {\n  uni.showLoading({\n    title,\n    mask: true\n  })\n}\n\n/**\n * 隐藏加载中\n */\nexport function hideLoading() {\n  uni.hideLoading()\n}\n"], "names": ["uni"], "mappings": ";;AAUO,SAAS,aAAa,QAAQ,WAAW,KAAK;AACnD,MAAI,OAAO,WAAW,UAAU;AAC9B,aAAS,WAAW,MAAM,KAAK;AAAA,EAChC;AACD,SAAO,GAAG,QAAQ,GAAG,OAAO,QAAQ,CAAC,CAAC;AACxC;AAQO,SAAS,WAAW,MAAM,SAAS,cAAc;AACtD,QAAM,IAAI,IAAI,KAAK,IAAI;AACvB,QAAM,OAAO,EAAE,YAAa;AAC5B,QAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,QAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAE/C,UAAQ,QAAM;AAAA,IACZ,KAAK;AACH,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC,KAAK;AACH,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB,KAAK;AACH,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAChC,KAAK;AACH,aAAO,GAAG,KAAK,IAAI,GAAG;AAAA,IACxB;AACE,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,EACjC;AACH;AAqBO,SAAS,WAAW;AACzB,SAAO,WAAW,oBAAI,MAAM;AAC9B;AAMO,SAAS,qBAAqB;AACnC,QAAM,MAAM,oBAAI,KAAM;AACtB,SAAO,WAAW,IAAI,KAAK,IAAI,YAAa,GAAE,IAAI,YAAY,CAAC,CAAC;AAClE;AAMO,SAAS,oBAAoB;AAClC,QAAM,MAAM,oBAAI,KAAM;AACtB,SAAO,WAAW,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAU,IAAG,GAAG,CAAC,CAAC;AACtE;AAOO,SAAS,kBAAkB,iBAAiB,GAAG;AACpD,QAAM,MAAM,oBAAI,KAAM;AACtB,QAAM,MAAM,IAAI,OAAQ;AACxB,QAAM,OAAO,MAAM;AACnB,QAAM,WAAW,IAAI,KAAK,GAAG;AAC7B,WAAS,QAAQ,IAAI,QAAO,IAAK,IAAI;AACrC,SAAO,WAAW,QAAQ;AAC5B;AAOO,SAAS,iBAAiB,iBAAiB,GAAG;AACnD,QAAM,MAAM,oBAAI,KAAM;AACtB,QAAM,MAAM,IAAI,OAAQ;AACxB,QAAM,OAAO,MAAM;AACnB,QAAM,UAAU,IAAI,KAAK,GAAG;AAC5B,UAAQ,QAAQ,IAAI,QAAO,IAAK,OAAO,CAAC;AACxC,SAAO,WAAW,OAAO;AAC3B;AAOO,SAAS,oBAAoB,MAAM;AACxC,QAAM,QAAQ,SAAU;AACxB,QAAM,YAAY,WAAW,IAAI,KAAK,KAAK,IAAK,IAAG,KAAK,KAAK,KAAK,GAAI,CAAC;AACvE,QAAM,WAAW,WAAW,IAAI,KAAK,KAAK,IAAK,IAAG,KAAK,KAAK,KAAK,GAAI,CAAC;AAEtE,MAAI,SAAS,OAAO;AAClB,WAAO;AAAA,EACX,WAAa,SAAS,WAAW;AAC7B,WAAO;AAAA,EACX,WAAa,SAAS,UAAU;AAC5B,WAAO;AAAA,EACX,OAAS;AACL,WAAO,WAAW,MAAM,QAAQ;AAAA,EACjC;AACH;AA+EO,SAAS,UAAU,OAAO,OAAO,QAAQ;AAC9CA,gBAAAA,MAAI,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACd,CAAG;AACH;;;;;;;;;;"}