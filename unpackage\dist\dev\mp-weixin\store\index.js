"use strict";
const common_vendor = require("../common/vendor.js");
const utils_storage = require("../utils/storage.js");
const utils_models = require("../utils/models.js");
const utils_helpers = require("../utils/helpers.js");
const state = common_vendor.reactive({
  // 记账记录
  records: [],
  // 分类数据
  categories: {
    expense: [],
    income: []
  },
  // 用户设置
  settings: {},
  // 当前选中的日期范围
  dateRange: {
    start: "",
    end: ""
  },
  // 加载状态
  loading: false
});
const getters = {
  // 获取所有记录
  allRecords: common_vendor.computed(() => state.records),
  // 获取支出记录
  expenseRecords: common_vendor.computed(
    () => state.records.filter((record) => record.type === "expense")
  ),
  // 获取收入记录
  incomeRecords: common_vendor.computed(
    () => state.records.filter((record) => record.type === "income")
  ),
  // 获取支出分类
  expenseCategories: common_vendor.computed(() => state.categories.expense),
  // 获取收入分类
  incomeCategories: common_vendor.computed(() => state.categories.income),
  // 获取统计数据
  statistics: common_vendor.computed(() => new utils_models.Statistics(state.records)),
  // 获取总收入
  totalIncome: common_vendor.computed(
    () => state.records.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
  ),
  // 获取总支出
  totalExpense: common_vendor.computed(
    () => state.records.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
  ),
  // 获取净收入
  netIncome: common_vendor.computed(() => getters.totalIncome.value - getters.totalExpense.value),
  // 根据日期范围过滤记录
  filteredRecords: common_vendor.computed(() => {
    if (!state.dateRange.start || !state.dateRange.end) {
      return state.records;
    }
    return state.records.filter(
      (record) => record.date >= state.dateRange.start && record.date <= state.dateRange.end
    );
  })
};
const actions = {
  // 初始化数据
  init() {
    this.loadRecords();
    this.loadCategories();
    this.loadSettings();
  },
  // 加载记账记录
  loadRecords() {
    try {
      const records = utils_storage.Storage.getRecords();
      state.records = records.map((record) => new utils_models.Record(record));
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:96", "加载记录失败:", error);
      utils_helpers.showToast("加载记录失败");
    }
  },
  // 保存记账记录
  saveRecords() {
    try {
      const records = state.records.map((record) => record.toObject());
      utils_storage.Storage.setRecords(records);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:108", "保存记录失败:", error);
      utils_helpers.showToast("保存记录失败");
      return false;
    }
  },
  // 添加记录
  addRecord(recordData) {
    try {
      const record = new utils_models.Record(recordData);
      state.records.unshift(record);
      this.saveRecords();
      utils_helpers.showToast("记录添加成功", "success");
      return record;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:123", "添加记录失败:", error);
      utils_helpers.showToast("添加记录失败");
      return null;
    }
  },
  // 更新记录
  updateRecord(id, recordData) {
    try {
      const index = state.records.findIndex((record) => record.id === id);
      if (index !== -1) {
        state.records[index].update(recordData);
        this.saveRecords();
        utils_helpers.showToast("记录更新成功", "success");
        return state.records[index];
      }
      throw new Error("记录不存在");
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:141", "更新记录失败:", error);
      utils_helpers.showToast("更新记录失败");
      return null;
    }
  },
  // 删除记录
  deleteRecord(id) {
    try {
      const index = state.records.findIndex((record) => record.id === id);
      if (index !== -1) {
        state.records.splice(index, 1);
        this.saveRecords();
        utils_helpers.showToast("记录删除成功", "success");
        return true;
      }
      throw new Error("记录不存在");
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:159", "删除记录失败:", error);
      utils_helpers.showToast("删除记录失败");
      return false;
    }
  },
  // 根据ID获取记录
  getRecordById(id) {
    return state.records.find((record) => record.id === id);
  },
  // 加载分类数据
  loadCategories() {
    try {
      const categories = utils_storage.Storage.getCategories();
      state.categories = categories;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:176", "加载分类失败:", error);
      utils_helpers.showToast("加载分类失败");
    }
  },
  // 保存分类数据
  saveCategories() {
    try {
      utils_storage.Storage.setCategories(state.categories);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:187", "保存分类失败:", error);
      utils_helpers.showToast("保存分类失败");
      return false;
    }
  },
  // 根据ID获取分类
  getCategoryById(id, type = "expense") {
    return state.categories[type].find((category) => category.id === id);
  },
  // 获取默认分类
  getDefaultCategories() {
    return utils_storage.Storage.getDefaultCategories();
  },
  // 加载设置
  loadSettings() {
    try {
      state.settings = utils_storage.Storage.getSettings();
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:208", "加载设置失败:", error);
      utils_helpers.showToast("加载设置失败");
    }
  },
  // 保存设置
  saveSettings() {
    try {
      utils_storage.Storage.setSettings(state.settings);
      utils_helpers.showToast("设置保存成功", "success");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:220", "保存设置失败:", error);
      utils_helpers.showToast("保存设置失败");
      return false;
    }
  },
  // 更新设置
  updateSettings(newSettings) {
    Object.assign(state.settings, newSettings);
    this.saveSettings();
  },
  // 设置日期范围
  setDateRange(start, end) {
    state.dateRange.start = start;
    state.dateRange.end = end;
  },
  // 清空日期范围
  clearDateRange() {
    state.dateRange.start = "";
    state.dateRange.end = "";
  },
  // 设置加载状态
  setLoading(loading) {
    state.loading = loading;
  }
};
const store = {
  state,
  getters,
  actions
};
exports.store = store;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/index.js.map
