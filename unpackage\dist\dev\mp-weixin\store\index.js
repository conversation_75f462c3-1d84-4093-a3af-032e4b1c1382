"use strict";
const common_vendor = require("../common/vendor.js");
const utils_storage = require("../utils/storage.js");
const utils_cloudStorage = require("../utils/cloud-storage.js");
const utils_models = require("../utils/models.js");
const utils_helpers = require("../utils/helpers.js");
const state = common_vendor.reactive({
  // 记账记录
  records: [],
  // 分类数据
  categories: {
    expense: [],
    income: []
  },
  // 用户设置
  settings: {},
  // 当前选中的日期范围
  dateRange: {
    start: "",
    end: ""
  },
  // 加载状态
  loading: false,
  // 云端同步状态
  syncing: false,
  // 是否使用云端存储
  useCloudStorage: true,
  // 最后同步时间
  lastSyncTime: null,
  // 网络状态
  isOnline: true
});
const getters = {
  // 获取所有记录
  allRecords: common_vendor.computed(() => state.records),
  // 获取支出记录
  expenseRecords: common_vendor.computed(
    () => state.records.filter((record) => record.type === "expense")
  ),
  // 获取收入记录
  incomeRecords: common_vendor.computed(
    () => state.records.filter((record) => record.type === "income")
  ),
  // 获取支出分类
  expenseCategories: common_vendor.computed(() => state.categories.expense),
  // 获取收入分类
  incomeCategories: common_vendor.computed(() => state.categories.income),
  // 获取统计数据
  statistics: common_vendor.computed(() => new utils_models.Statistics(state.records)),
  // 获取总收入
  totalIncome: common_vendor.computed(
    () => state.records.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
  ),
  // 获取总支出
  totalExpense: common_vendor.computed(
    () => state.records.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
  ),
  // 获取净收入
  netIncome: common_vendor.computed(() => getters.totalIncome.value - getters.totalExpense.value),
  // 根据日期范围过滤记录
  filteredRecords: common_vendor.computed(() => {
    if (!state.dateRange.start || !state.dateRange.end) {
      return state.records;
    }
    return state.records.filter(
      (record) => record.date >= state.dateRange.start && record.date <= state.dateRange.end
    );
  })
};
const actions = {
  // 初始化数据
  async init() {
    try {
      state.isOnline = await utils_cloudStorage.cloudStorage.isOnline();
      if (state.useCloudStorage && state.isOnline) {
        await this.initCloudMode();
      } else {
        await this.initLocalMode();
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:107", "初始化失败:", error);
      await this.initLocalMode();
    }
  },
  // 初始化云端模式
  async initCloudMode() {
    try {
      state.loading = true;
      await utils_cloudStorage.cloudStorage.init();
      await utils_cloudStorage.cloudStorage.handleOfflineData();
      await this.loadCloudData();
      state.lastSyncTime = /* @__PURE__ */ new Date();
      common_vendor.index.setStorageSync("lastSyncTime", state.lastSyncTime);
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:132", "云端模式初始化失败:", error);
      throw error;
    } finally {
      state.loading = false;
    }
  },
  // 初始化本地模式
  async initLocalMode() {
    try {
      state.loading = true;
      state.useCloudStorage = false;
      this.loadRecords();
      this.loadCategories();
      this.loadSettings();
    } finally {
      state.loading = false;
    }
  },
  // 加载云端数据
  async loadCloudData() {
    try {
      const [recordsResult, categoriesResult, settingsResult] = await Promise.all([
        utils_cloudStorage.cloudStorage.getRecords(),
        utils_cloudStorage.cloudStorage.getCategories(),
        utils_cloudStorage.cloudStorage.getUserSettings()
      ]);
      if (recordsResult.success) {
        state.records = recordsResult.data.records.map((record) => new utils_models.Record(record));
      }
      if (categoriesResult.success) {
        const categories = categoriesResult.data;
        state.categories = {
          expense: categories.filter((cat) => cat.type === "expense"),
          income: categories.filter((cat) => cat.type === "income")
        };
      }
      if (settingsResult.success) {
        state.settings = settingsResult.data || utils_cloudStorage.cloudStorage.getDefaultSettings();
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:184", "加载云端数据失败:", error);
      throw error;
    }
  },
  // 加载记账记录
  loadRecords() {
    try {
      const records = utils_storage.Storage.getRecords();
      state.records = records.map((record) => new utils_models.Record(record));
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:195", "加载记录失败:", error);
      utils_helpers.showToast("加载记录失败");
    }
  },
  // 保存记账记录
  saveRecords() {
    try {
      const records = state.records.map((record) => record.toObject());
      utils_storage.Storage.setRecords(records);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:207", "保存记录失败:", error);
      utils_helpers.showToast("保存记录失败");
      return false;
    }
  },
  // 添加记录
  async addRecord(recordData) {
    try {
      const record = new utils_models.Record(recordData);
      if (state.useCloudStorage && state.isOnline) {
        const result = await utils_cloudStorage.cloudStorage.addRecord(record.toObject());
        if (result.success) {
          record.id = result.data.id || record.id;
          state.records.unshift(record);
          utils_helpers.showToast("记录添加成功", "success");
          return record;
        } else {
          throw new Error(result.error);
        }
      } else {
        state.records.unshift(record);
        this.saveRecords();
        if (state.useCloudStorage && !state.isOnline) {
          utils_cloudStorage.cloudStorage.saveOfflineRecord(record.toObject(), "add");
          utils_helpers.showToast("记录已保存，将在联网后同步", "success");
        } else {
          utils_helpers.showToast("记录添加成功", "success");
        }
        return record;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:245", "添加记录失败:", error);
      utils_helpers.showToast("添加记录失败");
      return null;
    }
  },
  // 更新记录
  async updateRecord(id, recordData) {
    try {
      const index = state.records.findIndex((record) => record.id === id);
      if (index === -1) {
        throw new Error("记录不存在");
      }
      if (state.useCloudStorage && state.isOnline) {
        const result = await utils_cloudStorage.cloudStorage.updateRecord(id, recordData);
        if (result.success) {
          state.records[index].update(recordData);
          utils_helpers.showToast("记录更新成功", "success");
          return state.records[index];
        } else {
          throw new Error(result.error);
        }
      } else {
        state.records[index].update(recordData);
        this.saveRecords();
        if (state.useCloudStorage && !state.isOnline) {
          utils_cloudStorage.cloudStorage.saveOfflineRecord({ id, ...recordData }, "update");
          utils_helpers.showToast("记录已更新，将在联网后同步", "success");
        } else {
          utils_helpers.showToast("记录更新成功", "success");
        }
        return state.records[index];
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:284", "更新记录失败:", error);
      utils_helpers.showToast("更新记录失败");
      return null;
    }
  },
  // 删除记录
  async deleteRecord(id) {
    try {
      const index = state.records.findIndex((record) => record.id === id);
      if (index === -1) {
        throw new Error("记录不存在");
      }
      if (state.useCloudStorage && state.isOnline) {
        const result = await utils_cloudStorage.cloudStorage.deleteRecord(id);
        if (result.success) {
          state.records.splice(index, 1);
          utils_helpers.showToast("记录删除成功", "success");
          return true;
        } else {
          throw new Error(result.error);
        }
      } else {
        const deletedRecord = state.records[index];
        state.records.splice(index, 1);
        this.saveRecords();
        if (state.useCloudStorage && !state.isOnline) {
          utils_cloudStorage.cloudStorage.saveOfflineRecord({ id }, "delete");
          utils_helpers.showToast("记录已删除，将在联网后同步", "success");
        } else {
          utils_helpers.showToast("记录删除成功", "success");
        }
        return true;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:324", "删除记录失败:", error);
      utils_helpers.showToast("删除记录失败");
      return false;
    }
  },
  // 根据ID获取记录
  getRecordById(id) {
    return state.records.find((record) => record.id === id);
  },
  // 加载分类数据
  loadCategories() {
    try {
      const categories = utils_storage.Storage.getCategories();
      state.categories = categories;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:341", "加载分类失败:", error);
      utils_helpers.showToast("加载分类失败");
    }
  },
  // 保存分类数据
  saveCategories() {
    try {
      utils_storage.Storage.setCategories(state.categories);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:352", "保存分类失败:", error);
      utils_helpers.showToast("保存分类失败");
      return false;
    }
  },
  // 根据ID获取分类
  getCategoryById(id, type = "expense") {
    return state.categories[type].find((category) => category.id === id);
  },
  // 获取默认分类
  getDefaultCategories() {
    return utils_storage.Storage.getDefaultCategories();
  },
  // 加载设置
  loadSettings() {
    try {
      state.settings = utils_storage.Storage.getSettings();
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:373", "加载设置失败:", error);
      utils_helpers.showToast("加载设置失败");
    }
  },
  // 保存设置
  saveSettings() {
    try {
      utils_storage.Storage.setSettings(state.settings);
      utils_helpers.showToast("设置保存成功", "success");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:385", "保存设置失败:", error);
      utils_helpers.showToast("保存设置失败");
      return false;
    }
  },
  // 更新设置
  updateSettings(newSettings) {
    Object.assign(state.settings, newSettings);
    this.saveSettings();
  },
  // 设置日期范围
  setDateRange(start, end) {
    state.dateRange.start = start;
    state.dateRange.end = end;
  },
  // 清空日期范围
  clearDateRange() {
    state.dateRange.start = "";
    state.dateRange.end = "";
  },
  // 设置加载状态
  setLoading(loading) {
    state.loading = loading;
  },
  // 云端同步
  async syncWithCloud() {
    if (!state.useCloudStorage) {
      return { success: false, message: "未启用云端存储" };
    }
    try {
      state.syncing = true;
      state.isOnline = await utils_cloudStorage.cloudStorage.isOnline();
      if (!state.isOnline) {
        throw new Error("网络连接不可用");
      }
      const offlineResult = await utils_cloudStorage.cloudStorage.handleOfflineData();
      const syncResult = await utils_cloudStorage.cloudStorage.syncData(state.lastSyncTime);
      if (syncResult.success) {
        await this.loadCloudData();
        state.lastSyncTime = /* @__PURE__ */ new Date();
        common_vendor.index.setStorageSync("lastSyncTime", state.lastSyncTime);
        utils_helpers.showToast("数据同步成功", "success");
        return { success: true, message: "数据同步成功" };
      } else {
        throw new Error(syncResult.error);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:448", "数据同步失败:", error);
      utils_helpers.showToast("数据同步失败");
      return { success: false, message: error.message };
    } finally {
      state.syncing = false;
    }
  },
  // 切换存储模式
  async switchStorageMode(useCloud = true) {
    try {
      state.loading = true;
      if (useCloud && !state.useCloudStorage) {
        state.useCloudStorage = true;
        await this.initCloudMode();
        utils_helpers.showToast("已切换到云端存储", "success");
      } else if (!useCloud && state.useCloudStorage) {
        state.useCloudStorage = false;
        await this.initLocalMode();
        utils_helpers.showToast("已切换到本地存储", "success");
      }
      common_vendor.index.setStorageSync("useCloudStorage", state.useCloudStorage);
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:477", "切换存储模式失败:", error);
      utils_helpers.showToast("切换存储模式失败");
    } finally {
      state.loading = false;
    }
  },
  // 导出数据
  async exportData() {
    try {
      if (state.useCloudStorage && state.isOnline) {
        const result = await utils_cloudStorage.cloudStorage.exportData();
        if (result.success) {
          return result.data;
        } else {
          throw new Error(result.error);
        }
      } else {
        return {
          records: state.records.map((record) => record.toObject()),
          categories: state.categories,
          settings: state.settings,
          exportTime: /* @__PURE__ */ new Date(),
          version: "1.0.0"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:505", "导出数据失败:", error);
      utils_helpers.showToast("导出数据失败");
      return null;
    }
  },
  // 导入数据
  async importData(data) {
    try {
      if (state.useCloudStorage && state.isOnline) {
        const result = await utils_cloudStorage.cloudStorage.importData(data);
        if (result.success) {
          await this.loadCloudData();
          utils_helpers.showToast("数据导入成功", "success");
          return true;
        } else {
          throw new Error(result.error);
        }
      } else {
        if (data.records) {
          state.records = data.records.map((record) => new utils_models.Record(record));
        }
        if (data.categories) {
          state.categories = data.categories;
        }
        if (data.settings) {
          state.settings = data.settings;
        }
        this.saveRecords();
        this.saveCategories();
        this.saveSettings();
        utils_helpers.showToast("数据导入成功", "success");
        return true;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:545", "导入数据失败:", error);
      utils_helpers.showToast("导入数据失败");
      return false;
    }
  },
  // 清空所有数据
  async clearAllData() {
    try {
      state.records = [];
      state.categories = { expense: [], income: [] };
      state.settings = {};
      if (state.useCloudStorage && state.isOnline) {
        const recordsResult = await utils_cloudStorage.cloudStorage.getRecords();
        if (recordsResult.success) {
          for (const record of recordsResult.data.records) {
            await utils_cloudStorage.cloudStorage.deleteRecord(record._id);
          }
        }
      } else {
        common_vendor.index.removeStorageSync("bookkeeping_records");
        common_vendor.index.removeStorageSync("bookkeeping_categories");
        common_vendor.index.removeStorageSync("bookkeeping_settings");
      }
      utils_helpers.showToast("数据清空成功", "success");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/index.js:577", "清空数据失败:", error);
      utils_helpers.showToast("清空数据失败");
      return false;
    }
  }
};
const store = {
  state,
  getters,
  actions
};
exports.store = store;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/index.js.map
