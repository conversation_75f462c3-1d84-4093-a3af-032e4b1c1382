<template>
  <view class="home-page">
    <!-- 顶部统计卡片 -->
    <view class="stats-card fade-in">
      <view class="stats-header">
        <text class="stats-title">本月概览</text>
        <text class="stats-date">{{ currentMonth }}</text>
      </view>

      <view class="stats-content">
        <view class="stats-item income">
          <view class="stats-label">收入</view>
          <view class="stats-amount">{{ formatAmount(monthlyIncome) }}</view>
        </view>

        <view class="stats-divider"></view>

        <view class="stats-item expense">
          <view class="stats-label">支出</view>
          <view class="stats-amount">{{ formatAmount(monthlyExpense) }}</view>
        </view>

        <view class="stats-divider"></view>

        <view class="stats-item balance">
          <view class="stats-label">结余</view>
          <view class="stats-amount" :class="{ 'negative': monthlyBalance < 0 }">
            {{ formatAmount(monthlyBalance) }}
          </view>
        </view>
      </view>
    </view>

    <!-- 快速记账按钮 -->
    <view class="quick-actions slide-in-up">
      <view class="quick-btn expense-btn" @click="quickAdd('expense')">
        <view class="quick-icon">💸</view>
        <text class="quick-text">支出</text>
      </view>

      <view class="quick-btn income-btn" @click="quickAdd('income')">
        <view class="quick-icon">💰</view>
        <text class="quick-text">收入</text>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="recent-section bounce-in">
      <view class="section-header">
        <text class="section-title">最近记录</text>
        <text class="section-more" @click="goToList">查看全部</text>
      </view>

      <view class="recent-list" v-if="recentRecords.length > 0">
        <view
          class="record-item"
          v-for="record in recentRecords"
          :key="record.id"
          @click="editRecord(record)"
        >
          <view class="record-left">
            <view class="record-icon" :style="{ backgroundColor: record.categoryColor }">
              {{ record.categoryIcon }}
            </view>
            <view class="record-info">
              <text class="record-category">{{ record.categoryName }}</text>
              <text class="record-note" v-if="record.note">{{ record.note }}</text>
              <text class="record-time">{{ formatDateTime(record.date, record.time) }}</text>
            </view>
          </view>

          <view class="record-right">
            <text class="record-amount" :class="record.type">
              {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}
            </text>
          </view>
        </view>
      </view>

      <view class="empty-state" v-else>
        <view class="empty-icon">📝</view>
        <text class="empty-text">暂无记录</text>
        <text class="empty-desc">点击下方按钮开始记账吧</text>
      </view>
    </view>
  </view>
</template>

<script>
import { computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/helpers.js'

export default {
  name: 'HomePage',
  setup() {
    // 当前月份
    const currentMonth = computed(() => {
      const now = new Date()
      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`
    })

    // 本月记录
    const monthlyRecords = computed(() => {
      const start = getFirstDayOfMonth()
      const end = getLastDayOfMonth()
      return store.state.records.filter(record =>
        record.date >= start && record.date <= end
      )
    })

    // 本月收入
    const monthlyIncome = computed(() =>
      monthlyRecords.value
        .filter(record => record.type === 'income')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 本月支出
    const monthlyExpense = computed(() =>
      monthlyRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 本月结余
    const monthlyBalance = computed(() => monthlyIncome.value - monthlyExpense.value)

    // 最近记录（最多显示5条）
    const recentRecords = computed(() =>
      store.state.records
        .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))
        .slice(0, 5)
    )

    // 格式化日期时间
    const formatDateTime = (date, time) => {
      const today = formatDate(new Date())
      const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))

      if (date === today) {
        return `今天 ${time}`
      } else if (date === yesterday) {
        return `昨天 ${time}`
      } else {
        return `${formatDate(date, 'MM-DD')} ${time}`
      }
    }

    // 快速记账
    const quickAdd = (type) => {
      uni.navigateTo({
        url: `/pages/add/add?type=${type}`
      })
    }

    // 编辑记录
    const editRecord = (record) => {
      uni.navigateTo({
        url: `/pages/add/add?id=${record.id}`
      })
    }

    // 跳转到账单列表
    const goToList = () => {
      uni.switchTab({
        url: '/pages/list/list'
      })
    }

    onMounted(() => {
      // 页面加载时刷新数据
      store.actions.loadRecords()
    })

    return {
      currentMonth,
      monthlyIncome,
      monthlyExpense,
      monthlyBalance,
      recentRecords,
      formatAmount,
      formatDateTime,
      quickAdd,
      editRecord,
      goToList
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  padding: 16px;
  background-color: $uni-bg-color-grey;
  min-height: 100vh;
}

/* 统计卡片 */
.stats-card {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.stats-date {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.stats-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 8px;
}

.stats-amount {
  color: white;
  font-size: 20px;
  font-weight: bold;

  &.negative {
    color: #FFE0E0;
  }
}

.stats-divider {
  width: 1px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 16px;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.quick-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.expense-btn {
  border-left: 4px solid #F44336;
}

.income-btn {
  border-left: 4px solid #4CAF50;
}

.quick-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.quick-text {
  font-size: 16px;
  font-weight: 500;
  color: $uni-text-color;
}

/* 最近记录 */
.recent-section {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid $uni-border-color;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: $uni-text-color;
}

.section-more {
  font-size: 14px;
  color: $uni-color-primary;
}

.recent-list {
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid $uni-border-color;
    transition: background-color 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: $uni-bg-color-hover;
    }
  }
}

.record-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.record-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}

.record-info {
  flex: 1;
}

.record-category {
  font-size: 16px;
  font-weight: 500;
  color: $uni-text-color;
  display: block;
  margin-bottom: 4px;
}

.record-note {
  font-size: 12px;
  color: $uni-text-color-grey;
  display: block;
  margin-bottom: 2px;
}

.record-time {
  font-size: 12px;
  color: $uni-text-color-placeholder;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 16px;
  font-weight: 600;

  &.income {
    color: $income-color;
  }

  &.expense {
    color: $expense-color;
  }
}

/* 空状态 */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: $uni-text-color-grey;
  margin-bottom: 8px;
  display: block;
}

.empty-desc {
  font-size: 14px;
  color: $uni-text-color-placeholder;
}
</style>
