<template>
  <view class="home-page">
    <!-- 顶部问候语 -->
    <view class="greeting-section">
      <view class="greeting-content">
        <text class="greeting-title">{{ greetingText }}</text>
        <text class="greeting-subtitle">{{ currentDateText }}</text>
      </view>
      <view class="profile-section">
        <view class="sync-status" :class="{ syncing: state.syncing, offline: !state.isOnline }">
          <text class="sync-icon">{{ syncStatusIcon }}</text>
        </view>
        <!-- 开发测试按钮 -->
        <view class="test-btn" @click="handleTestCloud" v-if="isDev">
          <text class="test-text">测试</text>
        </view>
        <!-- 测试页面按钮 -->
        <view class="test-page-btn" @click="goToTestPage" v-if="isDev">
          <text class="test-text">测试页</text>
        </view>
      </view>
    </view>

    <!-- 主要统计卡片 -->
    <view class="main-stats-card slide-in-down">
      <view class="balance-section">
        <view class="balance-header">
          <text class="balance-label">本月结余</text>
          <view class="trend-indicator" :class="balanceTrend">
            <text class="trend-icon">{{ balanceTrendIcon }}</text>
          </view>
        </view>
        <view class="balance-amount">
          <text class="currency">¥</text>
          <text class="amount" :class="{ negative: monthlyBalance < 0 }">
            {{ formatAmount(Math.abs(monthlyBalance)) }}
          </text>
        </view>
        <view class="balance-change" v-if="balanceChange !== 0">
          <text class="change-text" :class="{ positive: balanceChange > 0, negative: balanceChange < 0 }">
            {{ balanceChange > 0 ? '+' : '' }}{{ formatAmount(Math.abs(balanceChange)) }}
          </text>
          <text class="change-label">较上月</text>
        </view>
      </view>
    </view>

    <!-- 收支统计卡片 -->
    <view class="income-expense-cards">
      <view class="stat-card income-card slide-in-left">
        <view class="card-header">
          <view class="card-icon income-icon">📈</view>
          <text class="card-title">收入</text>
        </view>
        <view class="card-amount income">
          <text class="amount-text">{{ formatAmount(monthlyIncome) }}</text>
        </view>
        <view class="card-progress">
          <view class="progress-bar">
            <view class="progress-fill income-progress" :style="{ width: incomeProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ incomeTransactions }}笔交易</text>
        </view>
      </view>

      <view class="stat-card expense-card slide-in-right">
        <view class="card-header">
          <view class="card-icon expense-icon">📉</view>
          <text class="card-title">支出</text>
        </view>
        <view class="card-amount expense">
          <text class="amount-text">{{ formatAmount(monthlyExpense) }}</text>
        </view>
        <view class="card-progress">
          <view class="progress-bar">
            <view class="progress-fill expense-progress" :style="{ width: expenseProgress + '%' }"></view>
          </view>
          <text class="progress-text">{{ expenseTransactions }}笔交易</text>
        </view>
      </view>
    </view>

    <!-- 快速操作区域 -->
    <view class="quick-actions-section">
      <view class="section-title">
        <text class="title-text">快速记账</text>
        <text class="title-subtitle">点击快速添加收支记录</text>
      </view>

      <view class="quick-actions bounce-in">
        <view class="quick-btn expense-btn" @click="quickAdd('expense')">
          <view class="btn-content">
            <view class="btn-icon expense-icon">💸</view>
            <view class="btn-text">
              <text class="btn-title">记支出</text>
              <text class="btn-subtitle">日常消费</text>
            </view>
          </view>
          <view class="btn-arrow">→</view>
        </view>

        <view class="quick-btn income-btn" @click="quickAdd('income')">
          <view class="btn-content">
            <view class="btn-icon income-icon">💰</view>
            <view class="btn-text">
              <text class="btn-title">记收入</text>
              <text class="btn-subtitle">工资奖金</text>
            </view>
          </view>
          <view class="btn-arrow">→</view>
        </view>

        <view class="quick-btn ai-btn" @click="openAIRecord">
          <view class="btn-content">
            <view class="btn-icon ai-icon">🤖</view>
            <view class="btn-text">
              <text class="btn-title">AI记账</text>
              <text class="btn-subtitle">语音图片智能识别</text>
            </view>
          </view>
          <view class="btn-arrow">→</view>
        </view>
      </view>
    </view>

    <!-- 最近记录 -->
    <view class="recent-section fade-in-up">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">最近记录</text>
          <text class="section-subtitle">{{ recentRecords.length }}条记录</text>
        </view>
        <view class="header-right" @click="goToList">
          <text class="more-text">查看全部</text>
          <text class="more-arrow">→</text>
        </view>
      </view>

      <view class="recent-list" v-if="recentRecords.length > 0">
        <view
          class="record-item"
          v-for="(record, index) in recentRecords"
          :key="record.id"
          :style="{ animationDelay: (index * 0.1) + 's' }"
          @click="editRecord(record)"
        >
          <view class="record-left">
            <view class="record-icon-wrapper">
              <view class="record-icon" :style="{ backgroundColor: record.categoryColor }">
                <text class="icon-text">{{ record.categoryIcon }}</text>
              </view>
              <view class="type-indicator" :class="record.type"></view>
            </view>
            <view class="record-info">
              <text class="record-category">{{ record.categoryName }}</text>
              <text class="record-note" v-if="record.note">{{ record.note }}</text>
              <text class="record-time">{{ formatDateTime(record.date, record.time) }}</text>
            </view>
          </view>

          <view class="record-right">
            <text class="record-amount" :class="record.type">
              {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}
            </text>
            <view class="edit-indicator">
              <text class="edit-icon">✏️</text>
            </view>
          </view>
        </view>
      </view>

      <view class="empty-state" v-else>
        <view class="empty-icon">📝</view>
        <text class="empty-text">暂无记录</text>
        <text class="empty-desc">点击下方按钮开始记账吧</text>
      </view>
    </view>
  </view>
</template>

<script>
import { computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/helpers.js'
import { testCloudFunction, testCloudConnection } from '../../utils/test-cloud.js'

export default {
  name: 'HomePage',
  setup() {
    // 响应式状态
    const state = computed(() => store.state)

    // 问候语
    const greetingText = computed(() => {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了'
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    })

    // 当前日期文本
    const currentDateText = computed(() => {
      const now = new Date()
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return `${now.getMonth() + 1}月${now.getDate()}日 ${weekdays[now.getDay()]}`
    })

    // 同步状态图标
    const syncStatusIcon = computed(() => {
      if (state.value.syncing) return '🔄'
      if (!state.value.isOnline) return '📴'
      if (state.value.useCloudStorage) return '☁️'
      return '📱'
    })

    // 当前月份
    const currentMonth = computed(() => {
      const now = new Date()
      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`
    })

    // 本月记录
    const monthlyRecords = computed(() => {
      const start = getFirstDayOfMonth()
      const end = getLastDayOfMonth()
      return store.state.records.filter(record =>
        record.date >= start && record.date <= end
      )
    })

    // 上月记录（用于对比）
    const lastMonthRecords = computed(() => {
      const now = new Date()
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const start = formatDate(lastMonth)
      const end = formatDate(new Date(now.getFullYear(), now.getMonth(), 0))
      return store.state.records.filter(record =>
        record.date >= start && record.date <= end
      )
    })

    // 本月收入
    const monthlyIncome = computed(() =>
      monthlyRecords.value
        .filter(record => record.type === 'income')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 本月支出
    const monthlyExpense = computed(() =>
      monthlyRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 本月结余
    const monthlyBalance = computed(() => monthlyIncome.value - monthlyExpense.value)

    // 上月结余
    const lastMonthBalance = computed(() => {
      const income = lastMonthRecords.value
        .filter(record => record.type === 'income')
        .reduce((total, record) => total + record.amount, 0)
      const expense = lastMonthRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
      return income - expense
    })

    // 结余变化
    const balanceChange = computed(() => monthlyBalance.value - lastMonthBalance.value)

    // 结余趋势
    const balanceTrend = computed(() => {
      if (balanceChange.value > 0) return 'up'
      if (balanceChange.value < 0) return 'down'
      return 'stable'
    })

    // 趋势图标
    const balanceTrendIcon = computed(() => {
      if (balanceChange.value > 0) return '📈'
      if (balanceChange.value < 0) return '📉'
      return '➖'
    })

    // 收入交易数量
    const incomeTransactions = computed(() =>
      monthlyRecords.value.filter(record => record.type === 'income').length
    )

    // 支出交易数量
    const expenseTransactions = computed(() =>
      monthlyRecords.value.filter(record => record.type === 'expense').length
    )

    // 收入进度（相对于最大值）
    const incomeProgress = computed(() => {
      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)
      return maxAmount > 0 ? (monthlyIncome.value / maxAmount) * 100 : 0
    })

    // 支出进度（相对于最大值）
    const expenseProgress = computed(() => {
      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)
      return maxAmount > 0 ? (monthlyExpense.value / maxAmount) * 100 : 0
    })

    // 最近记录（最多显示5条）
    const recentRecords = computed(() =>
      store.state.records
        .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))
        .slice(0, 5)
    )

    // 格式化日期时间
    const formatDateTime = (date, time) => {
      const today = formatDate(new Date())
      const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))

      if (date === today) {
        return `今天 ${time}`
      } else if (date === yesterday) {
        return `昨天 ${time}`
      } else {
        return `${formatDate(date, 'MM-DD')} ${time}`
      }
    }

    // 快速记账
    const quickAdd = (type) => {
      // 由于记账页面是tabbar页面，需要使用switchTab
      // 但switchTab不支持传参，所以先存储类型到本地
      uni.setStorageSync('quickAddType', type)
      uni.switchTab({
        url: '/pages/add/add'
      })
    }

    // 打开AI记账
    const openAIRecord = () => {
      uni.navigateTo({
        url: '/pages/ai-record/ai-record'
      })
    }

    // 编辑记录
    const editRecord = (record) => {
      // 编辑记录需要传递参数，但switchTab不支持传参
      // 先存储记录ID到本地，然后跳转
      uni.setStorageSync('editRecordId', record.id)
      uni.switchTab({
        url: '/pages/add/add'
      })
    }

    // 跳转到账单列表
    const goToList = () => {
      // list页面不是tabbar页面，使用navigateTo
      uni.navigateTo({
        url: '/pages/list/list'
      })
    }

    // 测试云函数
    const handleTestCloud = async () => {
      uni.showLoading({
        title: '测试中...'
      })

      try {
        const result = await testCloudFunction()
        uni.hideLoading()

        if (result.success) {
          uni.showToast({
            title: '测试成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: '测试失败',
            icon: 'error'
          })
        }
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '测试出错',
          icon: 'error'
        })
        console.error('测试云函数失败:', error)
      }
    }

    // 跳转到测试页面
    const goToTestPage = () => {
      uni.navigateTo({
        url: '/pages/test/test'
      })
    }

    // 开发模式检测
    const isDev = computed(() => {
      // 在开发环境下显示测试按钮
      return process.env.NODE_ENV === 'development' || true // 临时设为true方便测试
    })

    onMounted(() => {
      // 页面加载时刷新数据
      store.actions.loadRecords()
    })

    return {
      // 状态
      state,
      // 问候语
      greetingText,
      currentDateText,
      syncStatusIcon,
      // 统计数据
      currentMonth,
      monthlyIncome,
      monthlyExpense,
      monthlyBalance,
      balanceChange,
      balanceTrend,
      balanceTrendIcon,
      incomeTransactions,
      expenseTransactions,
      incomeProgress,
      expenseProgress,
      // 记录列表
      recentRecords,
      // 工具函数
      formatAmount,
      formatDateTime,
      // 操作方法
      quickAdd,
      openAIRecord,
      editRecord,
      goToList,
      handleTestCloud,
      goToTestPage,
      // 开发相关
      isDev
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  padding: 0;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);
  min-height: 100vh;
}

/* 问候语区域 */
.greeting-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 16px;
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
}

.greeting-content {
  flex: 1;
}

.greeting-title {
  font-size: 24px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.greeting-subtitle {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}

.profile-section {
  display: flex;
  align-items: center;
}

.sync-status {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.sync-status.syncing {
  animation: spin 1s linear infinite;
}

.sync-status.offline {
  background: rgba(255, 87, 87, 0.3);
}

.sync-icon {
  font-size: 18px;
}

/* 主要统计卡片 */
.main-stats-card {
  margin: -20px 16px 24px;
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.balance-section {
  text-align: center;
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  gap: 8px;
}

.balance-label {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.trend-indicator {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.trend-indicator.up {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.trend-indicator.down {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.trend-indicator.stable {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.trend-icon {
  font-size: 14px;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8px;
}

.currency {
  font-size: 20px;
  color: #666;
  margin-right: 4px;
}

.amount {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  letter-spacing: -1px;
}

.amount.negative {
  color: #f44336;
}

.balance-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.change-text {
  font-size: 14px;
  font-weight: 600;
}

.change-text.positive {
  color: #4CAF50;
}

.change-text.negative {
  color: #f44336;
}

.change-label {
  font-size: 12px;
  color: #999;
}

/* 收支统计卡片 */
.income-expense-cards {
  display: flex;
  gap: 12px;
  padding: 0 16px;
  margin-bottom: 24px;
}

.stat-card {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card:active {
  transform: scale(0.98);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.card-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.income-icon {
  background: rgba(76, 175, 80, 0.1);
}

.expense-icon {
  background: rgba(244, 67, 54, 0.1);
}

.card-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-amount {
  margin-bottom: 12px;
}

.amount-text {
  font-size: 20px;
  font-weight: 700;
  display: block;
}

.income .amount-text {
  color: #4CAF50;
}

.expense .amount-text {
  color: #f44336;
}

.card-progress {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-bar {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.income-progress {
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
}

.expense-progress {
  background: linear-gradient(90deg, #f44336, #EF5350);
}

.progress-text {
  font-size: 12px;
  color: #999;
}

/* 快速操作区域 */
.quick-actions-section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.section-title {
  margin-bottom: 16px;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.title-subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-btn {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.quick-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.quick-btn:active::before {
  left: 100%;
}

.quick-btn:active {
  transform: scale(0.98);
}

.expense-btn {
  border-left: 4px solid #f44336;
}

.income-btn {
  border-left: 4px solid #4CAF50;
}

.ai-btn {
  border-left: 4px solid #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.btn-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.expense-icon {
  background: rgba(244, 67, 54, 0.1);
}

.income-icon {
  background: rgba(76, 175, 80, 0.1);
}

.ai-icon {
  background: rgba(102, 126, 234, 0.1);
}

.btn-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.btn-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}

.btn-subtitle {
  font-size: 12px;
  color: #666;
  display: block;
}

.btn-arrow {
  font-size: 18px;
  color: #ccc;
  font-weight: bold;
}

/* 最近记录区域 */
.recent-section {
  padding: 0 16px;
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 12px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 20px;
  background: rgba(76, 175, 80, 0.1);
  transition: all 0.3s ease;
}

.header-right:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}

.more-text {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}

.more-arrow {
  font-size: 12px;
  color: #4CAF50;
  font-weight: bold;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  animation: slideInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.record-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.record-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.record-icon-wrapper {
  position: relative;
}

.record-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 20px;
  color: white;
}

.type-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
  border: 2px solid white;
}

.type-indicator.income {
  background: #4CAF50;
}

.type-indicator.expense {
  background: #f44336;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.record-category {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}

.record-note {
  font-size: 12px;
  color: #666;
  display: block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.record-time {
  font-size: 12px;
  color: #999;
  display: block;
}

.record-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.record-amount {
  font-size: 16px;
  font-weight: 700;
}

.record-amount.income {
  color: #4CAF50;
}

.record-amount.expense {
  color: #f44336;
}

.edit-indicator {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.record-item:active .edit-indicator {
  opacity: 1;
}

.edit-icon {
  font-size: 12px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.empty-hint {
  font-size: 14px;
  color: #999;
  display: block;
}

/* 动画效果 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 应用动画 */
.slide-in-down {
  animation: slideInDown 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out 0.2s both;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out 0.3s both;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out 0.4s both;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* 测试按钮样式 */
.test-btn, .test-page-btn {
  padding: 6px 12px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 12px;
  margin-left: 8px;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.test-page-btn {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.test-btn:active, .test-page-btn:active {
  background: rgba(255, 193, 7, 0.2);
  transform: scale(0.95);
}

.test-page-btn:active {
  background: rgba(33, 150, 243, 0.2);
}

.test-text {
  font-size: 12px;
  color: #FFC107;
  font-weight: 500;
}

.test-page-btn .test-text {
  color: #2196F3;
}
</style>
