"use strict";
const common_vendor = require("../common/vendor.js");
class CloudStorage {
  constructor() {
    this.isLoggedIn = false;
    this.userId = null;
    this.init();
  }
  // 初始化
  async init() {
    try {
      const loginRes = await common_vendor.nr.getCurrentUserInfo();
      if (loginRes.uid) {
        this.isLoggedIn = true;
        this.userId = loginRes.uid;
      }
    } catch (error) {
      common_vendor.index.__f__("log", "at utils/cloud-storage.js:23", "用户未登录，使用游客模式");
      this.isLoggedIn = false;
    }
  }
  // 用户登录
  async login() {
    try {
      const res = await common_vendor.nr.callFunction({
        name: "uni-id-cf",
        data: {
          action: "loginAnonymously"
        }
      });
      if (res.result.code === 0) {
        this.isLoggedIn = true;
        this.userId = res.result.uid;
        common_vendor.index.setStorageSync("uni_id_token", res.result.token);
        common_vendor.index.setStorageSync("uni_id_token_expired", res.result.tokenExpired);
        return {
          success: true,
          data: res.result
        };
      } else {
        throw new Error(res.result.msg || "登录失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:54", "登录失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  // 调用云函数
  async callFunction(action, data = {}) {
    try {
      if (!this.isLoggedIn) {
        const loginResult = await this.login();
        if (!loginResult.success) {
          throw new Error("登录失败");
        }
      }
      const res = await common_vendor.nr.callFunction({
        name: "bookkeeping",
        data: {
          action,
          data
        }
      });
      if (res.result.code === 200) {
        return {
          success: true,
          data: res.result.data
        };
      } else {
        throw new Error(res.result.message || "操作失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:90", `云函数调用失败 [${action}]:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  // 记录相关操作
  async addRecord(record) {
    return await this.callFunction("addRecord", record);
  }
  async updateRecord(id, record) {
    return await this.callFunction("updateRecord", { id, ...record });
  }
  async deleteRecord(id) {
    return await this.callFunction("deleteRecord", { id });
  }
  async getRecords(params = {}) {
    return await this.callFunction("getRecords", params);
  }
  async getRecordById(id) {
    return await this.callFunction("getRecordById", { id });
  }
  // 分类相关操作
  async getCategories() {
    return await this.callFunction("getCategories");
  }
  async addCategory(category) {
    return await this.callFunction("addCategory", category);
  }
  async updateCategory(id, category) {
    return await this.callFunction("updateCategory", { id, ...category });
  }
  async deleteCategory(id) {
    return await this.callFunction("deleteCategory", { id });
  }
  // 统计相关操作
  async getStatistics(params = {}) {
    return await this.callFunction("getStatistics", params);
  }
  async getMonthlyStats(year, month) {
    return await this.callFunction("getMonthlyStats", { year, month });
  }
  async getCategoryStats(params = {}) {
    return await this.callFunction("getCategoryStats", params);
  }
  // 用户设置相关操作
  async getUserSettings() {
    return await this.callFunction("getUserSettings");
  }
  async updateUserSettings(settings) {
    return await this.callFunction("updateUserSettings", settings);
  }
  // 数据同步相关操作
  async syncData(lastSyncTime) {
    return await this.callFunction("syncData", { lastSyncTime });
  }
  async exportData() {
    return await this.callFunction("exportData");
  }
  async importData(data) {
    return await this.callFunction("importData", data);
  }
  // 初始化数据库
  async initDatabase() {
    try {
      const res = await common_vendor.nr.callFunction({
        name: "init-db"
      });
      return {
        success: res.result.code === 200,
        message: res.result.message
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:183", "数据库初始化失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  // 获取默认分类（从云端）
  async getDefaultCategories() {
    const result = await this.getCategories();
    if (result.success) {
      const categories = result.data;
      const defaultCategories = {
        expense: categories.filter((cat) => cat.type === "expense" && cat.isDefault),
        income: categories.filter((cat) => cat.type === "income" && cat.isDefault)
      };
      return defaultCategories;
    }
    return { expense: [], income: [] };
  }
  // 获取默认设置
  getDefaultSettings() {
    return {
      currency: "¥",
      monthlyBudget: 0,
      budgetAlert: true,
      theme: "light",
      language: "zh-CN"
    };
  }
  // 离线数据处理
  async handleOfflineData() {
    try {
      const offlineRecords = common_vendor.index.getStorageSync("offline_records") || [];
      const offlineCategories = common_vendor.index.getStorageSync("offline_categories") || [];
      if (offlineRecords.length === 0 && offlineCategories.length === 0) {
        return { success: true, message: "没有离线数据需要同步" };
      }
      for (const record of offlineRecords) {
        if (record._action === "add") {
          await this.addRecord(record);
        } else if (record._action === "update") {
          await this.updateRecord(record._id, record);
        } else if (record._action === "delete") {
          await this.deleteRecord(record._id);
        }
      }
      for (const category of offlineCategories) {
        if (category._action === "add") {
          await this.addCategory(category);
        } else if (category._action === "update") {
          await this.updateCategory(category._id, category);
        } else if (category._action === "delete") {
          await this.deleteCategory(category._id);
        }
      }
      common_vendor.index.removeStorageSync("offline_records");
      common_vendor.index.removeStorageSync("offline_categories");
      return {
        success: true,
        message: `同步完成：${offlineRecords.length}条记录，${offlineCategories.length}个分类`
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:258", "离线数据同步失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  // 保存离线数据
  saveOfflineRecord(record, action = "add") {
    try {
      const offlineRecords = common_vendor.index.getStorageSync("offline_records") || [];
      offlineRecords.push({
        ...record,
        _action: action,
        _timestamp: Date.now()
      });
      common_vendor.index.setStorageSync("offline_records", offlineRecords);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:277", "保存离线记录失败:", error);
    }
  }
  saveOfflineCategory(category, action = "add") {
    try {
      const offlineCategories = common_vendor.index.getStorageSync("offline_categories") || [];
      offlineCategories.push({
        ...category,
        _action: action,
        _timestamp: Date.now()
      });
      common_vendor.index.setStorageSync("offline_categories", offlineCategories);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:291", "保存离线分类失败:", error);
    }
  }
  // 检查网络状态
  isOnline() {
    return new Promise((resolve) => {
      common_vendor.index.getNetworkType({
        success: (res) => {
          resolve(res.networkType !== "none");
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }
}
const cloudStorage = new CloudStorage();
exports.cloudStorage = cloudStorage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/cloud-storage.js.map
