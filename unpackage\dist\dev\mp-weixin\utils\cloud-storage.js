"use strict";
const common_vendor = require("../common/vendor.js");
const CloudStorage = {
  isLoggedIn: false,
  userId: null,
  // 初始化
  async init() {
    try {
      await this.login();
    } catch (error) {
      common_vendor.index.__f__("log", "at utils/cloud-storage.js:16", "初始化失败，使用游客模式");
      this.isLoggedIn = false;
    }
  },
  // 用户登录（简化版，使用设备ID作为用户标识）
  async login() {
    try {
      const deviceInfo = common_vendor.index.getSystemInfoSync();
      const deviceId = deviceInfo.deviceId || deviceInfo.system + "_" + Date.now();
      this.isLoggedIn = true;
      this.userId = deviceId;
      common_vendor.index.setStorageSync("user_id", deviceId);
      return {
        success: true,
        data: { userId: deviceId }
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:39", "登录失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  },
  // 调用云函数
  async callFunction(action, data) {
    try {
      if (!this.isLoggedIn) {
        const loginResult = await this.login();
        if (!loginResult.success) {
          throw new Error("登录失败");
        }
      }
      const res = await common_vendor.nr.callFunction({
        name: "bookkeeping",
        data: {
          action,
          data: data || {}
        }
      });
      if (res.result && res.result.code === 200) {
        return {
          success: true,
          data: res.result.data
        };
      } else {
        throw new Error(res.result ? res.result.message : "操作失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:75", "云函数调用失败 [" + action + "]:", error);
      return {
        success: false,
        error: error.message
      };
    }
  },
  // 记录相关操作
  async addRecord(record) {
    return await this.callFunction("addRecord", record);
  },
  async getRecords(params) {
    return await this.callFunction("getRecords", params);
  },
  // 分类相关操作
  async getCategories() {
    return await this.callFunction("getCategories");
  },
  // 统计相关操作
  async getStatistics(params) {
    return await this.callFunction("getStatistics", params);
  },
  // 初始化数据库
  async initDatabase() {
    try {
      const res = await common_vendor.nr.callFunction({
        name: "init-db"
      });
      return {
        success: res.result && res.result.code === 200,
        message: res.result ? res.result.message : "初始化失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:114", "数据库初始化失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  },
  // 测试云函数连接
  async testConnection() {
    try {
      const res = await common_vendor.nr.callFunction({
        name: "test",
        data: { message: "hello from client" }
      });
      return {
        success: res.result && res.result.code === 200,
        message: res.result ? res.result.message : "连接失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-storage.js:135", "测试连接失败:", error);
      return {
        success: false,
        error: error.message
      };
    }
  },
  // 用户设置相关操作（简化版）
  async getUserSettings() {
    return {
      success: true,
      data: this.getDefaultSettings()
    };
  },
  // 获取默认设置
  getDefaultSettings() {
    return {
      currency: "¥",
      monthlyBudget: 0,
      budgetAlert: true,
      theme: "light",
      language: "zh-CN"
    };
  },
  // 检查网络状态
  async isOnline() {
    return new Promise((resolve) => {
      common_vendor.index.getNetworkType({
        success: (res) => {
          resolve(res.networkType !== "none");
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },
  // 离线数据处理（简化版）
  async handleOfflineData() {
    return { success: true, message: "没有离线数据需要同步" };
  },
  // 保存离线数据（简化版）
  saveOfflineRecord(record, action) {
    common_vendor.index.__f__("log", "at utils/cloud-storage.js:183", "保存离线记录:", action, record);
  }
};
exports.CloudStorage = CloudStorage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/cloud-storage.js.map
