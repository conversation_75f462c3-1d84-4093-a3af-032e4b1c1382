"use strict";
class Record {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.type = data.type || "expense";
    this.amount = data.amount || 0;
    this.categoryId = data.categoryId || null;
    this.categoryName = data.categoryName || "";
    this.categoryIcon = data.categoryIcon || "";
    this.categoryColor = data.categoryColor || "";
    this.note = data.note || "";
    this.date = data.date || (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
    this.time = data.time || (/* @__PURE__ */ new Date()).toTimeString().split(" ")[0].substring(0, 5);
    this.createTime = data.createTime || Date.now();
    this.updateTime = data.updateTime || Date.now();
  }
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
  // 更新记录
  update(data) {
    Object.keys(data).forEach((key) => {
      if (key !== "id" && key !== "createTime") {
        this[key] = data[key];
      }
    });
    this.updateTime = Date.now();
    return this;
  }
  // 转换为普通对象
  toObject() {
    return {
      id: this.id,
      type: this.type,
      amount: this.amount,
      categoryId: this.categoryId,
      categoryName: this.categoryName,
      categoryIcon: this.categoryIcon,
      categoryColor: this.categoryColor,
      note: this.note,
      date: this.date,
      time: this.time,
      createTime: this.createTime,
      updateTime: this.updateTime
    };
  }
}
class Statistics {
  constructor(records = []) {
    this.records = records;
  }
  // 获取总收入
  getTotalIncome() {
    return this.records.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0);
  }
  // 获取总支出
  getTotalExpense() {
    return this.records.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0);
  }
  // 获取净收入（收入-支出）
  getNetIncome() {
    return this.getTotalIncome() - this.getTotalExpense();
  }
  // 按分类统计
  getStatsByCategory() {
    const stats = {};
    this.records.forEach((record) => {
      const key = `${record.type}_${record.categoryId}`;
      if (!stats[key]) {
        stats[key] = {
          categoryId: record.categoryId,
          categoryName: record.categoryName,
          categoryIcon: record.categoryIcon,
          categoryColor: record.categoryColor,
          type: record.type,
          amount: 0,
          count: 0
        };
      }
      stats[key].amount += record.amount;
      stats[key].count += 1;
    });
    return Object.values(stats);
  }
  // 按日期统计
  getStatsByDate() {
    const stats = {};
    this.records.forEach((record) => {
      if (!stats[record.date]) {
        stats[record.date] = {
          date: record.date,
          income: 0,
          expense: 0,
          count: 0
        };
      }
      if (record.type === "income") {
        stats[record.date].income += record.amount;
      } else {
        stats[record.date].expense += record.amount;
      }
      stats[record.date].count += 1;
    });
    return Object.values(stats).sort((a, b) => new Date(a.date) - new Date(b.date));
  }
  // 按月份统计
  getStatsByMonth() {
    const stats = {};
    this.records.forEach((record) => {
      const month = record.date.substring(0, 7);
      if (!stats[month]) {
        stats[month] = {
          month,
          income: 0,
          expense: 0,
          count: 0
        };
      }
      if (record.type === "income") {
        stats[month].income += record.amount;
      } else {
        stats[month].expense += record.amount;
      }
      stats[month].count += 1;
    });
    return Object.values(stats).sort((a, b) => a.month.localeCompare(b.month));
  }
}
exports.Record = Record;
exports.Statistics = Statistics;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/models.js.map
