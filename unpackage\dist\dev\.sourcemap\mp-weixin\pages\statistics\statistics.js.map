{"version": 3, "file": "statistics.js", "sources": ["pages/statistics/statistics.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3RhdGlzdGljcy9zdGF0aXN0aWNzLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"statistics-page\">\n    <!-- 时间选择 -->\n    <view class=\"time-selector\">\n      <view \n        class=\"time-tab\" \n        v-for=\"tab in timeTabs\" \n        :key=\"tab.value\"\n        :class=\"{ active: currentTimeRange === tab.value }\"\n        @click=\"switchTimeRange(tab.value)\"\n      >\n        <text class=\"tab-text\">{{ tab.label }}</text>\n      </view>\n    </view>\n\n    <!-- 总览卡片 -->\n    <view class=\"overview-card\">\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">总收入</text>\n        <text class=\"overview-value income\">{{ formatAmount(totalIncome) }}</text>\n      </view>\n      <view class=\"overview-divider\"></view>\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">总支出</text>\n        <text class=\"overview-value expense\">{{ formatAmount(totalExpense) }}</text>\n      </view>\n      <view class=\"overview-divider\"></view>\n      <view class=\"overview-item\">\n        <text class=\"overview-label\">净收入</text>\n        <text class=\"overview-value\" :class=\"{ 'negative': netIncome < 0 }\">\n          {{ formatAmount(netIncome) }}\n        </text>\n      </view>\n    </view>\n\n    <!-- 图表区域 -->\n    <view class=\"chart-section\">\n      <view class=\"section-title\">支出分类占比</view>\n      <view class=\"chart-container\">\n        <!-- 这里应该是饼图，暂时用简单的条形图代替 -->\n        <view class=\"simple-chart\">\n          <view \n            class=\"chart-bar\" \n            v-for=\"item in expenseByCategory\" \n            :key=\"item.categoryId\"\n          >\n            <view class=\"bar-info\">\n              <view class=\"bar-icon\" :style=\"{ backgroundColor: item.categoryColor }\">\n                {{ item.categoryIcon }}\n              </view>\n              <text class=\"bar-name\">{{ item.categoryName }}</text>\n              <text class=\"bar-amount\">{{ formatAmount(item.amount) }}</text>\n            </view>\n            <view class=\"bar-container\">\n              <view \n                class=\"bar-fill\" \n                :style=\"{ \n                  width: (item.amount / maxExpenseAmount * 100) + '%',\n                  backgroundColor: item.categoryColor \n                }\"\n              ></view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 趋势分析 -->\n    <view class=\"trend-section\">\n      <view class=\"section-title\">收支趋势</view>\n      <view class=\"trend-chart\">\n        <view \n          class=\"trend-item\" \n          v-for=\"item in trendData\" \n          :key=\"item.date\"\n        >\n          <text class=\"trend-date\">{{ formatTrendDate(item.date) }}</text>\n          <view class=\"trend-bars\">\n            <view class=\"trend-bar income\">\n              <view \n                class=\"trend-bar-fill\" \n                :style=\"{ height: (item.income / maxTrendAmount * 100) + '%' }\"\n              ></view>\n            </view>\n            <view class=\"trend-bar expense\">\n              <view \n                class=\"trend-bar-fill\" \n                :style=\"{ height: (item.expense / maxTrendAmount * 100) + '%' }\"\n              ></view>\n            </view>\n          </view>\n          <view class=\"trend-values\">\n            <text class=\"trend-income\">{{ formatAmount(item.income) }}</text>\n            <text class=\"trend-expense\">{{ formatAmount(item.expense) }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分类详情 -->\n    <view class=\"category-section\">\n      <view class=\"section-title\">分类统计</view>\n      <view class=\"category-tabs\">\n        <view \n          class=\"category-tab\" \n          :class=\"{ active: currentCategoryType === 'expense' }\"\n          @click=\"switchCategoryType('expense')\"\n        >\n          <text class=\"tab-text\">支出</text>\n        </view>\n        <view \n          class=\"category-tab\" \n          :class=\"{ active: currentCategoryType === 'income' }\"\n          @click=\"switchCategoryType('income')\"\n        >\n          <text class=\"tab-text\">收入</text>\n        </view>\n      </view>\n      \n      <view class=\"category-list\">\n        <view \n          class=\"category-item\" \n          v-for=\"item in currentCategoryStats\" \n          :key=\"item.categoryId\"\n        >\n          <view class=\"category-left\">\n            <view class=\"category-icon\" :style=\"{ backgroundColor: item.categoryColor }\">\n              {{ item.categoryIcon }}\n            </view>\n            <view class=\"category-info\">\n              <text class=\"category-name\">{{ item.categoryName }}</text>\n              <text class=\"category-count\">{{ item.count }}笔</text>\n            </view>\n          </view>\n          <view class=\"category-right\">\n            <text class=\"category-amount\">{{ formatAmount(item.amount) }}</text>\n            <text class=\"category-percent\">\n              {{ ((item.amount / (currentCategoryType === 'expense' ? totalExpense : totalIncome)) * 100).toFixed(1) }}%\n            </text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport store from '../../store/index.js'\nimport { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth, getFirstDayOfWeek, getLastDayOfWeek } from '../../utils/helpers.js'\n\nexport default {\n  name: 'StatisticsPage',\n  setup() {\n    // 时间范围选择\n    const currentTimeRange = ref('month')\n    const currentCategoryType = ref('expense')\n    \n    const timeTabs = [\n      { label: '本周', value: 'week' },\n      { label: '本月', value: 'month' },\n      { label: '本年', value: 'year' }\n    ]\n\n    // 根据时间范围过滤记录\n    const filteredRecords = computed(() => {\n      const now = new Date()\n      let startDate, endDate\n\n      switch (currentTimeRange.value) {\n        case 'week':\n          startDate = getFirstDayOfWeek()\n          endDate = getLastDayOfWeek()\n          break\n        case 'month':\n          startDate = getFirstDayOfMonth()\n          endDate = getLastDayOfMonth()\n          break\n        case 'year':\n          startDate = formatDate(new Date(now.getFullYear(), 0, 1))\n          endDate = formatDate(new Date(now.getFullYear(), 11, 31))\n          break\n        default:\n          return store.state.records\n      }\n\n      return store.state.records.filter(record => \n        record.date >= startDate && record.date <= endDate\n      )\n    })\n\n    // 总收入\n    const totalIncome = computed(() => \n      filteredRecords.value\n        .filter(record => record.type === 'income')\n        .reduce((total, record) => total + record.amount, 0)\n    )\n\n    // 总支出\n    const totalExpense = computed(() => \n      filteredRecords.value\n        .filter(record => record.type === 'expense')\n        .reduce((total, record) => total + record.amount, 0)\n    )\n\n    // 净收入\n    const netIncome = computed(() => totalIncome.value - totalExpense.value)\n\n    // 按分类统计支出\n    const expenseByCategory = computed(() => {\n      const stats = {}\n      \n      filteredRecords.value\n        .filter(record => record.type === 'expense')\n        .forEach(record => {\n          if (!stats[record.categoryId]) {\n            stats[record.categoryId] = {\n              categoryId: record.categoryId,\n              categoryName: record.categoryName,\n              categoryIcon: record.categoryIcon,\n              categoryColor: record.categoryColor,\n              amount: 0,\n              count: 0\n            }\n          }\n          stats[record.categoryId].amount += record.amount\n          stats[record.categoryId].count += 1\n        })\n\n      return Object.values(stats)\n        .sort((a, b) => b.amount - a.amount)\n        .slice(0, 8) // 只显示前8个分类\n    })\n\n    // 最大支出金额（用于计算条形图比例）\n    const maxExpenseAmount = computed(() => \n      Math.max(...expenseByCategory.value.map(item => item.amount), 1)\n    )\n\n    // 趋势数据\n    const trendData = computed(() => {\n      const stats = {}\n      \n      filteredRecords.value.forEach(record => {\n        let key = record.date\n        \n        // 根据时间范围调整分组方式\n        if (currentTimeRange.value === 'year') {\n          key = record.date.substring(0, 7) // 按月分组\n        }\n        \n        if (!stats[key]) {\n          stats[key] = {\n            date: key,\n            income: 0,\n            expense: 0\n          }\n        }\n        \n        if (record.type === 'income') {\n          stats[key].income += record.amount\n        } else {\n          stats[key].expense += record.amount\n        }\n      })\n\n      return Object.values(stats)\n        .sort((a, b) => a.date.localeCompare(b.date))\n        .slice(-7) // 只显示最近7个时间点\n    })\n\n    // 趋势图最大值\n    const maxTrendAmount = computed(() => {\n      const amounts = trendData.value.flatMap(item => [item.income, item.expense])\n      return Math.max(...amounts, 1)\n    })\n\n    // 当前分类统计\n    const currentCategoryStats = computed(() => {\n      const stats = {}\n      \n      filteredRecords.value\n        .filter(record => record.type === currentCategoryType.value)\n        .forEach(record => {\n          if (!stats[record.categoryId]) {\n            stats[record.categoryId] = {\n              categoryId: record.categoryId,\n              categoryName: record.categoryName,\n              categoryIcon: record.categoryIcon,\n              categoryColor: record.categoryColor,\n              amount: 0,\n              count: 0\n            }\n          }\n          stats[record.categoryId].amount += record.amount\n          stats[record.categoryId].count += 1\n        })\n\n      return Object.values(stats).sort((a, b) => b.amount - a.amount)\n    })\n\n    // 方法\n    const switchTimeRange = (range) => {\n      currentTimeRange.value = range\n    }\n\n    const switchCategoryType = (type) => {\n      currentCategoryType.value = type\n    }\n\n    const formatTrendDate = (date) => {\n      if (currentTimeRange.value === 'year') {\n        return date.substring(5) // MM-DD\n      }\n      return formatDate(date, 'MM-DD')\n    }\n\n    onMounted(() => {\n      store.actions.loadRecords()\n    })\n\n    return {\n      timeTabs,\n      currentTimeRange,\n      currentCategoryType,\n      totalIncome,\n      totalExpense,\n      netIncome,\n      expenseByCategory,\n      maxExpenseAmount,\n      trendData,\n      maxTrendAmount,\n      currentCategoryStats,\n      formatAmount,\n      formatTrendDate,\n      switchTimeRange,\n      switchCategoryType\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.statistics-page {\n  background-color: $uni-bg-color-grey;\n  min-height: 100vh;\n}\n\n/* 时间选择器 */\n.time-selector {\n  display: flex;\n  background-color: white;\n  padding: 16px;\n  margin-bottom: 16px;\n}\n\n.time-tab {\n  flex: 1;\n  padding: 12px;\n  text-align: center;\n  border-radius: 8px;\n  margin: 0 4px;\n  transition: all 0.3s ease;\n\n  &.active {\n    background-color: $uni-color-primary;\n\n    .tab-text {\n      color: white;\n      font-weight: 600;\n    }\n  }\n}\n\n.tab-text {\n  font-size: 14px;\n  color: $uni-text-color;\n}\n\n/* 总览卡片 */\n.overview-card {\n  display: flex;\n  background-color: white;\n  padding: 24px 20px;\n  margin: 0 16px 16px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.overview-item {\n  flex: 1;\n  text-align: center;\n}\n\n.overview-label {\n  display: block;\n  font-size: 12px;\n  color: $uni-text-color-grey;\n  margin-bottom: 8px;\n}\n\n.overview-value {\n  font-size: 20px;\n  font-weight: bold;\n\n  &.income {\n    color: $income-color;\n  }\n\n  &.expense {\n    color: $expense-color;\n  }\n\n  &.negative {\n    color: $expense-color;\n  }\n}\n\n.overview-divider {\n  width: 1px;\n  background-color: $uni-border-color;\n  margin: 0 16px;\n}\n\n/* 图表区域 */\n.chart-section {\n  background-color: white;\n  margin: 0 16px 16px;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: $uni-text-color;\n  margin-bottom: 16px;\n}\n\n.simple-chart {\n  .chart-bar {\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n\n.bar-info {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.bar-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 12px;\n  margin-right: 8px;\n}\n\n.bar-name {\n  flex: 1;\n  font-size: 14px;\n  color: $uni-text-color;\n}\n\n.bar-amount {\n  font-size: 14px;\n  font-weight: 600;\n  color: $uni-text-color;\n}\n\n.bar-container {\n  height: 8px;\n  background-color: $uni-bg-color-grey;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.bar-fill {\n  height: 100%;\n  border-radius: 4px;\n  transition: width 0.3s ease;\n}\n\n/* 趋势分析 */\n.trend-section {\n  background-color: white;\n  margin: 0 16px 16px;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.trend-chart {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  height: 120px;\n  padding: 0 8px;\n}\n\n.trend-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin: 0 2px;\n}\n\n.trend-date {\n  font-size: 10px;\n  color: $uni-text-color-grey;\n  margin-bottom: 8px;\n}\n\n.trend-bars {\n  display: flex;\n  align-items: flex-end;\n  height: 60px;\n  margin-bottom: 8px;\n}\n\n.trend-bar {\n  width: 8px;\n  margin: 0 1px;\n  border-radius: 4px 4px 0 0;\n\n  &.income {\n    background-color: rgba(76, 175, 80, 0.2);\n  }\n\n  &.expense {\n    background-color: rgba(244, 67, 54, 0.2);\n  }\n}\n\n.trend-bar-fill {\n  width: 100%;\n  border-radius: 4px 4px 0 0;\n  transition: height 0.3s ease;\n\n  .trend-bar.income & {\n    background-color: $income-color;\n  }\n\n  .trend-bar.expense & {\n    background-color: $expense-color;\n  }\n}\n\n.trend-values {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.trend-income, .trend-expense {\n  font-size: 8px;\n  line-height: 1.2;\n}\n\n.trend-income {\n  color: $income-color;\n}\n\n.trend-expense {\n  color: $expense-color;\n}\n\n/* 分类详情 */\n.category-section {\n  background-color: white;\n  margin: 0 16px 16px;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.category-tabs {\n  display: flex;\n  border-bottom: 1px solid $uni-border-color;\n}\n\n.category-tab {\n  flex: 1;\n  padding: 16px;\n  text-align: center;\n  border-bottom: 3px solid transparent;\n  transition: all 0.3s ease;\n\n  &.active {\n    border-bottom-color: $uni-color-primary;\n\n    .tab-text {\n      color: $uni-color-primary;\n      font-weight: 600;\n    }\n  }\n}\n\n.category-list {\n  padding: 0 20px 20px;\n}\n\n.category-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 0;\n  border-bottom: 1px solid $uni-border-color;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.category-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.category-icon {\n  width: 36px;\n  height: 36px;\n  border-radius: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  margin-right: 12px;\n}\n\n.category-info {\n  flex: 1;\n}\n\n.category-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: $uni-text-color;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.category-count {\n  font-size: 12px;\n  color: $uni-text-color-grey;\n}\n\n.category-right {\n  text-align: right;\n}\n\n.category-amount {\n  font-size: 16px;\n  font-weight: 600;\n  color: $uni-text-color;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.category-percent {\n  font-size: 12px;\n  color: $uni-text-color-grey;\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/statistics/statistics.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "getFirstDayOfWeek", "getLastDayOfWeek", "getFirstDayOfMonth", "getLastDayOfMonth", "formatDate", "store", "onMounted", "formatAmount"], "mappings": ";;;;AAuJA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,mBAAmBA,cAAG,IAAC,OAAO;AACpC,UAAM,sBAAsBA,cAAG,IAAC,SAAS;AAEzC,UAAM,WAAW;AAAA,MACf,EAAE,OAAO,MAAM,OAAO,OAAQ;AAAA,MAC9B,EAAE,OAAO,MAAM,OAAO,QAAS;AAAA,MAC/B,EAAE,OAAO,MAAM,OAAO,OAAO;AAAA,IAC/B;AAGA,UAAM,kBAAkBC,cAAAA,SAAS,MAAM;AACrC,YAAM,MAAM,oBAAI,KAAK;AACrB,UAAI,WAAW;AAEf,cAAQ,iBAAiB,OAAK;AAAA,QAC5B,KAAK;AACH,sBAAYC,cAAAA,kBAAkB;AAC9B,oBAAUC,cAAAA,iBAAiB;AAC3B;AAAA,QACF,KAAK;AACH,sBAAYC,cAAAA,mBAAmB;AAC/B,oBAAUC,cAAAA,kBAAkB;AAC5B;AAAA,QACF,KAAK;AACH,sBAAYC,cAAAA,WAAW,IAAI,KAAK,IAAI,eAAe,GAAG,CAAC,CAAC;AACxD,oBAAUA,cAAAA,WAAW,IAAI,KAAK,IAAI,eAAe,IAAI,EAAE,CAAC;AACxD;AAAA,QACF;AACE,iBAAOC,YAAAA,MAAM,MAAM;AAAA,MACvB;AAEA,aAAOA,kBAAM,MAAM,QAAQ;AAAA,QAAO,YAChC,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA,MAC7C;AAAA,KACD;AAGD,UAAM,cAAcN,cAAAA;AAAAA,MAAS,MAC3B,gBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,eAAeA,cAAAA;AAAAA,MAAS,MAC5B,gBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,YAAYA,cAAAA,SAAS,MAAM,YAAY,QAAQ,aAAa,KAAK;AAGvE,UAAM,oBAAoBA,cAAAA,SAAS,MAAM;AACvC,YAAM,QAAQ,CAAC;AAEf,sBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,QAAQ,YAAU;AACjB,YAAI,CAAC,MAAM,OAAO,UAAU,GAAG;AAC7B,gBAAM,OAAO,UAAU,IAAI;AAAA,YACzB,YAAY,OAAO;AAAA,YACnB,cAAc,OAAO;AAAA,YACrB,cAAc,OAAO;AAAA,YACrB,eAAe,OAAO;AAAA,YACtB,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,OAAO,UAAU,EAAE,UAAU,OAAO;AAC1C,cAAM,OAAO,UAAU,EAAE,SAAS;AAAA,OACnC;AAEH,aAAO,OAAO,OAAO,KAAK,EACvB,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,EAClC,MAAM,GAAG,CAAC;AAAA,KACd;AAGD,UAAM,mBAAmBA,cAAAA;AAAAA,MAAS,MAChC,KAAK,IAAI,GAAG,kBAAkB,MAAM,IAAI,UAAQ,KAAK,MAAM,GAAG,CAAC;AAAA,IACjE;AAGA,UAAM,YAAYA,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAQ,CAAC;AAEf,sBAAgB,MAAM,QAAQ,YAAU;AACtC,YAAI,MAAM,OAAO;AAGjB,YAAI,iBAAiB,UAAU,QAAQ;AACrC,gBAAM,OAAO,KAAK,UAAU,GAAG,CAAC;AAAA,QAClC;AAEA,YAAI,CAAC,MAAM,GAAG,GAAG;AACf,gBAAM,GAAG,IAAI;AAAA,YACX,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,SAAS;AAAA,UACX;AAAA,QACF;AAEA,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,GAAG,EAAE,UAAU,OAAO;AAAA,eACvB;AACL,gBAAM,GAAG,EAAE,WAAW,OAAO;AAAA,QAC/B;AAAA,OACD;AAED,aAAO,OAAO,OAAO,KAAK,EACvB,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,cAAc,EAAE,IAAI,CAAC,EAC3C,MAAM,EAAE;AAAA,KACZ;AAGD,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,UAAU,UAAU,MAAM,QAAQ,UAAQ,CAAC,KAAK,QAAQ,KAAK,OAAO,CAAC;AAC3E,aAAO,KAAK,IAAI,GAAG,SAAS,CAAC;AAAA,KAC9B;AAGD,UAAM,uBAAuBA,cAAAA,SAAS,MAAM;AAC1C,YAAM,QAAQ,CAAC;AAEf,sBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,oBAAoB,KAAK,EAC1D,QAAQ,YAAU;AACjB,YAAI,CAAC,MAAM,OAAO,UAAU,GAAG;AAC7B,gBAAM,OAAO,UAAU,IAAI;AAAA,YACzB,YAAY,OAAO;AAAA,YACnB,cAAc,OAAO;AAAA,YACrB,cAAc,OAAO;AAAA,YACrB,eAAe,OAAO;AAAA,YACtB,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,OAAO,UAAU,EAAE,UAAU,OAAO;AAC1C,cAAM,OAAO,UAAU,EAAE,SAAS;AAAA,OACnC;AAEH,aAAO,OAAO,OAAO,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AAAA,KAC/D;AAGD,UAAM,kBAAkB,CAAC,UAAU;AACjC,uBAAiB,QAAQ;AAAA,IAC3B;AAEA,UAAM,qBAAqB,CAAC,SAAS;AACnC,0BAAoB,QAAQ;AAAA,IAC9B;AAEA,UAAM,kBAAkB,CAAC,SAAS;AAChC,UAAI,iBAAiB,UAAU,QAAQ;AACrC,eAAO,KAAK,UAAU,CAAC;AAAA,MACzB;AACA,aAAOK,cAAU,WAAC,MAAM,OAAO;AAAA,IACjC;AAEAE,kBAAAA,UAAU,MAAM;AACdD,kBAAK,MAAC,QAAQ,YAAY;AAAA,KAC3B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,oBACAE,cAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClVA,GAAG,WAAW,eAAe;"}