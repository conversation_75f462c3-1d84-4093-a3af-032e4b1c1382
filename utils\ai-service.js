/**
 * AI服务模块 - 阿里云百炼模型接入
 * 支持语音识别、图像识别、自然语言处理
 */

// 阿里云百炼API配置
const AI_CONFIG = {
  // 请替换为您的阿里云百炼API配置
  apiKey: 'your-dashscope-api-key', // 需要在manifest.json中配置或使用环境变量
  baseURL: 'https://dashscope.aliyuncs.com/api/v1',
  model: 'qwen-turbo', // 使用通义千问模型
  
  // 图像识别配置
  visionModel: 'qwen-vl-plus',
  
  // 语音识别配置 (可选，也可以使用uni-app内置语音识别)
  speechModel: 'paraformer-realtime-v1'
};

class AIService {
  constructor() {
    this.apiKey = AI_CONFIG.apiKey;
    this.baseURL = AI_CONFIG.baseURL;
  }

  /**
   * 调用阿里云百炼API
   * @param {Object} params - API调用参数
   * @returns {Promise<Object>} API响应结果
   */
  async callDashScopeAPI(params) {
    try {
      const response = await uni.request({
        url: `${this.baseURL}/services/aigc/text-generation/generation`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'X-DashScope-SSE': 'disable'
        },
        data: params
      });

      if (response.statusCode === 200 && response.data.output) {
        return {
          success: true,
          data: response.data.output.text,
          usage: response.data.usage
        };
      } else {
        throw new Error(response.data.message || '调用AI服务失败');
      }
    } catch (error) {
      console.error('AI服务调用失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分析文本内容，提取记账信息
   * @param {string} text - 要分析的文本内容
   * @returns {Promise<Object>} 提取的记账信息
   */
  async analyzeTextForBookkeeping(text) {
    const prompt = `
请分析以下文本内容，提取记账相关信息，并以JSON格式返回：

文本内容：${text}

请提取以下信息：
1. type: 收支类型 ("expense" 或 "income")
2. amount: 金额数字 (纯数字，不包含货币符号)
3. category: 分类 (餐饮、交通、购物、娱乐、工资、奖金等)
4. description: 描述/备注
5. merchant: 商家名称 (如果有)
6. confidence: 识别置信度 (0-1之间的数字)

返回格式示例：
{
  "type": "expense",
  "amount": 25.5,
  "category": "餐饮",
  "description": "午餐",
  "merchant": "麦当劳",
  "confidence": 0.9
}

如果无法识别有效的记账信息，请返回：
{
  "type": null,
  "amount": null,
  "category": null,
  "description": "${text}",
  "merchant": null,
  "confidence": 0
}

只返回JSON，不要其他说明文字。
`;

    try {
      const result = await this.callDashScopeAPI({
        model: AI_CONFIG.model,
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.1,
          max_tokens: 500
        }
      });

      if (result.success) {
        // 尝试解析JSON响应
        try {
          const jsonMatch = result.data.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsedData = JSON.parse(jsonMatch[0]);
            return {
              success: true,
              data: this.normalizeBookkeepingData(parsedData)
            };
          }
        } catch (parseError) {
          console.error('解析AI响应失败:', parseError);
        }
      }

      return {
        success: false,
        error: '无法解析AI响应'
      };
    } catch (error) {
      console.error('文本分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 分析图片内容，提取记账信息
   * @param {string} imageBase64 - 图片的base64编码
   * @returns {Promise<Object>} 提取的记账信息
   */
  async analyzeImageForBookkeeping(imageBase64) {
    const prompt = `
请分析这张图片，提取记账相关信息。这可能是发票、收据、账单或其他包含消费信息的图片。

请提取以下信息并以JSON格式返回：
1. type: 收支类型 ("expense" 或 "income")
2. amount: 金额数字 (纯数字，不包含货币符号)
3. category: 分类 (根据商品/服务类型判断：餐饮、交通、购物、娱乐等)
4. description: 描述/备注 (商品名称或服务描述)
5. merchant: 商家名称
6. date: 日期 (YYYY-MM-DD格式，如果图片中有的话)
7. confidence: 识别置信度 (0-1之间的数字)

返回格式示例：
{
  "type": "expense",
  "amount": 68.5,
  "category": "餐饮",
  "description": "晚餐",
  "merchant": "海底捞火锅",
  "date": "2024-01-15",
  "confidence": 0.85
}

只返回JSON，不要其他说明文字。
`;

    try {
      const result = await uni.request({
        url: `${this.baseURL}/services/aigc/multimodal-generation/generation`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        data: {
          model: AI_CONFIG.visionModel,
          input: {
            messages: [
              {
                role: 'user',
                content: [
                  {
                    type: 'text',
                    text: prompt
                  },
                  {
                    type: 'image_url',
                    image_url: {
                      url: `data:image/jpeg;base64,${imageBase64}`
                    }
                  }
                ]
              }
            ]
          },
          parameters: {
            temperature: 0.1,
            max_tokens: 500
          }
        }
      });

      if (response.statusCode === 200 && response.data.output) {
        try {
          const jsonMatch = response.data.output.text.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const parsedData = JSON.parse(jsonMatch[0]);
            return {
              success: true,
              data: this.normalizeBookkeepingData(parsedData)
            };
          }
        } catch (parseError) {
          console.error('解析AI图片分析响应失败:', parseError);
        }
      }

      return {
        success: false,
        error: '无法解析图片内容'
      };
    } catch (error) {
      console.error('图片分析失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 标准化记账数据
   * @param {Object} data - AI返回的原始数据
   * @returns {Object} 标准化后的数据
   */
  normalizeBookkeepingData(data) {
    // 分类映射表
    const categoryMap = {
      '餐饮': 'exp_food',
      '交通': 'exp_transport', 
      '购物': 'exp_shopping',
      '娱乐': 'exp_entertainment',
      '工资': 'inc_salary',
      '奖金': 'inc_bonus',
      '其他': data.type === 'expense' ? 'exp_other' : 'inc_other'
    };

    return {
      type: data.type || 'expense',
      amount: parseFloat(data.amount) || 0,
      categoryId: categoryMap[data.category] || (data.type === 'expense' ? 'exp_other' : 'inc_other'),
      categoryName: data.category || '其他',
      categoryIcon: this.getCategoryIcon(data.category),
      categoryColor: this.getCategoryColor(data.category),
      note: data.description || '',
      merchant: data.merchant || '',
      date: data.date || new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().split(' ')[0].substring(0, 5),
      confidence: data.confidence || 0
    };
  }

  /**
   * 获取分类图标
   * @param {string} category - 分类名称
   * @returns {string} 图标emoji
   */
  getCategoryIcon(category) {
    const iconMap = {
      '餐饮': '🍽️',
      '交通': '🚗',
      '购物': '🛍️',
      '娱乐': '🎮',
      '工资': '💰',
      '奖金': '🎉'
    };
    return iconMap[category] || '📦';
  }

  /**
   * 获取分类颜色
   * @param {string} category - 分类名称
   * @returns {string} 颜色值
   */
  getCategoryColor(category) {
    const colorMap = {
      '餐饮': '#FF6B6B',
      '交通': '#4ECDC4',
      '购物': '#45B7D1',
      '娱乐': '#96CEB4',
      '工资': '#2ECC71',
      '奖金': '#3498DB'
    };
    return colorMap[category] || '#AEB6BF';
  }

  /**
   * 检查API配置是否有效
   * @returns {boolean} 配置是否有效
   */
  isConfigValid() {
    return this.apiKey && this.apiKey !== 'your-dashscope-api-key';
  }
}

export default new AIService();
