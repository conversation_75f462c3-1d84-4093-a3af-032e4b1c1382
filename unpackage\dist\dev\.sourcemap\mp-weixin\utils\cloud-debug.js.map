{"version": 3, "file": "cloud-debug.js", "sources": ["utils/cloud-debug.js"], "sourcesContent": ["/**\n * 云函数调试工具\n * 用于诊断云函数调用问题\n */\n\nexport default {\n  // 测试基础云函数连接\n  async testBasicConnection() {\n    try {\n      console.log('测试基础云函数连接...');\n      \n      const result = await uniCloud.callFunction({\n        name: 'test',\n        data: { message: 'debug test' }\n      });\n      \n      console.log('基础连接测试结果:', result);\n      \n      return {\n        success: result.result && result.result.code === 200,\n        data: result,\n        message: result.result ? result.result.message : '连接失败'\n      };\n    } catch (error) {\n      console.error('基础连接测试失败:', error);\n      return {\n        success: false,\n        error: error.message,\n        details: error\n      };\n    }\n  },\n\n  // 测试 bookkeeping 云函数基础调用\n  async testBookkeepingBasic() {\n    try {\n      console.log('测试 bookkeeping 云函数基础调用...');\n      \n      const result = await uniCloud.callFunction({\n        name: 'bookkeeping',\n        data: {\n          action: 'getCategories',\n          data: {}\n        }\n      });\n      \n      console.log('bookkeeping 基础测试结果:', result);\n      \n      return {\n        success: result.result && result.result.code === 200,\n        data: result,\n        message: result.result ? result.result.message : '调用失败'\n      };\n    } catch (error) {\n      console.error('bookkeeping 基础测试失败:', error);\n      return {\n        success: false,\n        error: error.message,\n        details: error\n      };\n    }\n  },\n\n  // 测试数据库初始化\n  async testDatabaseInit() {\n    try {\n      console.log('测试数据库初始化...');\n      \n      const result = await uniCloud.callFunction({\n        name: 'init-db',\n        data: {}\n      });\n      \n      console.log('数据库初始化测试结果:', result);\n      \n      return {\n        success: result.result && result.result.code === 200,\n        data: result,\n        message: result.result ? result.result.message : '初始化失败'\n      };\n    } catch (error) {\n      console.error('数据库初始化测试失败:', error);\n      return {\n        success: false,\n        error: error.message,\n        details: error\n      };\n    }\n  },\n\n  // 测试添加记录（简化版）\n  async testAddRecordSimple() {\n    try {\n      console.log('测试添加记录（简化版）...');\n      \n      const testRecord = {\n        type: 'expense',\n        amount: 1.0,\n        categoryId: 'exp_food',\n        categoryName: '餐饮',\n        categoryIcon: '🍽️',\n        categoryColor: '#FF6B6B',\n        note: '调试测试',\n        date: new Date().toISOString().split('T')[0],\n        time: '12:00'\n      };\n      \n      console.log('测试记录数据:', testRecord);\n      \n      const result = await uniCloud.callFunction({\n        name: 'bookkeeping',\n        data: {\n          action: 'addRecord',\n          data: testRecord\n        }\n      });\n      \n      console.log('添加记录测试结果:', result);\n      \n      return {\n        success: result.result && result.result.code === 200,\n        data: result,\n        message: result.result ? result.result.message : '添加失败'\n      };\n    } catch (error) {\n      console.error('添加记录测试失败:', error);\n      return {\n        success: false,\n        error: error.message,\n        details: error\n      };\n    }\n  },\n\n  // 完整的诊断流程\n  async runFullDiagnosis() {\n    const results = {\n      basicConnection: null,\n      bookkeepingBasic: null,\n      databaseInit: null,\n      addRecord: null\n    };\n\n    console.log('开始完整诊断...');\n\n    // 1. 基础连接测试\n    results.basicConnection = await this.testBasicConnection();\n    \n    // 2. bookkeeping 基础测试\n    results.bookkeepingBasic = await this.testBookkeepingBasic();\n    \n    // 3. 数据库初始化测试\n    results.databaseInit = await this.testDatabaseInit();\n    \n    // 4. 添加记录测试\n    results.addRecord = await this.testAddRecordSimple();\n\n    console.log('完整诊断结果:', results);\n\n    return results;\n  },\n\n  // 生成诊断报告\n  generateReport(results) {\n    let report = '=== 云函数诊断报告 ===\\n\\n';\n    \n    report += `1. 基础连接: ${results.basicConnection?.success ? '✅ 正常' : '❌ 失败'}\\n`;\n    if (!results.basicConnection?.success) {\n      report += `   错误: ${results.basicConnection?.error}\\n`;\n    }\n    \n    report += `2. bookkeeping 云函数: ${results.bookkeepingBasic?.success ? '✅ 正常' : '❌ 失败'}\\n`;\n    if (!results.bookkeepingBasic?.success) {\n      report += `   错误: ${results.bookkeepingBasic?.error}\\n`;\n    }\n    \n    report += `3. 数据库初始化: ${results.databaseInit?.success ? '✅ 正常' : '❌ 失败'}\\n`;\n    if (!results.databaseInit?.success) {\n      report += `   错误: ${results.databaseInit?.error}\\n`;\n    }\n    \n    report += `4. 添加记录: ${results.addRecord?.success ? '✅ 正常' : '❌ 失败'}\\n`;\n    if (!results.addRecord?.success) {\n      report += `   错误: ${results.addRecord?.error}\\n`;\n    }\n    \n    report += '\\n=== 建议操作 ===\\n';\n    \n    if (!results.basicConnection?.success) {\n      report += '- 检查 uniCloud 空间配置\\n';\n      report += '- 确保 test 云函数已部署\\n';\n    }\n    \n    if (!results.bookkeepingBasic?.success) {\n      report += '- 重新部署 bookkeeping 云函数\\n';\n      report += '- 检查云函数代码是否有语法错误\\n';\n    }\n    \n    if (!results.databaseInit?.success) {\n      report += '- 部署 init-db 云函数\\n';\n      report += '- 检查数据库权限配置\\n';\n    }\n    \n    if (!results.addRecord?.success) {\n      report += '- 确保数据库已初始化\\n';\n      report += '- 检查 generateId 函数是否存在\\n';\n    }\n    \n    return report;\n  }\n};\n"], "names": ["uni", "uniCloud"], "mappings": ";;AAKA,MAAe,aAAA;AAAA;AAAA,EAEb,MAAM,sBAAsB;AAC1B,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,cAAc;AAE1B,YAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,MAAM,EAAE,SAAS,aAAc;AAAA,MACvC,CAAO;AAEDD,oBAAY,MAAA,MAAA,OAAA,8BAAA,aAAa,MAAM;AAE/B,aAAO;AAAA,QACL,SAAS,OAAO,UAAU,OAAO,OAAO,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,SAAS,OAAO,SAAS,OAAO,OAAO,UAAU;AAAA,MACzD;AAAA,IACK,SAAQ,OAAO;AACdA,uEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,uBAAuB;AAC3B,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,2BAA2B;AAEvC,YAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM,CAAE;AAAA,QACT;AAAA,MACT,CAAO;AAEDD,qEAAY,uBAAuB,MAAM;AAEzC,aAAO;AAAA,QACL,SAAS,OAAO,UAAU,OAAO,OAAO,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,SAAS,OAAO,SAAS,OAAO,OAAO,UAAU;AAAA,MACzD;AAAA,IACK,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,uBAAuB,KAAK;AAC1C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB;AACvB,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,aAAa;AAEzB,YAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,MAAM,CAAE;AAAA,MAChB,CAAO;AAEDD,oBAAA,MAAA,MAAA,OAAA,8BAAY,eAAe,MAAM;AAEjC,aAAO;AAAA,QACL,SAAS,OAAO,UAAU,OAAO,OAAO,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,SAAS,OAAO,SAAS,OAAO,OAAO,UAAU;AAAA,MACzD;AAAA,IACK,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,KAAK;AAClC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,sBAAsB;AAC1B,QAAI;AACFA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,gBAAgB;AAE5B,YAAM,aAAa;AAAA,QACjB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,MAAM;AAAA,QACN,OAAM,oBAAI,QAAO,YAAa,EAAC,MAAM,GAAG,EAAE,CAAC;AAAA,QAC3C,MAAM;AAAA,MACd;AAEMA,oBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,UAAU;AAEjC,YAAM,SAAS,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACzC,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,MAAM;AAAA,QACP;AAAA,MACT,CAAO;AAEDD,oBAAY,MAAA,MAAA,OAAA,+BAAA,aAAa,MAAM;AAE/B,aAAO;AAAA,QACL,SAAS,OAAO,UAAU,OAAO,OAAO,SAAS;AAAA,QACjD,MAAM;AAAA,QACN,SAAS,OAAO,SAAS,OAAO,OAAO,UAAU;AAAA,MACzD;AAAA,IACK,SAAQ,OAAO;AACdA,wEAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,MACjB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,mBAAmB;AACvB,UAAM,UAAU;AAAA,MACd,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,WAAW;AAAA,IACjB;AAEIA,kBAAAA,MAAY,MAAA,OAAA,+BAAA,WAAW;AAGvB,YAAQ,kBAAkB,MAAM,KAAK,oBAAmB;AAGxD,YAAQ,mBAAmB,MAAM,KAAK,qBAAoB;AAG1D,YAAQ,eAAe,MAAM,KAAK,iBAAgB;AAGlD,YAAQ,YAAY,MAAM,KAAK,oBAAmB;AAElDA,kBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,OAAO;AAE9B,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,eAAe,SAAS;;AACtB,QAAI,SAAS;AAEb,cAAU,cAAY,aAAQ,oBAAR,mBAAyB,WAAU,SAAS,MAAM;AAAA;AACxE,QAAI,GAAC,aAAQ,oBAAR,mBAAyB,UAAS;AACrC,gBAAU,WAAU,aAAQ,oBAAR,mBAAyB,KAAK;AAAA;AAAA,IACnD;AAED,cAAU,yBAAuB,aAAQ,qBAAR,mBAA0B,WAAU,SAAS,MAAM;AAAA;AACpF,QAAI,GAAC,aAAQ,qBAAR,mBAA0B,UAAS;AACtC,gBAAU,WAAU,aAAQ,qBAAR,mBAA0B,KAAK;AAAA;AAAA,IACpD;AAED,cAAU,gBAAc,aAAQ,iBAAR,mBAAsB,WAAU,SAAS,MAAM;AAAA;AACvE,QAAI,GAAC,aAAQ,iBAAR,mBAAsB,UAAS;AAClC,gBAAU,WAAU,aAAQ,iBAAR,mBAAsB,KAAK;AAAA;AAAA,IAChD;AAED,cAAU,cAAY,aAAQ,cAAR,mBAAmB,WAAU,SAAS,MAAM;AAAA;AAClE,QAAI,GAAC,aAAQ,cAAR,mBAAmB,UAAS;AAC/B,gBAAU,WAAU,aAAQ,cAAR,mBAAmB,KAAK;AAAA;AAAA,IAC7C;AAED,cAAU;AAEV,QAAI,GAAC,aAAQ,oBAAR,mBAAyB,UAAS;AACrC,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,QAAI,GAAC,aAAQ,qBAAR,mBAA0B,UAAS;AACtC,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,QAAI,GAAC,aAAQ,iBAAR,mBAAsB,UAAS;AAClC,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,QAAI,GAAC,aAAQ,cAAR,mBAAmB,UAAS;AAC/B,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,WAAO;AAAA,EACR;AACH;;"}