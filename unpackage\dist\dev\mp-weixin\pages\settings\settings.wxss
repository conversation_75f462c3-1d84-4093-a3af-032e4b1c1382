/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-7fad0a1c {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-7fad0a1c {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-7fad0a1c {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-7fad0a1c {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-7fad0a1c {
  justify-content: space-between;
}
.flex-center.data-v-7fad0a1c {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-7fad0a1c {
  color: #4CAF50;
}
.text-expense.data-v-7fad0a1c {
  color: #F44336;
}
.text-bold.data-v-7fad0a1c {
  font-weight: bold;
}
.text-center.data-v-7fad0a1c {
  text-align: center;
}
.text-right.data-v-7fad0a1c {
  text-align: right;
}
.settings-page.data-v-7fad0a1c {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 16px;
}

/* 用户信息卡片 */
.user-card.data-v-7fad0a1c {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.user-avatar.data-v-7fad0a1c {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}
.avatar-text.data-v-7fad0a1c {
  font-size: 24px;
  font-weight: bold;
  color: white;
}
.user-info.data-v-7fad0a1c {
  flex: 1;
}
.user-name.data-v-7fad0a1c {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.user-desc.data-v-7fad0a1c {
  font-size: 14px;
  color: #999;
}

/* 统计概览 */
.stats-overview.data-v-7fad0a1c {
  display: flex;
  background-color: white;
  border-radius: 12px;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.stats-item.data-v-7fad0a1c {
  flex: 1;
  padding: 20px;
  text-align: center;
  border-right: 1px solid #e5e5e5;
}
.stats-item.data-v-7fad0a1c:last-child {
  border-right: none;
}
.stats-number.data-v-7fad0a1c {
  font-size: 20px;
  font-weight: bold;
  color: #4CAF50;
  display: block;
  margin-bottom: 8px;
}
.stats-label.data-v-7fad0a1c {
  font-size: 12px;
  color: #999;
}

/* 设置区域 */
.settings-section.data-v-7fad0a1c {
  margin-bottom: 24px;
}
.section-title.data-v-7fad0a1c {
  font-size: 14px;
  font-weight: 600;
  color: #999;
  margin-bottom: 12px;
  padding: 0 4px;
}
.setting-item.data-v-7fad0a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.3s ease;
}
.setting-item.data-v-7fad0a1c:first-child {
  border-radius: 12px 12px 0 0;
}
.setting-item.data-v-7fad0a1c:last-child {
  border-bottom: none;
  border-radius: 0 0 12px 12px;
}
.setting-item.data-v-7fad0a1c:only-child {
  border-radius: 12px;
}
.setting-item.data-v-7fad0a1c:active {
  background-color: #f1f1f1;
}
.setting-left.data-v-7fad0a1c {
  display: flex;
  align-items: center;
  flex: 1;
}
.setting-icon.data-v-7fad0a1c {
  font-size: 20px;
  margin-right: 12px;
}
.setting-name.data-v-7fad0a1c {
  font-size: 16px;
  color: #333;
}
.setting-right.data-v-7fad0a1c {
  display: flex;
  align-items: center;
}
.setting-value.data-v-7fad0a1c {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}
.setting-arrow.data-v-7fad0a1c {
  font-size: 12px;
  color: #808080;
}