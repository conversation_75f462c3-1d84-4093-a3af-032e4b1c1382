"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_testCloud = require("../../utils/test-cloud.js");
const _sfc_main = {
  name: "HomePage",
  setup() {
    const state = common_vendor.computed(() => store_index.store.state);
    const greetingText = common_vendor.computed(() => {
      const hour = (/* @__PURE__ */ new Date()).getHours();
      if (hour < 6)
        return "夜深了";
      if (hour < 12)
        return "早上好";
      if (hour < 18)
        return "下午好";
      return "晚上好";
    });
    const currentDateText = common_vendor.computed(() => {
      const now = /* @__PURE__ */ new Date();
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return `${now.getMonth() + 1}月${now.getDate()}日 ${weekdays[now.getDay()]}`;
    });
    const syncStatusIcon = common_vendor.computed(() => {
      if (state.value.syncing)
        return "🔄";
      if (!state.value.isOnline)
        return "📴";
      if (state.value.useCloudStorage)
        return "☁️";
      return "📱";
    });
    const currentMonth = common_vendor.computed(() => {
      const now = /* @__PURE__ */ new Date();
      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, "0")}月`;
    });
    const monthlyRecords = common_vendor.computed(() => {
      const start = utils_helpers.getFirstDayOfMonth();
      const end = utils_helpers.getLastDayOfMonth();
      return store_index.store.state.records.filter(
        (record) => record.date >= start && record.date <= end
      );
    });
    const lastMonthRecords = common_vendor.computed(() => {
      const now = /* @__PURE__ */ new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const start = utils_helpers.formatDate(lastMonth);
      const end = utils_helpers.formatDate(new Date(now.getFullYear(), now.getMonth(), 0));
      return store_index.store.state.records.filter(
        (record) => record.date >= start && record.date <= end
      );
    });
    const monthlyIncome = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
    );
    const monthlyExpense = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
    );
    const monthlyBalance = common_vendor.computed(() => monthlyIncome.value - monthlyExpense.value);
    const lastMonthBalance = common_vendor.computed(() => {
      const income = lastMonthRecords.value.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0);
      const expense = lastMonthRecords.value.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0);
      return income - expense;
    });
    const balanceChange = common_vendor.computed(() => monthlyBalance.value - lastMonthBalance.value);
    const balanceTrend = common_vendor.computed(() => {
      if (balanceChange.value > 0)
        return "up";
      if (balanceChange.value < 0)
        return "down";
      return "stable";
    });
    const balanceTrendIcon = common_vendor.computed(() => {
      if (balanceChange.value > 0)
        return "📈";
      if (balanceChange.value < 0)
        return "📉";
      return "➖";
    });
    const incomeTransactions = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "income").length
    );
    const expenseTransactions = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "expense").length
    );
    const incomeProgress = common_vendor.computed(() => {
      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value);
      return maxAmount > 0 ? monthlyIncome.value / maxAmount * 100 : 0;
    });
    const expenseProgress = common_vendor.computed(() => {
      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value);
      return maxAmount > 0 ? monthlyExpense.value / maxAmount * 100 : 0;
    });
    const recentRecords = common_vendor.computed(
      () => store_index.store.state.records.sort((a, b) => /* @__PURE__ */ new Date(b.date + " " + b.time) - /* @__PURE__ */ new Date(a.date + " " + a.time)).slice(0, 5)
    );
    const formatDateTime = (date, time) => {
      const today = utils_helpers.formatDate(/* @__PURE__ */ new Date());
      const yesterday = utils_helpers.formatDate(new Date(Date.now() - 24 * 60 * 60 * 1e3));
      if (date === today) {
        return `今天 ${time}`;
      } else if (date === yesterday) {
        return `昨天 ${time}`;
      } else {
        return `${utils_helpers.formatDate(date, "MM-DD")} ${time}`;
      }
    };
    const quickAdd = (type) => {
      common_vendor.index.setStorageSync("quickAddType", type);
      common_vendor.index.switchTab({
        url: "/pages/add/add"
      });
    };
    const editRecord = (record) => {
      common_vendor.index.setStorageSync("editRecordId", record.id);
      common_vendor.index.switchTab({
        url: "/pages/add/add"
      });
    };
    const goToList = () => {
      common_vendor.index.navigateTo({
        url: "/pages/list/list"
      });
    };
    const handleTestCloud = async () => {
      common_vendor.index.showLoading({
        title: "测试中..."
      });
      try {
        const result = await utils_testCloud.testCloudFunction();
        common_vendor.index.hideLoading();
        if (result.success) {
          common_vendor.index.showToast({
            title: "测试成功",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "测试失败",
            icon: "error"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "测试出错",
          icon: "error"
        });
        common_vendor.index.__f__("error", "at pages/index/index.vue:375", "测试云函数失败:", error);
      }
    };
    const goToTestPage = () => {
      common_vendor.index.navigateTo({
        url: "/pages/test/test"
      });
    };
    const isDev = common_vendor.computed(() => {
      return true;
    });
    common_vendor.onMounted(() => {
      store_index.store.actions.loadRecords();
    });
    return {
      // 状态
      state,
      // 问候语
      greetingText,
      currentDateText,
      syncStatusIcon,
      // 统计数据
      currentMonth,
      monthlyIncome,
      monthlyExpense,
      monthlyBalance,
      balanceChange,
      balanceTrend,
      balanceTrendIcon,
      incomeTransactions,
      expenseTransactions,
      incomeProgress,
      expenseProgress,
      // 记录列表
      recentRecords,
      // 工具函数
      formatAmount: utils_helpers.formatAmount,
      formatDateTime,
      // 操作方法
      quickAdd,
      editRecord,
      goToList,
      handleTestCloud,
      goToTestPage,
      // 开发相关
      isDev
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($setup.greetingText),
    b: common_vendor.t($setup.currentDateText),
    c: common_vendor.t($setup.syncStatusIcon),
    d: $setup.state.syncing ? 1 : "",
    e: !$setup.state.isOnline ? 1 : "",
    f: $setup.isDev
  }, $setup.isDev ? {
    g: common_vendor.o((...args) => $setup.handleTestCloud && $setup.handleTestCloud(...args))
  } : {}, {
    h: $setup.isDev
  }, $setup.isDev ? {
    i: common_vendor.o((...args) => $setup.goToTestPage && $setup.goToTestPage(...args))
  } : {}, {
    j: common_vendor.t($setup.balanceTrendIcon),
    k: common_vendor.n($setup.balanceTrend),
    l: common_vendor.t($setup.formatAmount(Math.abs($setup.monthlyBalance))),
    m: $setup.monthlyBalance < 0 ? 1 : "",
    n: $setup.balanceChange !== 0
  }, $setup.balanceChange !== 0 ? {
    o: common_vendor.t($setup.balanceChange > 0 ? "+" : ""),
    p: common_vendor.t($setup.formatAmount(Math.abs($setup.balanceChange))),
    q: $setup.balanceChange > 0 ? 1 : "",
    r: $setup.balanceChange < 0 ? 1 : ""
  } : {}, {
    s: common_vendor.t($setup.formatAmount($setup.monthlyIncome)),
    t: $setup.incomeProgress + "%",
    v: common_vendor.t($setup.incomeTransactions),
    w: common_vendor.t($setup.formatAmount($setup.monthlyExpense)),
    x: $setup.expenseProgress + "%",
    y: common_vendor.t($setup.expenseTransactions),
    z: common_vendor.o(($event) => $setup.quickAdd("expense")),
    A: common_vendor.o(($event) => $setup.quickAdd("income")),
    B: common_vendor.t($setup.recentRecords.length),
    C: common_vendor.o((...args) => $setup.goToList && $setup.goToList(...args)),
    D: $setup.recentRecords.length > 0
  }, $setup.recentRecords.length > 0 ? {
    E: common_vendor.f($setup.recentRecords, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.categoryIcon),
        b: record.categoryColor,
        c: common_vendor.n(record.type),
        d: common_vendor.t(record.categoryName),
        e: record.note
      }, record.note ? {
        f: common_vendor.t(record.note)
      } : {}, {
        g: common_vendor.t($setup.formatDateTime(record.date, record.time)),
        h: common_vendor.t(record.type === "expense" ? "-" : "+"),
        i: common_vendor.t($setup.formatAmount(record.amount)),
        j: common_vendor.n(record.type),
        k: record.id,
        l: index * 0.1 + "s",
        m: common_vendor.o(($event) => $setup.editRecord(record), record.id)
      });
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
