"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "HomePage",
  setup() {
    const currentMonth = common_vendor.computed(() => {
      const now = /* @__PURE__ */ new Date();
      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, "0")}月`;
    });
    const monthlyRecords = common_vendor.computed(() => {
      const start = utils_helpers.getFirstDayOfMonth();
      const end = utils_helpers.getLastDayOfMonth();
      return store_index.store.state.records.filter(
        (record) => record.date >= start && record.date <= end
      );
    });
    const monthlyIncome = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
    );
    const monthlyExpense = common_vendor.computed(
      () => monthlyRecords.value.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
    );
    const monthlyBalance = common_vendor.computed(() => monthlyIncome.value - monthlyExpense.value);
    const recentRecords = common_vendor.computed(
      () => store_index.store.state.records.sort((a, b) => /* @__PURE__ */ new Date(b.date + " " + b.time) - /* @__PURE__ */ new Date(a.date + " " + a.time)).slice(0, 5)
    );
    const formatDateTime = (date, time) => {
      const today = utils_helpers.formatDate(/* @__PURE__ */ new Date());
      const yesterday = utils_helpers.formatDate(new Date(Date.now() - 24 * 60 * 60 * 1e3));
      if (date === today) {
        return `今天 ${time}`;
      } else if (date === yesterday) {
        return `昨天 ${time}`;
      } else {
        return `${utils_helpers.formatDate(date, "MM-DD")} ${time}`;
      }
    };
    const quickAdd = (type) => {
      common_vendor.index.navigateTo({
        url: `/pages/add/add?type=${type}`
      });
    };
    const editRecord = (record) => {
      common_vendor.index.navigateTo({
        url: `/pages/add/add?id=${record.id}`
      });
    };
    const goToList = () => {
      common_vendor.index.switchTab({
        url: "/pages/list/list"
      });
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadRecords();
    });
    return {
      currentMonth,
      monthlyIncome,
      monthlyExpense,
      monthlyBalance,
      recentRecords,
      formatAmount: utils_helpers.formatAmount,
      formatDateTime,
      quickAdd,
      editRecord,
      goToList
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($setup.currentMonth),
    b: common_vendor.t($setup.formatAmount($setup.monthlyIncome)),
    c: common_vendor.t($setup.formatAmount($setup.monthlyExpense)),
    d: common_vendor.t($setup.formatAmount($setup.monthlyBalance)),
    e: $setup.monthlyBalance < 0 ? 1 : "",
    f: common_vendor.o(($event) => $setup.quickAdd("expense")),
    g: common_vendor.o(($event) => $setup.quickAdd("income")),
    h: common_vendor.o((...args) => $setup.goToList && $setup.goToList(...args)),
    i: $setup.recentRecords.length > 0
  }, $setup.recentRecords.length > 0 ? {
    j: common_vendor.f($setup.recentRecords, (record, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(record.categoryIcon),
        b: record.categoryColor,
        c: common_vendor.t(record.categoryName),
        d: record.note
      }, record.note ? {
        e: common_vendor.t(record.note)
      } : {}, {
        f: common_vendor.t($setup.formatDateTime(record.date, record.time)),
        g: common_vendor.t(record.type === "expense" ? "-" : "+"),
        h: common_vendor.t($setup.formatAmount(record.amount)),
        i: common_vendor.n(record.type),
        j: record.id,
        k: common_vendor.o(($event) => $setup.editRecord(record), record.id)
      });
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
