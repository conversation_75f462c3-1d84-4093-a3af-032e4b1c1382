/**
 * 工具函数
 */

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {string} currency 货币符号
 * @returns {string} 格式化后的金额
 */
export function formatAmount(amount, currency = '¥') {
  if (typeof amount !== 'number') {
    amount = parseFloat(amount) || 0
  }
  return `${currency}${amount.toFixed(2)}`
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'YYYY年MM月DD日':
      return `${year}年${month}月${day}日`
    case 'MM月DD日':
      return `${month}月${day}日`
    default:
      return `${year}-${month}-${day}`
  }
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间
 */
export function formatTime(time) {
  if (typeof time === 'string' && time.includes(':')) {
    return time
  }
  const d = new Date(time)
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * 获取今天的日期
 * @returns {string} YYYY-MM-DD格式的日期
 */
export function getToday() {
  return formatDate(new Date())
}

/**
 * 获取本月第一天
 * @returns {string} YYYY-MM-DD格式的日期
 */
export function getFirstDayOfMonth() {
  const now = new Date()
  return formatDate(new Date(now.getFullYear(), now.getMonth(), 1))
}

/**
 * 获取本月最后一天
 * @returns {string} YYYY-MM-DD格式的日期
 */
export function getLastDayOfMonth() {
  const now = new Date()
  return formatDate(new Date(now.getFullYear(), now.getMonth() + 1, 0))
}

/**
 * 获取本周第一天
 * @param {number} firstDayOfWeek 一周的第一天 (0: 周日, 1: 周一)
 * @returns {string} YYYY-MM-DD格式的日期
 */
export function getFirstDayOfWeek(firstDayOfWeek = 1) {
  const now = new Date()
  const day = now.getDay()
  const diff = day - firstDayOfWeek
  const firstDay = new Date(now)
  firstDay.setDate(now.getDate() - diff)
  return formatDate(firstDay)
}

/**
 * 获取本周最后一天
 * @param {number} firstDayOfWeek 一周的第一天 (0: 周日, 1: 周一)
 * @returns {string} YYYY-MM-DD格式的日期
 */
export function getLastDayOfWeek(firstDayOfWeek = 1) {
  const now = new Date()
  const day = now.getDay()
  const diff = day - firstDayOfWeek
  const lastDay = new Date(now)
  lastDay.setDate(now.getDate() - diff + 6)
  return formatDate(lastDay)
}

/**
 * 获取相对日期描述
 * @param {string} date YYYY-MM-DD格式的日期
 * @returns {string} 相对日期描述
 */
export function getRelativeDateText(date) {
  const today = getToday()
  const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const tomorrow = formatDate(new Date(Date.now() + 24 * 60 * 60 * 1000))
  
  if (date === today) {
    return '今天'
  } else if (date === yesterday) {
    return '昨天'
  } else if (date === tomorrow) {
    return '明天'
  } else {
    return formatDate(date, 'MM月DD日')
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 限制时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 显示提示信息
 * @param {string} title 提示标题
 * @param {string} icon 图标类型
 */
export function showToast(title, icon = 'none') {
  uni.showToast({
    title,
    icon,
    duration: 2000
  })
}

/**
 * 显示加载中
 * @param {string} title 加载文字
 */
export function showLoading(title = '加载中...') {
  uni.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载中
 */
export function hideLoading() {
  uni.hideLoading()
}
