"use strict";
const common_vendor = require("../common/vendor.js");
function formatAmount(amount, currency = "¥") {
  if (typeof amount !== "number") {
    amount = parseFloat(amount) || 0;
  }
  return `${currency}${amount.toFixed(2)}`;
}
function formatDate(date, format = "YYYY-MM-DD") {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  switch (format) {
    case "YYYY-MM-DD":
      return `${year}-${month}-${day}`;
    case "MM-DD":
      return `${month}-${day}`;
    case "YYYY年MM月DD日":
      return `${year}年${month}月${day}日`;
    case "MM月DD日":
      return `${month}月${day}日`;
    default:
      return `${year}-${month}-${day}`;
  }
}
function getToday() {
  return formatDate(/* @__PURE__ */ new Date());
}
function getFirstDayOfMonth() {
  const now = /* @__PURE__ */ new Date();
  return formatDate(new Date(now.getFullYear(), now.getMonth(), 1));
}
function getLastDayOfMonth() {
  const now = /* @__PURE__ */ new Date();
  return formatDate(new Date(now.getFullYear(), now.getMonth() + 1, 0));
}
function getFirstDayOfWeek(firstDayOfWeek = 1) {
  const now = /* @__PURE__ */ new Date();
  const day = now.getDay();
  const diff = day - firstDayOfWeek;
  const firstDay = new Date(now);
  firstDay.setDate(now.getDate() - diff);
  return formatDate(firstDay);
}
function getLastDayOfWeek(firstDayOfWeek = 1) {
  const now = /* @__PURE__ */ new Date();
  const day = now.getDay();
  const diff = day - firstDayOfWeek;
  const lastDay = new Date(now);
  lastDay.setDate(now.getDate() - diff + 6);
  return formatDate(lastDay);
}
function getRelativeDateText(date) {
  const today = getToday();
  const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1e3));
  const tomorrow = formatDate(new Date(Date.now() + 24 * 60 * 60 * 1e3));
  if (date === today) {
    return "今天";
  } else if (date === yesterday) {
    return "昨天";
  } else if (date === tomorrow) {
    return "明天";
  } else {
    return formatDate(date, "MM月DD日");
  }
}
function showToast(title, icon = "none") {
  common_vendor.index.showToast({
    title,
    icon,
    duration: 2e3
  });
}
exports.formatAmount = formatAmount;
exports.formatDate = formatDate;
exports.getFirstDayOfMonth = getFirstDayOfMonth;
exports.getFirstDayOfWeek = getFirstDayOfWeek;
exports.getLastDayOfMonth = getLastDayOfMonth;
exports.getLastDayOfWeek = getLastDayOfWeek;
exports.getRelativeDateText = getRelativeDateText;
exports.getToday = getToday;
exports.showToast = showToast;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/helpers.js.map
