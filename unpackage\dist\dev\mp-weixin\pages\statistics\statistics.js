"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "StatisticsPage",
  setup() {
    const currentTimeRange = common_vendor.ref("month");
    const currentCategoryType = common_vendor.ref("expense");
    const timeTabs = [
      { label: "本周", value: "week" },
      { label: "本月", value: "month" },
      { label: "本年", value: "year" }
    ];
    const filteredRecords = common_vendor.computed(() => {
      const now = /* @__PURE__ */ new Date();
      let startDate, endDate;
      switch (currentTimeRange.value) {
        case "week":
          startDate = utils_helpers.getFirstDayOfWeek();
          endDate = utils_helpers.getLastDayOfWeek();
          break;
        case "month":
          startDate = utils_helpers.getFirstDayOfMonth();
          endDate = utils_helpers.getLastDayOfMonth();
          break;
        case "year":
          startDate = utils_helpers.formatDate(new Date(now.getFullYear(), 0, 1));
          endDate = utils_helpers.formatDate(new Date(now.getFullYear(), 11, 31));
          break;
        default:
          return store_index.store.state.records;
      }
      return store_index.store.state.records.filter(
        (record) => record.date >= startDate && record.date <= endDate
      );
    });
    const totalIncome = common_vendor.computed(
      () => filteredRecords.value.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
    );
    const totalExpense = common_vendor.computed(
      () => filteredRecords.value.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
    );
    const netIncome = common_vendor.computed(() => totalIncome.value - totalExpense.value);
    const expenseByCategory = common_vendor.computed(() => {
      const stats = {};
      filteredRecords.value.filter((record) => record.type === "expense").forEach((record) => {
        if (!stats[record.categoryId]) {
          stats[record.categoryId] = {
            categoryId: record.categoryId,
            categoryName: record.categoryName,
            categoryIcon: record.categoryIcon,
            categoryColor: record.categoryColor,
            amount: 0,
            count: 0
          };
        }
        stats[record.categoryId].amount += record.amount;
        stats[record.categoryId].count += 1;
      });
      return Object.values(stats).sort((a, b) => b.amount - a.amount).slice(0, 8);
    });
    const maxExpenseAmount = common_vendor.computed(
      () => Math.max(...expenseByCategory.value.map((item) => item.amount), 1)
    );
    const trendData = common_vendor.computed(() => {
      const stats = {};
      filteredRecords.value.forEach((record) => {
        let key = record.date;
        if (currentTimeRange.value === "year") {
          key = record.date.substring(0, 7);
        }
        if (!stats[key]) {
          stats[key] = {
            date: key,
            income: 0,
            expense: 0
          };
        }
        if (record.type === "income") {
          stats[key].income += record.amount;
        } else {
          stats[key].expense += record.amount;
        }
      });
      return Object.values(stats).sort((a, b) => a.date.localeCompare(b.date)).slice(-7);
    });
    const maxTrendAmount = common_vendor.computed(() => {
      const amounts = trendData.value.flatMap((item) => [item.income, item.expense]);
      return Math.max(...amounts, 1);
    });
    const currentCategoryStats = common_vendor.computed(() => {
      const stats = {};
      filteredRecords.value.filter((record) => record.type === currentCategoryType.value).forEach((record) => {
        if (!stats[record.categoryId]) {
          stats[record.categoryId] = {
            categoryId: record.categoryId,
            categoryName: record.categoryName,
            categoryIcon: record.categoryIcon,
            categoryColor: record.categoryColor,
            amount: 0,
            count: 0
          };
        }
        stats[record.categoryId].amount += record.amount;
        stats[record.categoryId].count += 1;
      });
      return Object.values(stats).sort((a, b) => b.amount - a.amount);
    });
    const switchTimeRange = (range) => {
      currentTimeRange.value = range;
    };
    const switchCategoryType = (type) => {
      currentCategoryType.value = type;
    };
    const formatTrendDate = (date) => {
      if (currentTimeRange.value === "year") {
        return date.substring(5);
      }
      return utils_helpers.formatDate(date, "MM-DD");
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadRecords();
    });
    return {
      timeTabs,
      currentTimeRange,
      currentCategoryType,
      totalIncome,
      totalExpense,
      netIncome,
      expenseByCategory,
      maxExpenseAmount,
      trendData,
      maxTrendAmount,
      currentCategoryStats,
      formatAmount: utils_helpers.formatAmount,
      formatTrendDate,
      switchTimeRange,
      switchCategoryType
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($setup.timeTabs, (tab, k0, i0) => {
      return {
        a: common_vendor.t(tab.label),
        b: tab.value,
        c: $setup.currentTimeRange === tab.value ? 1 : "",
        d: common_vendor.o(($event) => $setup.switchTimeRange(tab.value), tab.value)
      };
    }),
    b: common_vendor.t($setup.formatAmount($setup.totalIncome)),
    c: common_vendor.t($setup.formatAmount($setup.totalExpense)),
    d: common_vendor.t($setup.formatAmount($setup.netIncome)),
    e: $setup.netIncome < 0 ? 1 : "",
    f: common_vendor.f($setup.expenseByCategory, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.categoryIcon),
        b: item.categoryColor,
        c: common_vendor.t(item.categoryName),
        d: common_vendor.t($setup.formatAmount(item.amount)),
        e: item.amount / $setup.maxExpenseAmount * 100 + "%",
        f: item.categoryColor,
        g: item.categoryId
      };
    }),
    g: common_vendor.f($setup.trendData, (item, k0, i0) => {
      return {
        a: common_vendor.t($setup.formatTrendDate(item.date)),
        b: item.income / $setup.maxTrendAmount * 100 + "%",
        c: item.expense / $setup.maxTrendAmount * 100 + "%",
        d: common_vendor.t($setup.formatAmount(item.income)),
        e: common_vendor.t($setup.formatAmount(item.expense)),
        f: item.date
      };
    }),
    h: $setup.currentCategoryType === "expense" ? 1 : "",
    i: common_vendor.o(($event) => $setup.switchCategoryType("expense")),
    j: $setup.currentCategoryType === "income" ? 1 : "",
    k: common_vendor.o(($event) => $setup.switchCategoryType("income")),
    l: common_vendor.f($setup.currentCategoryStats, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.categoryIcon),
        b: item.categoryColor,
        c: common_vendor.t(item.categoryName),
        d: common_vendor.t(item.count),
        e: common_vendor.t($setup.formatAmount(item.amount)),
        f: common_vendor.t((item.amount / ($setup.currentCategoryType === "expense" ? $setup.totalExpense : $setup.totalIncome) * 100).toFixed(1)),
        g: item.categoryId
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fc23ec97"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/statistics/statistics.js.map
