{"version": 3, "file": "test-cloud.js", "sources": ["utils/test-cloud.js"], "sourcesContent": ["/**\n * 云函数测试工具\n */\n\nexport async function testCloudFunction() {\n  console.log('开始测试云函数连接...');\n\n  try {\n    // 先测试最简单的测试云函数\n    console.log('1. 测试基础云函数...');\n    const testResult = await uniCloud.callFunction({\n      name: 'test',\n      data: { message: 'hello from client' }\n    });\n\n    console.log('基础云函数测试结果:', testResult);\n\n    if (testResult.result && testResult.result.code === 200) {\n      console.log('✅ 基础云函数调用成功');\n\n      // 测试数据库初始化\n      console.log('2. 测试数据库初始化...');\n      const initResult = await uniCloud.callFunction({\n        name: 'init-db',\n        data: {}\n      });\n\n      console.log('数据库初始化结果:', initResult);\n\n      if (initResult.result && initResult.result.code === 200) {\n        console.log('✅ 数据库初始化成功');\n\n        // 测试获取分类\n        console.log('3. 测试获取分类...');\n        const categoriesResult = await uniCloud.callFunction({\n          name: 'bookkeeping',\n          data: {\n            action: 'getCategories',\n            data: { type: 'expense' }\n          }\n        });\n\n        console.log('获取分类结果:', categoriesResult);\n\n        if (categoriesResult.result && categoriesResult.result.code === 200) {\n          console.log('✅ 获取分类成功，分类数量:', categoriesResult.result.data.length);\n        } else {\n          console.log('❌ 获取分类失败:', categoriesResult.result ? categoriesResult.result.message : '未知错误');\n        }\n      } else {\n        console.log('❌ 数据库初始化失败:', initResult.result ? initResult.result.message : '未知错误');\n      }\n    } else {\n      console.log('❌ 基础云函数调用失败:', testResult.result ? testResult.result.message : '未知错误');\n    }\n\n    console.log('🎉 云函数基础测试完成');\n\n    return {\n      success: true,\n      message: '云函数测试完成'\n    };\n    \n  } catch (error) {\n    console.error('❌ 云函数测试失败:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n}\n\nexport async function testCloudConnection() {\n  try {\n    console.log('测试云函数连接...');\n    \n    // 简单的ping测试\n    const result = await uniCloud.callFunction({\n      name: 'bookkeeping',\n      data: {\n        action: 'getCategories',\n        data: {}\n      }\n    });\n    \n    console.log('连接测试结果:', result);\n    \n    if (result.result) {\n      return {\n        success: true,\n        message: '云函数连接正常'\n      };\n    } else {\n      return {\n        success: false,\n        message: '云函数连接失败'\n      };\n    }\n  } catch (error) {\n    console.error('云函数连接测试失败:', error);\n    return {\n      success: false,\n      error: error.message\n    };\n  }\n}\n"], "names": ["uni", "uniCloud"], "mappings": ";;AAIO,eAAe,oBAAoB;AACxCA,gBAAAA,MAAY,MAAA,OAAA,4BAAA,cAAc;AAE1B,MAAI;AAEFA,kBAAAA,MAAA,MAAA,OAAA,6BAAY,eAAe;AAC3B,UAAM,aAAa,MAAMC,cAAQ,GAAC,aAAa;AAAA,MAC7C,MAAM;AAAA,MACN,MAAM,EAAE,SAAS,oBAAqB;AAAA,IAC5C,CAAK;AAEDD,kBAAA,MAAA,MAAA,OAAA,6BAAY,cAAc,UAAU;AAEpC,QAAI,WAAW,UAAU,WAAW,OAAO,SAAS,KAAK;AACvDA,oBAAAA,MAAY,MAAA,OAAA,6BAAA,aAAa;AAGzBA,oBAAAA,MAAY,MAAA,OAAA,6BAAA,gBAAgB;AAC5B,YAAM,aAAa,MAAMC,cAAQ,GAAC,aAAa;AAAA,QAC7C,MAAM;AAAA,QACN,MAAM,CAAE;AAAA,MAChB,CAAO;AAEDD,oBAAA,MAAA,MAAA,OAAA,6BAAY,aAAa,UAAU;AAEnC,UAAI,WAAW,UAAU,WAAW,OAAO,SAAS,KAAK;AACvDA,sBAAAA,gDAAY,YAAY;AAGxBA,sBAAAA,MAAY,MAAA,OAAA,6BAAA,cAAc;AAC1B,cAAM,mBAAmB,MAAMC,cAAQ,GAAC,aAAa;AAAA,UACnD,MAAM;AAAA,UACN,MAAM;AAAA,YACJ,QAAQ;AAAA,YACR,MAAM,EAAE,MAAM,UAAW;AAAA,UAC1B;AAAA,QACX,CAAS;AAEDD,sBAAA,MAAA,MAAA,OAAA,6BAAY,WAAW,gBAAgB;AAEvC,YAAI,iBAAiB,UAAU,iBAAiB,OAAO,SAAS,KAAK;AACnEA,8BAAA,MAAA,OAAA,6BAAY,kBAAkB,iBAAiB,OAAO,KAAK,MAAM;AAAA,QAC3E,OAAe;AACLA,wBAAAA,gDAAY,aAAa,iBAAiB,SAAS,iBAAiB,OAAO,UAAU,MAAM;AAAA,QAC5F;AAAA,MACT,OAAa;AACLA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,eAAe,WAAW,SAAS,WAAW,OAAO,UAAU,MAAM;AAAA,MAClF;AAAA,IACP,OAAW;AACLA,oBAAAA,gDAAY,gBAAgB,WAAW,SAAS,WAAW,OAAO,UAAU,MAAM;AAAA,IACnF;AAEDA,kBAAAA,gDAAY,cAAc;AAE1B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,SAAS;AAAA,IACf;AAAA,EAEG,SAAQ,OAAO;AACdA,kBAAc,MAAA,MAAA,SAAA,6BAAA,cAAc,KAAK;AACjC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,MAAM;AAAA,IACnB;AAAA,EACG;AACH;;"}