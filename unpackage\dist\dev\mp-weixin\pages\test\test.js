"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      basicTest: false,
      cloudTest: false,
      storageTest: false,
      cloudTestStatus: "未测试",
      logs: []
    };
  },
  onLoad() {
    this.addLog("测试页面加载完成");
    this.runBasicTests();
  },
  methods: {
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.push(`[${timestamp}] ${message}`);
    },
    runBasicTests() {
      try {
        const testObj = { test: true };
        const testArray = [1, 2, 3];
        const testString = `测试字符串 ${Date.now()}`;
        this.basicTest = true;
        this.addLog("✅ 基础功能测试通过");
        common_vendor.index.setStorageSync("test_key", "test_value");
        const stored = common_vendor.index.getStorageSync("test_key");
        this.storageTest = stored === "test_value";
        this.addLog(this.storageTest ? "✅ 存储功能测试通过" : "❌ 存储功能测试失败");
      } catch (error) {
        this.addLog(`❌ 基础测试失败: ${error.message}`);
      }
    },
    async runTests() {
      this.addLog("开始运行完整测试...");
      try {
        this.cloudTestStatus = "测试中...";
        this.addLog("正在测试云函数连接...");
        const result = await common_vendor.nr.callFunction({
          name: "test",
          data: { message: "hello from test page" }
        });
        if (result.result && result.result.code === 200) {
          this.cloudTest = true;
          this.cloudTestStatus = "✅ 连接正常";
          this.addLog("✅ 云函数连接测试通过");
        } else {
          this.cloudTestStatus = "❌ 连接失败";
          this.addLog("❌ 云函数返回异常");
        }
      } catch (error) {
        this.cloudTest = false;
        this.cloudTestStatus = "❌ 连接失败";
        this.addLog(`❌ 云函数连接失败: ${error.message}`);
      }
      this.addLog("测试完成");
    },
    goHome() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.basicTest ? "✅ 正常" : "❌ 异常"),
    b: $data.basicTest ? 1 : "",
    c: common_vendor.t($data.cloudTestStatus),
    d: $data.cloudTest ? 1 : "",
    e: common_vendor.t($data.storageTest ? "✅ 正常" : "❌ 异常"),
    f: $data.storageTest ? 1 : "",
    g: common_vendor.o((...args) => $options.runTests && $options.runTests(...args)),
    h: common_vendor.o((...args) => $options.goHome && $options.goHome(...args)),
    i: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-727d09f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/test.js.map
