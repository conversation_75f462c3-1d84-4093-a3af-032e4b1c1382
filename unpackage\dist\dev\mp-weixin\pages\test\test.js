"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_cloudDebug = require("../../utils/cloud-debug.js");
const _sfc_main = {
  data() {
    return {
      basicTest: false,
      cloudTest: false,
      storageTest: false,
      cloudTestStatus: "未测试",
      logs: []
    };
  },
  onLoad() {
    this.addLog("测试页面加载完成");
    this.runBasicTests();
  },
  methods: {
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.push(`[${timestamp}] ${message}`);
    },
    runBasicTests() {
      try {
        const testObj = { test: true };
        const testArray = [1, 2, 3];
        const testString = `测试字符串 ${Date.now()}`;
        this.basicTest = true;
        this.addLog("✅ 基础功能测试通过");
        common_vendor.index.setStorageSync("test_key", "test_value");
        const stored = common_vendor.index.getStorageSync("test_key");
        this.storageTest = stored === "test_value";
        this.addLog(this.storageTest ? "✅ 存储功能测试通过" : "❌ 存储功能测试失败");
      } catch (error) {
        this.addLog(`❌ 基础测试失败: ${error.message}`);
      }
    },
    async runTests() {
      this.addLog("开始运行完整测试...");
      await this.testBasicCloudFunction();
      await this.testBookkeepingFunction();
      await this.testDatabaseInit();
      await this.testAddRecord();
      this.addLog("所有测试完成");
    },
    async testBasicCloudFunction() {
      try {
        this.cloudTestStatus = "测试基础连接...";
        this.addLog("正在测试基础云函数连接...");
        const result = await common_vendor.nr.callFunction({
          name: "test",
          data: { message: "hello from test page" }
        });
        if (result.result && result.result.code === 200) {
          this.cloudTest = true;
          this.cloudTestStatus = "✅ 基础连接正常";
          this.addLog("✅ 基础云函数连接测试通过");
        } else {
          this.cloudTestStatus = "❌ 基础连接失败";
          this.addLog("❌ 基础云函数返回异常:", JSON.stringify(result));
        }
      } catch (error) {
        this.cloudTest = false;
        this.cloudTestStatus = "❌ 基础连接失败";
        this.addLog(`❌ 基础云函数连接失败: ${error.message}`);
      }
    },
    async testBookkeepingFunction() {
      try {
        this.addLog("正在测试 bookkeeping 云函数...");
        const result = await common_vendor.nr.callFunction({
          name: "bookkeeping",
          data: {
            action: "getCategories",
            data: {}
          }
        });
        this.addLog("bookkeeping 云函数响应:", JSON.stringify(result));
        if (result.result && result.result.code === 200) {
          this.addLog("✅ bookkeeping 云函数测试通过");
        } else {
          this.addLog("❌ bookkeeping 云函数返回异常");
        }
      } catch (error) {
        this.addLog(`❌ bookkeeping 云函数测试失败: ${error.message}`);
      }
    },
    async testDatabaseInit() {
      try {
        this.addLog("正在测试数据库初始化...");
        const result = await common_vendor.nr.callFunction({
          name: "init-db",
          data: {}
        });
        this.addLog("数据库初始化响应:", JSON.stringify(result));
        if (result.result && result.result.code === 200) {
          this.addLog("✅ 数据库初始化测试通过");
        } else {
          this.addLog("❌ 数据库初始化失败");
        }
      } catch (error) {
        this.addLog(`❌ 数据库初始化测试失败: ${error.message}`);
      }
    },
    async testAddRecord() {
      try {
        this.addLog("正在测试添加记录...");
        const testRecord = {
          type: "expense",
          amount: 10.5,
          categoryId: "cat1",
          categoryName: "餐饮",
          categoryIcon: "🍽️",
          categoryColor: "#FF9800",
          note: "测试记录",
          date: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
          time: (/* @__PURE__ */ new Date()).toTimeString().split(" ")[0].substring(0, 5)
        };
        const result = await common_vendor.nr.callFunction({
          name: "bookkeeping",
          data: {
            action: "addRecord",
            data: testRecord
          }
        });
        this.addLog("添加记录响应:", JSON.stringify(result));
        if (result.result && result.result.code === 200) {
          this.addLog("✅ 添加记录测试通过");
        } else {
          this.addLog("❌ 添加记录失败");
        }
      } catch (error) {
        this.addLog(`❌ 添加记录测试失败: ${error.message}`);
      }
    },
    async runDiagnosis() {
      var _a, _b;
      this.addLog("开始完整诊断...");
      this.logs = [];
      try {
        const results = await utils_cloudDebug.CloudDebug.runFullDiagnosis();
        const report = utils_cloudDebug.CloudDebug.generateReport(results);
        const reportLines = report.split("\n");
        reportLines.forEach((line) => {
          if (line.trim()) {
            this.addLog(line);
          }
        });
        this.cloudTest = ((_a = results.basicConnection) == null ? void 0 : _a.success) || false;
        this.cloudTestStatus = ((_b = results.addRecord) == null ? void 0 : _b.success) ? "✅ 完全正常" : "❌ 存在问题";
      } catch (error) {
        this.addLog(`❌ 诊断过程失败: ${error.message}`);
      }
    },
    goHome() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.basicTest ? "✅ 正常" : "❌ 异常"),
    b: $data.basicTest ? 1 : "",
    c: common_vendor.t($data.cloudTestStatus),
    d: $data.cloudTest ? 1 : "",
    e: common_vendor.t($data.storageTest ? "✅ 正常" : "❌ 异常"),
    f: $data.storageTest ? 1 : "",
    g: common_vendor.o((...args) => $options.runTests && $options.runTests(...args)),
    h: common_vendor.o((...args) => $options.runDiagnosis && $options.runDiagnosis(...args)),
    i: common_vendor.o((...args) => $options.goHome && $options.goHome(...args)),
    j: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-727d09f0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/test.js.map
