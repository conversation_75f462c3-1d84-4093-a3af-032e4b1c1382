/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-between {
  justify-content: space-between;
}
.flex-center {
  justify-content: center;
  align-items: center;
}
.text-income {
  color: #4CAF50;
}
.text-expense {
  color: #F44336;
}
.text-bold {
  font-weight: bold;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-between {
  justify-content: space-between;
}
.flex-center {
  justify-content: center;
  align-items: center;
}
.text-income {
  color: #4CAF50;
}
.text-expense {
  color: #F44336;
}
.text-bold {
  font-weight: bold;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
/* 全局样式重置 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}
/* 全局通用样式 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}
.btn.btn-primary {
  background-color: #4CAF50;
  color: white;
}
.btn.btn-primary:hover {
  background-color: #3d8b40;
}
.btn.btn-secondary {
  background-color: #ffffff;
  color: #333;
  border: 1px solid #e5e5e5;
}
.btn.btn-secondary:hover {
  background-color: #f1f1f1;
}
.btn.btn-danger {
  background-color: #F44336;
  color: white;
}
.btn.btn-danger:hover {
  background-color: #ea1c0d;
}
.btn.btn-small {
  padding: 8px 16px;
  font-size: 14px;
}
.btn.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
/* 输入框样式 */
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 16px;
  background-color: #ffffff;
}
.input:focus {
  border-color: #4CAF50;
  outline: none;
}
.input::-webkit-input-placeholder {
  color: #808080;
}
.input::placeholder {
  color: #808080;
}
/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}
/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
}
.list-item:last-child {
  border-bottom: none;
}
.list-item:active {
  background-color: #f1f1f1;
}
/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
.loading .loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e5e5;
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}
.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}
.empty-state .empty-text {
  color: #999;
  font-size: 16px;
  margin-bottom: 8px;
}
.empty-state .empty-desc {
  color: #808080;
  font-size: 14px;
}
/* 页面过渡动画 */
.page-enter-active, .page-leave-active {
  transition: all 0.3s ease;
}
.page-enter-from {
  opacity: 0;
  transform: translateX(100%);
}
.page-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}
/* 卡片悬浮效果 */
.card-hover {
  transition: all 0.3s ease;
}
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}
/* 按钮点击效果 */
.btn-press {
  transition: all 0.2s ease;
}
.btn-press:active {
  transform: scale(0.98);
}
/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
@keyframes fadeIn {
from {
    opacity: 0;
    transform: translateY(20px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/* 滑入动画 */
.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}
@keyframes slideInUp {
from {
    opacity: 0;
    transform: translateY(30px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/* 弹跳动画 */
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}
@keyframes bounceIn {
0% {
    opacity: 0;
    transform: scale(0.3);
}
50% {
    opacity: 1;
    transform: scale(1.05);
}
70% {
    transform: scale(0.9);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}