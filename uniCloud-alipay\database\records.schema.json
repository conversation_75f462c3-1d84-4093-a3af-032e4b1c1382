{"bsonType": "object", "description": "记账记录表", "required": ["id", "type", "amount", "categoryId", "date"], "properties": {"_id": {"description": "存储文档 ID（自动生成）"}, "id": {"bsonType": "string", "description": "记录唯一标识"}, "type": {"bsonType": "string", "enum": ["income", "expense"], "description": "记录类型：收入或支出"}, "amount": {"bsonType": "number", "minimum": 0, "description": "金额"}, "categoryId": {"bsonType": "string", "description": "分类ID"}, "categoryName": {"bsonType": "string", "description": "分类名称"}, "categoryIcon": {"bsonType": "string", "description": "分类图标"}, "categoryColor": {"bsonType": "string", "description": "分类颜色"}, "note": {"bsonType": "string", "description": "备注信息"}, "date": {"bsonType": "string", "description": "记录日期 YYYY-MM-DD"}, "time": {"bsonType": "string", "description": "记录时间 HH:mm"}, "createTime": {"bsonType": "date", "description": "创建时间"}, "updateTime": {"bsonType": "date", "description": "更新时间"}}}