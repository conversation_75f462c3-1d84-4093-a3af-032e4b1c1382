"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "AddPage",
  setup() {
    const formData = common_vendor.ref({
      type: "expense",
      amount: 0,
      categoryId: null,
      categoryName: "",
      categoryIcon: "",
      categoryColor: "",
      note: "",
      date: utils_helpers.getToday(),
      time: (/* @__PURE__ */ new Date()).toTimeString().split(" ")[0].substring(0, 5)
    });
    const currentType = common_vendor.ref("expense");
    const selectedCategory = common_vendor.ref(null);
    const displayAmount = common_vendor.ref("");
    const amountFocused = common_vendor.ref(false);
    const isEdit = common_vendor.ref(false);
    const editId = common_vendor.ref(null);
    const quickAmounts = common_vendor.ref([10, 20, 50, 100, 200, 500]);
    const noteSuggestions = common_vendor.computed(() => {
      var _a;
      if (!formData.value.note || formData.value.note.length < 2)
        return [];
      const suggestions = {
        "餐饮": ["早餐", "午餐", "晚餐", "下午茶", "夜宵"],
        "交通": ["地铁", "公交", "打车", "加油", "停车费"],
        "购物": ["超市", "网购", "服装", "日用品"],
        "娱乐": ["电影", "游戏", "KTV", "聚会"],
        "医疗": ["看病", "买药", "体检", "保健品"],
        "教育": ["培训", "书籍", "课程", "学费"]
      };
      const categoryName = ((_a = selectedCategory.value) == null ? void 0 : _a.name) || "";
      return suggestions[categoryName] || [];
    });
    const currentCategories = common_vendor.computed(
      () => store_index.store.state.categories[currentType.value] || []
    );
    const canSave = common_vendor.computed(
      () => formData.value.amount > 0 && selectedCategory.value
    );
    const minDate = common_vendor.computed(() => "2020-01-01");
    const maxDate = common_vendor.computed(() => {
      const future = /* @__PURE__ */ new Date();
      future.setFullYear(future.getFullYear() + 1);
      return utils_helpers.formatDate(future);
    });
    const switchType = (type) => {
      currentType.value = type;
      formData.value.type = type;
      selectedCategory.value = null;
      formData.value.categoryId = null;
      formData.value.categoryName = "";
      formData.value.categoryIcon = "";
      formData.value.categoryColor = "";
    };
    const onAmountInput = (e) => {
      let value = e.detail.value;
      value = value.replace(/[^\d.]/g, "");
      const parts = value.split(".");
      if (parts.length > 2) {
        value = parts[0] + "." + parts.slice(1).join("");
      }
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + "." + parts[1].substring(0, 2);
      }
      displayAmount.value = value;
      formData.value.amount = parseFloat(value) || 0;
    };
    const onAmountFocus = () => {
      amountFocused.value = true;
    };
    const onAmountBlur = () => {
      amountFocused.value = false;
    };
    const setQuickAmount = (amount) => {
      displayAmount.value = amount.toString();
      formData.value.amount = amount;
    };
    const showCalculator = () => {
      utils_helpers.showToast("计算器功能开发中");
    };
    const manageCategories = () => {
      utils_helpers.showToast("分类管理功能开发中");
    };
    const addNewCategory = () => {
      utils_helpers.showToast("添加分类功能开发中");
    };
    const setCurrentTime = () => {
      const now = /* @__PURE__ */ new Date();
      formData.value.date = utils_helpers.getToday();
      formData.value.time = now.toTimeString().split(" ")[0].substring(0, 5);
      utils_helpers.showToast("已设置为当前时间");
    };
    const onNoteFocus = () => {
    };
    const onNoteBlur = () => {
    };
    const selectNoteSuggestion = (suggestion) => {
      formData.value.note = suggestion;
    };
    const deleteRecord = () => {
      if (!isEdit.value)
        return;
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这条记录吗？",
        success: (res) => {
          if (res.confirm) {
            store_index.store.actions.deleteRecord(editId.value);
            common_vendor.index.navigateBack();
          }
        }
      });
    };
    const selectCategory = (category) => {
      selectedCategory.value = category;
      formData.value.categoryId = category.id;
      formData.value.categoryName = category.name;
      formData.value.categoryIcon = category.icon;
      formData.value.categoryColor = category.color;
    };
    const showDatePicker = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "请使用系统日期选择器",
        showCancel: false
      });
    };
    const showTimePicker = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "请使用系统时间选择器",
        showCancel: false
      });
    };
    const onDateChange = (e) => {
      formData.value.date = e.detail.value;
    };
    const onTimeChange = (e) => {
      formData.value.time = e.detail.value;
    };
    const save = () => {
      if (!canSave.value) {
        utils_helpers.showToast("请填写完整信息");
        return;
      }
      try {
        if (isEdit.value) {
          store_index.store.actions.updateRecord(editId.value, formData.value);
        } else {
          store_index.store.actions.addRecord(formData.value);
        }
        common_vendor.index.navigateBack();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/add/add.vue:434", "保存失败:", error);
        utils_helpers.showToast("保存失败");
      }
    };
    const cancel = () => {
      common_vendor.index.navigateBack();
    };
    const loadEditRecord = (recordId) => {
      const record = store_index.store.state.records.find((r) => r.id === recordId);
      if (record) {
        isEdit.value = true;
        editId.value = recordId;
        formData.value = {
          type: record.type,
          amount: record.amount,
          categoryId: record.categoryId,
          categoryName: record.categoryName,
          categoryIcon: record.categoryIcon,
          categoryColor: record.categoryColor,
          note: record.note,
          date: record.date,
          time: record.time
        };
        currentType.value = record.type;
        displayAmount.value = record.amount.toString();
        const categories = store_index.store.state.categories[record.type] || [];
        selectedCategory.value = categories.find((cat) => cat.id === record.categoryId);
      }
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadCategories();
      const quickAddType = common_vendor.index.getStorageSync("quickAddType");
      if (quickAddType) {
        switchType(quickAddType);
        common_vendor.index.removeStorageSync("quickAddType");
      }
      const editRecordId = common_vendor.index.getStorageSync("editRecordId");
      if (editRecordId) {
        loadEditRecord(editRecordId);
        common_vendor.index.removeStorageSync("editRecordId");
      }
    });
    return {
      formData,
      currentType,
      selectedCategory,
      displayAmount,
      amountFocused,
      isEdit,
      editId,
      quickAmounts,
      noteSuggestions,
      currentCategories,
      canSave,
      minDate,
      maxDate,
      switchType,
      onAmountInput,
      onAmountFocus,
      onAmountBlur,
      setQuickAmount,
      showCalculator,
      manageCategories,
      addNewCategory,
      setCurrentTime,
      onNoteFocus,
      onNoteBlur,
      selectNoteSuggestion,
      deleteRecord,
      selectCategory,
      showDatePicker,
      showTimePicker,
      onDateChange,
      onTimeChange,
      save,
      cancel,
      formatDate: utils_helpers.formatDate
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($setup.isEdit ? "编辑记录" : "记一笔"),
    b: common_vendor.t($setup.currentType === "expense" ? "记录支出" : "记录收入"),
    c: $setup.isEdit
  }, $setup.isEdit ? {
    d: common_vendor.o((...args) => $setup.deleteRecord && $setup.deleteRecord(...args))
  } : {}, {
    e: $setup.currentType === "expense" ? 1 : "",
    f: common_vendor.o(($event) => $setup.switchType("expense")),
    g: $setup.currentType === "income" ? 1 : "",
    h: common_vendor.o(($event) => $setup.switchType("income")),
    i: common_vendor.f($setup.quickAmounts, (amount, k0, i0) => {
      return {
        a: common_vendor.t(amount),
        b: amount,
        c: common_vendor.o(($event) => $setup.setQuickAmount(amount), amount)
      };
    }),
    j: $setup.displayAmount,
    k: common_vendor.o((...args) => $setup.onAmountInput && $setup.onAmountInput(...args)),
    l: common_vendor.o((...args) => $setup.onAmountFocus && $setup.onAmountFocus(...args)),
    m: common_vendor.o((...args) => $setup.onAmountBlur && $setup.onAmountBlur(...args)),
    n: $setup.amountFocused,
    o: common_vendor.o((...args) => $setup.showCalculator && $setup.showCalculator(...args)),
    p: common_vendor.t($setup.currentCategories.length),
    q: common_vendor.o((...args) => $setup.manageCategories && $setup.manageCategories(...args)),
    r: common_vendor.f($setup.currentCategories, (category, index, i0) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.t(category.icon),
        b: category.color,
        c: ((_a = $setup.selectedCategory) == null ? void 0 : _a.id) === category.id
      }, ((_b = $setup.selectedCategory) == null ? void 0 : _b.id) === category.id ? {} : {}, {
        d: common_vendor.t(category.name),
        e: category.id,
        f: ((_c = $setup.selectedCategory) == null ? void 0 : _c.id) === category.id ? 1 : "",
        g: index * 0.05 + "s",
        h: common_vendor.o(($event) => $setup.selectCategory(category), category.id)
      });
    }),
    s: common_vendor.o((...args) => $setup.addNewCategory && $setup.addNewCategory(...args)),
    t: common_vendor.o((...args) => $setup.setCurrentTime && $setup.setCurrentTime(...args)),
    v: common_vendor.t($setup.formatDate($setup.formData.date, "YYYY年MM月DD日")),
    w: common_vendor.o((...args) => $setup.showDatePicker && $setup.showDatePicker(...args)),
    x: common_vendor.t($setup.formData.time),
    y: common_vendor.o((...args) => $setup.showTimePicker && $setup.showTimePicker(...args)),
    z: common_vendor.t($setup.formData.note.length),
    A: common_vendor.o((...args) => $setup.onNoteFocus && $setup.onNoteFocus(...args)),
    B: common_vendor.o((...args) => $setup.onNoteBlur && $setup.onNoteBlur(...args)),
    C: $setup.formData.note,
    D: common_vendor.o(($event) => $setup.formData.note = $event.detail.value),
    E: $setup.noteSuggestions.length > 0
  }, $setup.noteSuggestions.length > 0 ? {
    F: common_vendor.f($setup.noteSuggestions, (suggestion, k0, i0) => {
      return {
        a: common_vendor.t(suggestion),
        b: suggestion,
        c: common_vendor.o(($event) => $setup.selectNoteSuggestion(suggestion), suggestion)
      };
    })
  } : {}, {
    G: common_vendor.o((...args) => $setup.cancel && $setup.cancel(...args)),
    H: common_vendor.t($setup.isEdit ? "更新" : "保存"),
    I: common_vendor.o((...args) => $setup.save && $setup.save(...args)),
    J: !$setup.canSave,
    K: $setup.formData.date,
    L: common_vendor.o((...args) => $setup.onDateChange && $setup.onDateChange(...args)),
    M: $setup.minDate,
    N: $setup.maxDate,
    O: $setup.formData.time,
    P: common_vendor.o((...args) => $setup.onTimeChange && $setup.onTimeChange(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e8d2fd40"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/add/add.js.map
