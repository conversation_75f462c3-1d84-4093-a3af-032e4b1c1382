"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "AddPage",
  setup() {
    const formData = common_vendor.ref({
      type: "expense",
      amount: 0,
      categoryId: null,
      categoryName: "",
      categoryIcon: "",
      categoryColor: "",
      note: "",
      date: utils_helpers.getToday(),
      time: (/* @__PURE__ */ new Date()).toTimeString().split(" ")[0].substring(0, 5)
    });
    const currentType = common_vendor.ref("expense");
    const selectedCategory = common_vendor.ref(null);
    const displayAmount = common_vendor.ref("");
    const amountFocused = common_vendor.ref(true);
    const isEdit = common_vendor.ref(false);
    const editId = common_vendor.ref("");
    const currentCategories = common_vendor.computed(
      () => store_index.store.state.categories[currentType.value] || []
    );
    const canSave = common_vendor.computed(
      () => formData.value.amount > 0 && selectedCategory.value
    );
    const minDate = common_vendor.computed(() => "2020-01-01");
    const maxDate = common_vendor.computed(() => {
      const future = /* @__PURE__ */ new Date();
      future.setFullYear(future.getFullYear() + 1);
      return utils_helpers.formatDate(future);
    });
    const switchType = (type) => {
      currentType.value = type;
      formData.value.type = type;
      selectedCategory.value = null;
      formData.value.categoryId = null;
      formData.value.categoryName = "";
      formData.value.categoryIcon = "";
      formData.value.categoryColor = "";
    };
    const onAmountInput = (e) => {
      let value = e.detail.value;
      value = value.replace(/[^\d.]/g, "");
      const parts = value.split(".");
      if (parts.length > 2) {
        value = parts[0] + "." + parts.slice(1).join("");
      }
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + "." + parts[1].substring(0, 2);
      }
      displayAmount.value = value;
      formData.value.amount = parseFloat(value) || 0;
    };
    const selectCategory = (category) => {
      selectedCategory.value = category;
      formData.value.categoryId = category.id;
      formData.value.categoryName = category.name;
      formData.value.categoryIcon = category.icon;
      formData.value.categoryColor = category.color;
    };
    const showDatePicker = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "请使用系统日期选择器",
        showCancel: false
      });
    };
    const showTimePicker = () => {
      common_vendor.index.showModal({
        title: "提示",
        content: "请使用系统时间选择器",
        showCancel: false
      });
    };
    const onDateChange = (e) => {
      formData.value.date = e.detail.value;
    };
    const onTimeChange = (e) => {
      formData.value.time = e.detail.value;
    };
    const save = () => {
      if (!canSave.value) {
        utils_helpers.showToast("请填写完整信息");
        return;
      }
      try {
        if (isEdit.value) {
          store_index.store.actions.updateRecord(editId.value, formData.value);
        } else {
          store_index.store.actions.addRecord(formData.value);
        }
        common_vendor.index.navigateBack();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/add/add.vue:238", "保存失败:", error);
        utils_helpers.showToast("保存失败");
      }
    };
    const cancel = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadCategories();
    });
    return {
      formData,
      currentType,
      selectedCategory,
      displayAmount,
      amountFocused,
      isEdit,
      currentCategories,
      canSave,
      minDate,
      maxDate,
      switchType,
      onAmountInput,
      selectCategory,
      showDatePicker,
      showTimePicker,
      onDateChange,
      onTimeChange,
      save,
      cancel,
      formatDate: utils_helpers.formatDate
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $setup.currentType === "expense" ? 1 : "",
    b: common_vendor.o(($event) => $setup.switchType("expense")),
    c: $setup.currentType === "income" ? 1 : "",
    d: common_vendor.o(($event) => $setup.switchType("income")),
    e: $setup.displayAmount,
    f: common_vendor.o((...args) => $setup.onAmountInput && $setup.onAmountInput(...args)),
    g: $setup.amountFocused,
    h: common_vendor.f($setup.currentCategories, (category, k0, i0) => {
      var _a;
      return {
        a: common_vendor.t(category.icon),
        b: category.color,
        c: common_vendor.t(category.name),
        d: category.id,
        e: ((_a = $setup.selectedCategory) == null ? void 0 : _a.id) === category.id ? 1 : "",
        f: common_vendor.o(($event) => $setup.selectCategory(category), category.id)
      };
    }),
    i: common_vendor.t($setup.formatDate($setup.formData.date, "YYYY年MM月DD日")),
    j: common_vendor.o((...args) => $setup.showDatePicker && $setup.showDatePicker(...args)),
    k: common_vendor.t($setup.formData.time),
    l: common_vendor.o((...args) => $setup.showTimePicker && $setup.showTimePicker(...args)),
    m: $setup.formData.note,
    n: common_vendor.o(($event) => $setup.formData.note = $event.detail.value),
    o: common_vendor.o((...args) => $setup.cancel && $setup.cancel(...args)),
    p: common_vendor.t($setup.isEdit ? "更新" : "保存"),
    q: common_vendor.o((...args) => $setup.save && $setup.save(...args)),
    r: !$setup.canSave,
    s: $setup.formData.date,
    t: common_vendor.o((...args) => $setup.onDateChange && $setup.onDateChange(...args)),
    v: $setup.minDate,
    w: $setup.maxDate,
    x: $setup.formData.time,
    y: common_vendor.o((...args) => $setup.onTimeChange && $setup.onTimeChange(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e8d2fd40"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/add/add.js.map
