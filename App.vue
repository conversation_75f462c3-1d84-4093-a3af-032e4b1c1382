<script>
import store from './store/index.js'

export default {
  onLaunch: function() {
    console.log('记账本启动')
    // 初始化数据
    store.actions.init()
  },
  onShow: function() {
    console.log('记账本显示')
  },
  onHide: function() {
    console.log('记账本隐藏')
  }
}
</script>

<style lang="scss">
@import './uni.scss';

/* 全局样式重置 */
page {
  background-color: $uni-bg-color-grey;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 全局通用样式 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &.btn-primary {
    background-color: $uni-color-primary;
    color: white;

    &:hover {
      background-color: darken($uni-color-primary, 10%);
    }
  }

  &.btn-secondary {
    background-color: $uni-bg-color;
    color: $uni-text-color;
    border: 1px solid $uni-border-color;

    &:hover {
      background-color: $uni-bg-color-hover;
    }
  }

  &.btn-danger {
    background-color: $uni-color-error;
    color: white;

    &:hover {
      background-color: darken($uni-color-error, 10%);
    }
  }

  &.btn-small {
    padding: 8px 16px;
    font-size: 14px;
  }

  &.btn-large {
    padding: 16px 32px;
    font-size: 18px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid $uni-border-color;
  border-radius: 8px;
  font-size: 16px;
  background-color: $uni-bg-color;

  &:focus {
    border-color: $uni-color-primary;
    outline: none;
  }

  &::placeholder {
    color: $uni-text-color-placeholder;
  }
}

/* 卡片样式 */
.card {
  background-color: $uni-bg-color;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

/* 列表项样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: $uni-bg-color;
  border-bottom: 1px solid $uni-border-color;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid $uni-border-color;
    border-top: 2px solid $uni-color-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    color: $uni-text-color-grey;
    font-size: 16px;
    margin-bottom: 8px;
  }

  .empty-desc {
    color: $uni-text-color-placeholder;
    font-size: 14px;
  }
}

/* 页面过渡动画 */
.page-enter-active, .page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

/* 卡片悬浮效果 */
.card-hover {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

/* 按钮点击效果 */
.btn-press {
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑入动画 */
.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹跳动画 */
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
