"use strict";
const common_vendor = require("../common/vendor.js");
const CloudDebug = {
  // 测试基础云函数连接
  async testBasicConnection() {
    try {
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:10", "测试基础云函数连接...");
      const result = await common_vendor.nr.callFunction({
        name: "test",
        data: { message: "debug test" }
      });
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:17", "基础连接测试结果:", result);
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : "连接失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-debug.js:25", "基础连接测试失败:", error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },
  // 测试 bookkeeping 云函数基础调用
  async testBookkeepingBasic() {
    try {
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:37", "测试 bookkeeping 云函数基础调用...");
      const result = await common_vendor.nr.callFunction({
        name: "bookkeeping",
        data: {
          action: "getCategories",
          data: {}
        }
      });
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:47", "bookkeeping 基础测试结果:", result);
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : "调用失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-debug.js:55", "bookkeeping 基础测试失败:", error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },
  // 测试数据库初始化
  async testDatabaseInit() {
    try {
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:67", "测试数据库初始化...");
      const result = await common_vendor.nr.callFunction({
        name: "init-db",
        data: {}
      });
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:74", "数据库初始化测试结果:", result);
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : "初始化失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-debug.js:82", "数据库初始化测试失败:", error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },
  // 测试添加记录（简化版）
  async testAddRecordSimple() {
    try {
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:94", "测试添加记录（简化版）...");
      const testRecord = {
        type: "expense",
        amount: 1,
        categoryId: "exp_food",
        categoryName: "餐饮",
        categoryIcon: "🍽️",
        categoryColor: "#FF6B6B",
        note: "调试测试",
        date: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
        time: "12:00"
      };
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:108", "测试记录数据:", testRecord);
      const result = await common_vendor.nr.callFunction({
        name: "bookkeeping",
        data: {
          action: "addRecord",
          data: testRecord
        }
      });
      common_vendor.index.__f__("log", "at utils/cloud-debug.js:118", "添加记录测试结果:", result);
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : "添加失败"
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/cloud-debug.js:126", "添加记录测试失败:", error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },
  // 完整的诊断流程
  async runFullDiagnosis() {
    const results = {
      basicConnection: null,
      bookkeepingBasic: null,
      databaseInit: null,
      addRecord: null
    };
    common_vendor.index.__f__("log", "at utils/cloud-debug.js:144", "开始完整诊断...");
    results.basicConnection = await this.testBasicConnection();
    results.bookkeepingBasic = await this.testBookkeepingBasic();
    results.databaseInit = await this.testDatabaseInit();
    results.addRecord = await this.testAddRecordSimple();
    common_vendor.index.__f__("log", "at utils/cloud-debug.js:158", "完整诊断结果:", results);
    return results;
  },
  // 生成诊断报告
  generateReport(results) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
    let report = "=== 云函数诊断报告 ===\n\n";
    report += `1. 基础连接: ${((_a = results.basicConnection) == null ? void 0 : _a.success) ? "✅ 正常" : "❌ 失败"}
`;
    if (!((_b = results.basicConnection) == null ? void 0 : _b.success)) {
      report += `   错误: ${(_c = results.basicConnection) == null ? void 0 : _c.error}
`;
    }
    report += `2. bookkeeping 云函数: ${((_d = results.bookkeepingBasic) == null ? void 0 : _d.success) ? "✅ 正常" : "❌ 失败"}
`;
    if (!((_e = results.bookkeepingBasic) == null ? void 0 : _e.success)) {
      report += `   错误: ${(_f = results.bookkeepingBasic) == null ? void 0 : _f.error}
`;
    }
    report += `3. 数据库初始化: ${((_g = results.databaseInit) == null ? void 0 : _g.success) ? "✅ 正常" : "❌ 失败"}
`;
    if (!((_h = results.databaseInit) == null ? void 0 : _h.success)) {
      report += `   错误: ${(_i = results.databaseInit) == null ? void 0 : _i.error}
`;
    }
    report += `4. 添加记录: ${((_j = results.addRecord) == null ? void 0 : _j.success) ? "✅ 正常" : "❌ 失败"}
`;
    if (!((_k = results.addRecord) == null ? void 0 : _k.success)) {
      report += `   错误: ${(_l = results.addRecord) == null ? void 0 : _l.error}
`;
    }
    report += "\n=== 建议操作 ===\n";
    if (!((_m = results.basicConnection) == null ? void 0 : _m.success)) {
      report += "- 检查 uniCloud 空间配置\n";
      report += "- 确保 test 云函数已部署\n";
    }
    if (!((_n = results.bookkeepingBasic) == null ? void 0 : _n.success)) {
      report += "- 重新部署 bookkeeping 云函数\n";
      report += "- 检查云函数代码是否有语法错误\n";
    }
    if (!((_o = results.databaseInit) == null ? void 0 : _o.success)) {
      report += "- 部署 init-db 云函数\n";
      report += "- 检查数据库权限配置\n";
    }
    if (!((_p = results.addRecord) == null ? void 0 : _p.success)) {
      report += "- 确保数据库已初始化\n";
      report += "- 检查 generateId 函数是否存在\n";
    }
    return report;
  }
};
exports.CloudDebug = CloudDebug;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/cloud-debug.js.map
