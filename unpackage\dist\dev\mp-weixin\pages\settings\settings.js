"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "SettingsPage",
  setup() {
    const settings = common_vendor.computed(() => store_index.store.state.settings);
    const expenseCategories = common_vendor.computed(() => store_index.store.state.categories.expense);
    const incomeCategories = common_vendor.computed(() => store_index.store.state.categories.income);
    const totalRecords = common_vendor.computed(() => store_index.store.state.records.length);
    const totalDays = common_vendor.computed(() => {
      const dates = [...new Set(store_index.store.state.records.map((record) => record.date))];
      return dates.length;
    });
    const totalAmount = common_vendor.computed(
      () => store_index.store.state.records.reduce((total, record) => total + record.amount, 0)
    );
    const showCurrencyPicker = () => {
      const currencies = ["¥", "$", "€", "£", "₹", "₩"];
      common_vendor.index.showActionSheet({
        itemList: currencies,
        success: (res) => {
          const newCurrency = currencies[res.tapIndex];
          store_index.store.actions.updateSettings({ currency: newCurrency });
        }
      });
    };
    const showBudgetSetting = () => {
      common_vendor.index.showModal({
        title: "设置月度预算",
        editable: true,
        placeholderText: "请输入预算金额",
        success: (res) => {
          if (res.confirm && res.content) {
            const budget = parseFloat(res.content) || 0;
            store_index.store.actions.updateSettings({ monthlyBudget: budget });
          }
        }
      });
    };
    const onBudgetAlertChange = (e) => {
      store_index.store.actions.updateSettings({ budgetAlert: e.detail.value });
    };
    const manageCategoriesExpense = () => {
      utils_helpers.showToast("分类管理功能开发中");
    };
    const manageCategoriesIncome = () => {
      utils_helpers.showToast("分类管理功能开发中");
    };
    const exportData = () => {
      try {
        const data = {
          records: store_index.store.state.records,
          categories: store_index.store.state.categories,
          settings: store_index.store.state.settings,
          exportTime: (/* @__PURE__ */ new Date()).toISOString()
        };
        common_vendor.index.__f__("log", "at pages/settings/settings.vue:233", "导出数据:", data);
        utils_helpers.showToast("数据导出成功", "success");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/settings/settings.vue:236", "导出失败:", error);
        utils_helpers.showToast("数据导出失败");
      }
    };
    const importData = () => {
      utils_helpers.showToast("数据导入功能开发中");
    };
    const clearData = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "此操作将清空所有记账数据，且无法恢复，确定要继续吗？",
        confirmColor: "#F44336",
        success: (res) => {
          if (res.confirm) {
            try {
              store_index.store.state.records = [];
              store_index.store.actions.saveRecords();
              store_index.store.state.categories = store_index.store.actions.getDefaultCategories();
              store_index.store.actions.saveCategories();
              utils_helpers.showToast("数据清空成功", "success");
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/settings/settings.vue:263", "清空失败:", error);
              utils_helpers.showToast("数据清空失败");
            }
          }
        }
      });
    };
    const showAbout = () => {
      common_vendor.index.showModal({
        title: "关于记账本",
        content: "记账本 v1.0.0\n\n一款简洁易用的个人记账小程序\n\n功能特色：\n• 快速记账\n• 分类管理\n• 统计分析\n• 数据导出",
        showCancel: false,
        confirmText: "知道了"
      });
    };
    const showHelp = () => {
      common_vendor.index.showModal({
        title: "使用帮助",
        content: "1. 点击首页的快速记账按钮开始记账\n2. 在记账页面选择分类和输入金额\n3. 在账单页面查看所有记录\n4. 在统计页面查看收支分析\n5. 在设置页面管理分类和导出数据",
        showCancel: false,
        confirmText: "知道了"
      });
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadSettings();
      store_index.store.actions.loadCategories();
      store_index.store.actions.loadRecords();
    });
    return {
      settings,
      expenseCategories,
      incomeCategories,
      totalRecords,
      totalDays,
      totalAmount,
      formatAmount: utils_helpers.formatAmount,
      showCurrencyPicker,
      showBudgetSetting,
      onBudgetAlertChange,
      manageCategoriesExpense,
      manageCategoriesIncome,
      exportData,
      importData,
      clearData,
      showAbout,
      showHelp
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($setup.totalRecords),
    b: common_vendor.t($setup.totalDays),
    c: common_vendor.t($setup.formatAmount($setup.totalAmount)),
    d: common_vendor.t($setup.settings.currency),
    e: common_vendor.o((...args) => $setup.showCurrencyPicker && $setup.showCurrencyPicker(...args)),
    f: common_vendor.t($setup.formatAmount($setup.settings.monthlyBudget)),
    g: common_vendor.o((...args) => $setup.showBudgetSetting && $setup.showBudgetSetting(...args)),
    h: $setup.settings.budgetAlert,
    i: common_vendor.o((...args) => $setup.onBudgetAlertChange && $setup.onBudgetAlertChange(...args)),
    j: common_vendor.t($setup.expenseCategories.length),
    k: common_vendor.o((...args) => $setup.manageCategoriesExpense && $setup.manageCategoriesExpense(...args)),
    l: common_vendor.t($setup.incomeCategories.length),
    m: common_vendor.o((...args) => $setup.manageCategoriesIncome && $setup.manageCategoriesIncome(...args)),
    n: common_vendor.o((...args) => $setup.exportData && $setup.exportData(...args)),
    o: common_vendor.o((...args) => $setup.importData && $setup.importData(...args)),
    p: common_vendor.o((...args) => $setup.clearData && $setup.clearData(...args)),
    q: common_vendor.o((...args) => $setup.showAbout && $setup.showAbout(...args)),
    r: common_vendor.o((...args) => $setup.showHelp && $setup.showHelp(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7fad0a1c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/settings/settings.js.map
