{"version": 3, "file": "cloud-storage.js", "sources": ["utils/cloud-storage.js"], "sourcesContent": ["/**\n * 云端数据存储工具类\n * 封装uniCloud数据库操作\n */\n\nclass CloudStorage {\n  constructor() {\n    this.isLoggedIn = false;\n    this.userId = null;\n    this.init();\n  }\n\n  // 初始化\n  async init() {\n    try {\n      // 检查登录状态\n      const loginRes = await uniCloud.getCurrentUserInfo();\n      if (loginRes.uid) {\n        this.isLoggedIn = true;\n        this.userId = loginRes.uid;\n      }\n    } catch (error) {\n      console.log('用户未登录，使用游客模式');\n      this.isLoggedIn = false;\n    }\n  }\n\n  // 用户登录\n  async login() {\n    try {\n      const res = await uniCloud.callFunction({\n        name: 'uni-id-cf',\n        data: {\n          action: 'loginAnonymously'\n        }\n      });\n      \n      if (res.result.code === 0) {\n        this.isLoggedIn = true;\n        this.userId = res.result.uid;\n        \n        // 保存token\n        uni.setStorageSync('uni_id_token', res.result.token);\n        uni.setStorageSync('uni_id_token_expired', res.result.tokenExpired);\n        \n        return {\n          success: true,\n          data: res.result\n        };\n      } else {\n        throw new Error(res.result.msg || '登录失败');\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // 调用云函数\n  async callFunction(action, data = {}) {\n    try {\n      // 如果未登录，先登录\n      if (!this.isLoggedIn) {\n        const loginResult = await this.login();\n        if (!loginResult.success) {\n          throw new Error('登录失败');\n        }\n      }\n\n      const res = await uniCloud.callFunction({\n        name: 'bookkeeping',\n        data: {\n          action,\n          data\n        }\n      });\n\n      if (res.result.code === 200) {\n        return {\n          success: true,\n          data: res.result.data\n        };\n      } else {\n        throw new Error(res.result.message || '操作失败');\n      }\n    } catch (error) {\n      console.error(`云函数调用失败 [${action}]:`, error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // 记录相关操作\n  async addRecord(record) {\n    return await this.callFunction('addRecord', record);\n  }\n\n  async updateRecord(id, record) {\n    return await this.callFunction('updateRecord', { id, ...record });\n  }\n\n  async deleteRecord(id) {\n    return await this.callFunction('deleteRecord', { id });\n  }\n\n  async getRecords(params = {}) {\n    return await this.callFunction('getRecords', params);\n  }\n\n  async getRecordById(id) {\n    return await this.callFunction('getRecordById', { id });\n  }\n\n  // 分类相关操作\n  async getCategories() {\n    return await this.callFunction('getCategories');\n  }\n\n  async addCategory(category) {\n    return await this.callFunction('addCategory', category);\n  }\n\n  async updateCategory(id, category) {\n    return await this.callFunction('updateCategory', { id, ...category });\n  }\n\n  async deleteCategory(id) {\n    return await this.callFunction('deleteCategory', { id });\n  }\n\n  // 统计相关操作\n  async getStatistics(params = {}) {\n    return await this.callFunction('getStatistics', params);\n  }\n\n  async getMonthlyStats(year, month) {\n    return await this.callFunction('getMonthlyStats', { year, month });\n  }\n\n  async getCategoryStats(params = {}) {\n    return await this.callFunction('getCategoryStats', params);\n  }\n\n  // 用户设置相关操作\n  async getUserSettings() {\n    return await this.callFunction('getUserSettings');\n  }\n\n  async updateUserSettings(settings) {\n    return await this.callFunction('updateUserSettings', settings);\n  }\n\n  // 数据同步相关操作\n  async syncData(lastSyncTime) {\n    return await this.callFunction('syncData', { lastSyncTime });\n  }\n\n  async exportData() {\n    return await this.callFunction('exportData');\n  }\n\n  async importData(data) {\n    return await this.callFunction('importData', data);\n  }\n\n  // 初始化数据库\n  async initDatabase() {\n    try {\n      const res = await uniCloud.callFunction({\n        name: 'init-db'\n      });\n      \n      return {\n        success: res.result.code === 200,\n        message: res.result.message\n      };\n    } catch (error) {\n      console.error('数据库初始化失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // 获取默认分类（从云端）\n  async getDefaultCategories() {\n    const result = await this.getCategories();\n    if (result.success) {\n      const categories = result.data;\n      const defaultCategories = {\n        expense: categories.filter(cat => cat.type === 'expense' && cat.isDefault),\n        income: categories.filter(cat => cat.type === 'income' && cat.isDefault)\n      };\n      return defaultCategories;\n    }\n    return { expense: [], income: [] };\n  }\n\n  // 获取默认设置\n  getDefaultSettings() {\n    return {\n      currency: '¥',\n      monthlyBudget: 0,\n      budgetAlert: true,\n      theme: 'light',\n      language: 'zh-CN'\n    };\n  }\n\n  // 离线数据处理\n  async handleOfflineData() {\n    try {\n      // 获取离线存储的数据\n      const offlineRecords = uni.getStorageSync('offline_records') || [];\n      const offlineCategories = uni.getStorageSync('offline_categories') || [];\n      \n      if (offlineRecords.length === 0 && offlineCategories.length === 0) {\n        return { success: true, message: '没有离线数据需要同步' };\n      }\n\n      // 同步离线记录\n      for (const record of offlineRecords) {\n        if (record._action === 'add') {\n          await this.addRecord(record);\n        } else if (record._action === 'update') {\n          await this.updateRecord(record._id, record);\n        } else if (record._action === 'delete') {\n          await this.deleteRecord(record._id);\n        }\n      }\n\n      // 同步离线分类\n      for (const category of offlineCategories) {\n        if (category._action === 'add') {\n          await this.addCategory(category);\n        } else if (category._action === 'update') {\n          await this.updateCategory(category._id, category);\n        } else if (category._action === 'delete') {\n          await this.deleteCategory(category._id);\n        }\n      }\n\n      // 清除离线数据\n      uni.removeStorageSync('offline_records');\n      uni.removeStorageSync('offline_categories');\n\n      return { \n        success: true, \n        message: `同步完成：${offlineRecords.length}条记录，${offlineCategories.length}个分类` \n      };\n    } catch (error) {\n      console.error('离线数据同步失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  // 保存离线数据\n  saveOfflineRecord(record, action = 'add') {\n    try {\n      const offlineRecords = uni.getStorageSync('offline_records') || [];\n      offlineRecords.push({\n        ...record,\n        _action: action,\n        _timestamp: Date.now()\n      });\n      uni.setStorageSync('offline_records', offlineRecords);\n    } catch (error) {\n      console.error('保存离线记录失败:', error);\n    }\n  }\n\n  saveOfflineCategory(category, action = 'add') {\n    try {\n      const offlineCategories = uni.getStorageSync('offline_categories') || [];\n      offlineCategories.push({\n        ...category,\n        _action: action,\n        _timestamp: Date.now()\n      });\n      uni.setStorageSync('offline_categories', offlineCategories);\n    } catch (error) {\n      console.error('保存离线分类失败:', error);\n    }\n  }\n\n  // 检查网络状态\n  isOnline() {\n    return new Promise((resolve) => {\n      uni.getNetworkType({\n        success: (res) => {\n          resolve(res.networkType !== 'none');\n        },\n        fail: () => {\n          resolve(false);\n        }\n      });\n    });\n  }\n}\n\n// 创建单例实例\nconst cloudStorage = new CloudStorage();\n\nexport default cloudStorage;\n"], "names": ["uniCloud", "uni"], "mappings": ";;AAKA,MAAM,aAAa;AAAA,EACjB,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,KAAI;AAAA,EACV;AAAA;AAAA,EAGD,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,WAAW,MAAMA,iBAAS;AAChC,UAAI,SAAS,KAAK;AAChB,aAAK,aAAa;AAClB,aAAK,SAAS,SAAS;AAAA,MACxB;AAAA,IACF,SAAQ,OAAO;AACdC,oBAAAA,MAAY,MAAA,OAAA,gCAAA,cAAc;AAC1B,WAAK,aAAa;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,QAAQ;AACZ,QAAI;AACF,YAAM,MAAM,MAAMD,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,QAAQ;AAAA,QACT;AAAA,MACT,CAAO;AAED,UAAI,IAAI,OAAO,SAAS,GAAG;AACzB,aAAK,aAAa;AAClB,aAAK,SAAS,IAAI,OAAO;AAGzBC,sBAAG,MAAC,eAAe,gBAAgB,IAAI,OAAO,KAAK;AACnDA,sBAAG,MAAC,eAAe,wBAAwB,IAAI,OAAO,YAAY;AAElE,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,IAAI;AAAA,QACpB;AAAA,MACA,OAAa;AACL,cAAM,IAAI,MAAM,IAAI,OAAO,OAAO,MAAM;AAAA,MACzC;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,gCAAA,SAAS,KAAK;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,QAAQ,OAAO,IAAI;AACpC,QAAI;AAEF,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,cAAc,MAAM,KAAK;AAC/B,YAAI,CAAC,YAAY,SAAS;AACxB,gBAAM,IAAI,MAAM,MAAM;AAAA,QACvB;AAAA,MACF;AAED,YAAM,MAAM,MAAMD,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,QACN,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACD;AAAA,MACT,CAAO;AAED,UAAI,IAAI,OAAO,SAAS,KAAK;AAC3B,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,IAAI,OAAO;AAAA,QAC3B;AAAA,MACA,OAAa;AACL,cAAM,IAAI,MAAM,IAAI,OAAO,WAAW,MAAM;AAAA,MAC7C;AAAA,IACF,SAAQ,OAAO;AACdC,yEAAc,YAAY,MAAM,MAAM,KAAK;AAC3C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,UAAU,QAAQ;AACtB,WAAO,MAAM,KAAK,aAAa,aAAa,MAAM;AAAA,EACnD;AAAA,EAED,MAAM,aAAa,IAAI,QAAQ;AAC7B,WAAO,MAAM,KAAK,aAAa,gBAAgB,EAAE,IAAI,GAAG,OAAM,CAAE;AAAA,EACjE;AAAA,EAED,MAAM,aAAa,IAAI;AACrB,WAAO,MAAM,KAAK,aAAa,gBAAgB,EAAE,GAAI,CAAA;AAAA,EACtD;AAAA,EAED,MAAM,WAAW,SAAS,IAAI;AAC5B,WAAO,MAAM,KAAK,aAAa,cAAc,MAAM;AAAA,EACpD;AAAA,EAED,MAAM,cAAc,IAAI;AACtB,WAAO,MAAM,KAAK,aAAa,iBAAiB,EAAE,GAAI,CAAA;AAAA,EACvD;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,WAAO,MAAM,KAAK,aAAa,eAAe;AAAA,EAC/C;AAAA,EAED,MAAM,YAAY,UAAU;AAC1B,WAAO,MAAM,KAAK,aAAa,eAAe,QAAQ;AAAA,EACvD;AAAA,EAED,MAAM,eAAe,IAAI,UAAU;AACjC,WAAO,MAAM,KAAK,aAAa,kBAAkB,EAAE,IAAI,GAAG,SAAQ,CAAE;AAAA,EACrE;AAAA,EAED,MAAM,eAAe,IAAI;AACvB,WAAO,MAAM,KAAK,aAAa,kBAAkB,EAAE,GAAI,CAAA;AAAA,EACxD;AAAA;AAAA,EAGD,MAAM,cAAc,SAAS,IAAI;AAC/B,WAAO,MAAM,KAAK,aAAa,iBAAiB,MAAM;AAAA,EACvD;AAAA,EAED,MAAM,gBAAgB,MAAM,OAAO;AACjC,WAAO,MAAM,KAAK,aAAa,mBAAmB,EAAE,MAAM,MAAK,CAAE;AAAA,EAClE;AAAA,EAED,MAAM,iBAAiB,SAAS,IAAI;AAClC,WAAO,MAAM,KAAK,aAAa,oBAAoB,MAAM;AAAA,EAC1D;AAAA;AAAA,EAGD,MAAM,kBAAkB;AACtB,WAAO,MAAM,KAAK,aAAa,iBAAiB;AAAA,EACjD;AAAA,EAED,MAAM,mBAAmB,UAAU;AACjC,WAAO,MAAM,KAAK,aAAa,sBAAsB,QAAQ;AAAA,EAC9D;AAAA;AAAA,EAGD,MAAM,SAAS,cAAc;AAC3B,WAAO,MAAM,KAAK,aAAa,YAAY,EAAE,aAAc,CAAA;AAAA,EAC5D;AAAA,EAED,MAAM,aAAa;AACjB,WAAO,MAAM,KAAK,aAAa,YAAY;AAAA,EAC5C;AAAA,EAED,MAAM,WAAW,MAAM;AACrB,WAAO,MAAM,KAAK,aAAa,cAAc,IAAI;AAAA,EAClD;AAAA;AAAA,EAGD,MAAM,eAAe;AACnB,QAAI;AACF,YAAM,MAAM,MAAMD,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,MACd,CAAO;AAED,aAAO;AAAA,QACL,SAAS,IAAI,OAAO,SAAS;AAAA,QAC7B,SAAS,IAAI,OAAO;AAAA,MAC5B;AAAA,IACK,SAAQ,OAAO;AACdC,0EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,uBAAuB;AAC3B,UAAM,SAAS,MAAM,KAAK;AAC1B,QAAI,OAAO,SAAS;AAClB,YAAM,aAAa,OAAO;AAC1B,YAAM,oBAAoB;AAAA,QACxB,SAAS,WAAW,OAAO,SAAO,IAAI,SAAS,aAAa,IAAI,SAAS;AAAA,QACzE,QAAQ,WAAW,OAAO,SAAO,IAAI,SAAS,YAAY,IAAI,SAAS;AAAA,MAC/E;AACM,aAAO;AAAA,IACR;AACD,WAAO,EAAE,SAAS,CAAA,GAAI,QAAQ,CAAE,EAAA;AAAA,EACjC;AAAA;AAAA,EAGD,qBAAqB;AACnB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,aAAa;AAAA,MACb,OAAO;AAAA,MACP,UAAU;AAAA,IAChB;AAAA,EACG;AAAA;AAAA,EAGD,MAAM,oBAAoB;AACxB,QAAI;AAEF,YAAM,iBAAiBA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAChE,YAAM,oBAAoBA,cAAG,MAAC,eAAe,oBAAoB,KAAK,CAAA;AAEtE,UAAI,eAAe,WAAW,KAAK,kBAAkB,WAAW,GAAG;AACjE,eAAO,EAAE,SAAS,MAAM,SAAS,aAAY;AAAA,MAC9C;AAGD,iBAAW,UAAU,gBAAgB;AACnC,YAAI,OAAO,YAAY,OAAO;AAC5B,gBAAM,KAAK,UAAU,MAAM;AAAA,QACrC,WAAmB,OAAO,YAAY,UAAU;AACtC,gBAAM,KAAK,aAAa,OAAO,KAAK,MAAM;AAAA,QACpD,WAAmB,OAAO,YAAY,UAAU;AACtC,gBAAM,KAAK,aAAa,OAAO,GAAG;AAAA,QACnC;AAAA,MACF;AAGD,iBAAW,YAAY,mBAAmB;AACxC,YAAI,SAAS,YAAY,OAAO;AAC9B,gBAAM,KAAK,YAAY,QAAQ;AAAA,QACzC,WAAmB,SAAS,YAAY,UAAU;AACxC,gBAAM,KAAK,eAAe,SAAS,KAAK,QAAQ;AAAA,QAC1D,WAAmB,SAAS,YAAY,UAAU;AACxC,gBAAM,KAAK,eAAe,SAAS,GAAG;AAAA,QACvC;AAAA,MACF;AAGDA,0BAAI,kBAAkB,iBAAiB;AACvCA,0BAAI,kBAAkB,oBAAoB;AAE1C,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS,QAAQ,eAAe,MAAM,OAAO,kBAAkB,MAAM;AAAA,MAC7E;AAAA,IACK,SAAQ,OAAO;AACdA,0EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,kBAAkB,QAAQ,SAAS,OAAO;AACxC,QAAI;AACF,YAAM,iBAAiBA,cAAG,MAAC,eAAe,iBAAiB,KAAK,CAAA;AAChE,qBAAe,KAAK;AAAA,QAClB,GAAG;AAAA,QACH,SAAS;AAAA,QACT,YAAY,KAAK,IAAK;AAAA,MAC9B,CAAO;AACDA,oBAAAA,MAAI,eAAe,mBAAmB,cAAc;AAAA,IACrD,SAAQ,OAAO;AACdA,0EAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA,EAED,oBAAoB,UAAU,SAAS,OAAO;AAC5C,QAAI;AACF,YAAM,oBAAoBA,cAAG,MAAC,eAAe,oBAAoB,KAAK,CAAA;AACtE,wBAAkB,KAAK;AAAA,QACrB,GAAG;AAAA,QACH,SAAS;AAAA,QACT,YAAY,KAAK,IAAK;AAAA,MAC9B,CAAO;AACDA,oBAAAA,MAAI,eAAe,sBAAsB,iBAAiB;AAAA,IAC3D,SAAQ,OAAO;AACdA,0EAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AAAA;AAAA,EAGD,WAAW;AACT,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,kBAAQ,IAAI,gBAAgB,MAAM;AAAA,QACnC;AAAA,QACD,MAAM,MAAM;AACV,kBAAQ,KAAK;AAAA,QACd;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAGK,MAAC,eAAe,IAAI,aAAY;;"}