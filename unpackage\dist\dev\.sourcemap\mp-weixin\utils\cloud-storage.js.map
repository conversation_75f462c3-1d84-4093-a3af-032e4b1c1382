{"version": 3, "file": "cloud-storage.js", "sources": ["utils/cloud-storage.js"], "sourcesContent": ["/**\n * 云端数据存储工具类\n * 封装uniCloud数据库操作\n */\n\nconst CloudStorage = {\n  isLoggedIn: false,\n  userId: null,\n\n  // 初始化\n  async init() {\n    try {\n      // 简化登录逻辑，直接使用设备ID\n      await this.login();\n    } catch (error) {\n      console.log('初始化失败，使用游客模式');\n      this.isLoggedIn = false;\n    }\n  },\n\n  // 用户登录（简化版，使用设备ID作为用户标识）\n  async login() {\n    try {\n      // 获取设备信息作为用户标识\n      const deviceInfo = uni.getSystemInfoSync();\n      const deviceId = deviceInfo.deviceId || deviceInfo.system + '_' + Date.now();\n\n      this.isLoggedIn = true;\n      this.userId = deviceId;\n\n      // 保存用户ID\n      uni.setStorageSync('user_id', deviceId);\n\n      return {\n        success: true,\n        data: { userId: deviceId }\n      };\n    } catch (error) {\n      console.error('登录失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n\n  // 调用云函数\n  async callFunction(action, data) {\n    try {\n      // 如果未登录，先登录\n      if (!this.isLoggedIn) {\n        const loginResult = await this.login();\n        if (!loginResult.success) {\n          throw new Error('登录失败');\n        }\n      }\n\n      const res = await uniCloud.callFunction({\n        name: 'bookkeeping',\n        data: {\n          action: action,\n          data: data || {}\n        }\n      });\n\n      if (res.result && res.result.code === 200) {\n        return {\n          success: true,\n          data: res.result.data\n        };\n      } else {\n        throw new Error(res.result ? res.result.message : '操作失败');\n      }\n    } catch (error) {\n      console.error('云函数调用失败 [' + action + ']:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n\n  // 记录相关操作\n  async addRecord(record) {\n    return await this.callFunction('addRecord', record);\n  },\n\n  async getRecords(params) {\n    return await this.callFunction('getRecords', params);\n  },\n\n  // 分类相关操作\n  async getCategories() {\n    return await this.callFunction('getCategories');\n  },\n\n  // 统计相关操作\n  async getStatistics(params) {\n    return await this.callFunction('getStatistics', params);\n  },\n\n  // 初始化数据库\n  async initDatabase() {\n    try {\n      const res = await uniCloud.callFunction({\n        name: 'init-db'\n      });\n\n      return {\n        success: res.result && res.result.code === 200,\n        message: res.result ? res.result.message : '初始化失败'\n      };\n    } catch (error) {\n      console.error('数据库初始化失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n\n  // 测试云函数连接\n  async testConnection() {\n    try {\n      const res = await uniCloud.callFunction({\n        name: 'test',\n        data: { message: 'hello from client' }\n      });\n\n      return {\n        success: res.result && res.result.code === 200,\n        message: res.result ? res.result.message : '连接失败'\n      };\n    } catch (error) {\n      console.error('测试连接失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  },\n\n  // 用户设置相关操作（简化版）\n  async getUserSettings() {\n    return {\n      success: true,\n      data: this.getDefaultSettings()\n    };\n  },\n\n  // 获取默认设置\n  getDefaultSettings() {\n    return {\n      currency: '¥',\n      monthlyBudget: 0,\n      budgetAlert: true,\n      theme: 'light',\n      language: 'zh-CN'\n    };\n  },\n\n  // 检查网络状态\n  async isOnline() {\n    return new Promise((resolve) => {\n      uni.getNetworkType({\n        success: (res) => {\n          resolve(res.networkType !== 'none');\n        },\n        fail: () => {\n          resolve(false);\n        }\n      });\n    });\n  },\n\n  // 离线数据处理（简化版）\n  async handleOfflineData() {\n    return { success: true, message: '没有离线数据需要同步' };\n  },\n\n  // 保存离线数据（简化版）\n  saveOfflineRecord(record, action) {\n    console.log('保存离线记录:', action, record);\n    // 简化实现，实际项目中可以实现完整的离线队列\n  }\n};\n\nexport default CloudStorage;\n"], "names": ["uni", "uniCloud"], "mappings": ";;AAKK,MAAC,eAAe;AAAA,EACnB,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA,EAGR,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,KAAK;IACZ,SAAQ,OAAO;AACdA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,cAAc;AAC1B,WAAK,aAAa;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,QAAQ;AACZ,QAAI;AAEF,YAAM,aAAaA,oBAAI;AACvB,YAAM,WAAW,WAAW,YAAY,WAAW,SAAS,MAAM,KAAK;AAEvE,WAAK,aAAa;AAClB,WAAK,SAAS;AAGdA,oBAAAA,MAAI,eAAe,WAAW,QAAQ;AAEtC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,MAAM,EAAE,QAAQ,SAAU;AAAA,MAClC;AAAA,IACK,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,gCAAA,SAAS,KAAK;AAC5B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,QAAQ,MAAM;AAC/B,QAAI;AAEF,UAAI,CAAC,KAAK,YAAY;AACpB,cAAM,cAAc,MAAM,KAAK;AAC/B,YAAI,CAAC,YAAY,SAAS;AACxB,gBAAM,IAAI,MAAM,MAAM;AAAA,QACvB;AAAA,MACF;AAED,YAAM,MAAM,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,QACN,MAAM;AAAA,UACJ;AAAA,UACA,MAAM,QAAQ,CAAE;AAAA,QACjB;AAAA,MACT,CAAO;AAED,UAAI,IAAI,UAAU,IAAI,OAAO,SAAS,KAAK;AACzC,eAAO;AAAA,UACL,SAAS;AAAA,UACT,MAAM,IAAI,OAAO;AAAA,QAC3B;AAAA,MACA,OAAa;AACL,cAAM,IAAI,MAAM,IAAI,SAAS,IAAI,OAAO,UAAU,MAAM;AAAA,MACzD;AAAA,IACF,SAAQ,OAAO;AACdD,0BAAA,MAAA,SAAA,gCAAc,cAAc,SAAS,MAAM,KAAK;AAChD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,UAAU,QAAQ;AACtB,WAAO,MAAM,KAAK,aAAa,aAAa,MAAM;AAAA,EACnD;AAAA,EAED,MAAM,WAAW,QAAQ;AACvB,WAAO,MAAM,KAAK,aAAa,cAAc,MAAM;AAAA,EACpD;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,WAAO,MAAM,KAAK,aAAa,eAAe;AAAA,EAC/C;AAAA;AAAA,EAGD,MAAM,cAAc,QAAQ;AAC1B,WAAO,MAAM,KAAK,aAAa,iBAAiB,MAAM;AAAA,EACvD;AAAA;AAAA,EAGD,MAAM,eAAe;AACnB,QAAI;AACF,YAAM,MAAM,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,MACd,CAAO;AAED,aAAO;AAAA,QACL,SAAS,IAAI,UAAU,IAAI,OAAO,SAAS;AAAA,QAC3C,SAAS,IAAI,SAAS,IAAI,OAAO,UAAU;AAAA,MACnD;AAAA,IACK,SAAQ,OAAO;AACdD,0EAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,MAAM,MAAMC,cAAQ,GAAC,aAAa;AAAA,QACtC,MAAM;AAAA,QACN,MAAM,EAAE,SAAS,oBAAqB;AAAA,MAC9C,CAAO;AAED,aAAO;AAAA,QACL,SAAS,IAAI,UAAU,IAAI,OAAO,SAAS;AAAA,QAC3C,SAAS,IAAI,SAAS,IAAI,OAAO,UAAU;AAAA,MACnD;AAAA,IACK,SAAQ,OAAO;AACdD,oBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,KAAK;AAC9B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACrB;AAAA,IACK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,kBAAkB;AACtB,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM,KAAK,mBAAoB;AAAA,IACrC;AAAA,EACG;AAAA;AAAA,EAGD,qBAAqB;AACnB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,aAAa;AAAA,MACb,OAAO;AAAA,MACP,UAAU;AAAA,IAChB;AAAA,EACG;AAAA;AAAA,EAGD,MAAM,WAAW;AACf,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9BA,oBAAAA,MAAI,eAAe;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,kBAAQ,IAAI,gBAAgB,MAAM;AAAA,QACnC;AAAA,QACD,MAAM,MAAM;AACV,kBAAQ,KAAK;AAAA,QACd;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,oBAAoB;AACxB,WAAO,EAAE,SAAS,MAAM,SAAS,aAAY;AAAA,EAC9C;AAAA;AAAA,EAGD,kBAAkB,QAAQ,QAAQ;AAChCA,kBAAA,MAAA,MAAA,OAAA,iCAAY,WAAW,QAAQ,MAAM;AAAA,EAEtC;AACH;;"}