/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-98a9e0b2 {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-98a9e0b2 {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-98a9e0b2 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-98a9e0b2 {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-98a9e0b2 {
  justify-content: space-between;
}
.flex-center.data-v-98a9e0b2 {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-98a9e0b2 {
  color: #4CAF50;
}
.text-expense.data-v-98a9e0b2 {
  color: #F44336;
}
.text-bold.data-v-98a9e0b2 {
  font-weight: bold;
}
.text-center.data-v-98a9e0b2 {
  text-align: center;
}
.text-right.data-v-98a9e0b2 {
  text-align: right;
}
.list-page.data-v-98a9e0b2 {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 80px;
}

/* 筛选栏 */
.filter-bar.data-v-98a9e0b2 {
  display: flex;
  background-color: white;
  padding: 12px 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.filter-item.data-v-98a9e0b2 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 6px;
  margin: 0 4px;
  background-color: #f5f5f5;
  transition: background-color 0.3s ease;
}
.filter-item.data-v-98a9e0b2:active {
  background-color: #f1f1f1;
}
.filter-text.data-v-98a9e0b2 {
  font-size: 14px;
  color: #333;
  margin-right: 4px;
}
.filter-arrow.data-v-98a9e0b2 {
  font-size: 10px;
  color: #999;
}

/* 统计卡片 */
.summary-card.data-v-98a9e0b2 {
  display: flex;
  background-color: white;
  padding: 20px;
  margin: 0 16px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.summary-item.data-v-98a9e0b2 {
  flex: 1;
  text-align: center;
}
.summary-label.data-v-98a9e0b2 {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}
.summary-value.data-v-98a9e0b2 {
  font-size: 18px;
  font-weight: 600;
}
.summary-value.income.data-v-98a9e0b2 {
  color: #4CAF50;
}
.summary-value.expense.data-v-98a9e0b2 {
  color: #F44336;
}
.summary-value.negative.data-v-98a9e0b2 {
  color: #F44336;
}

/* 记录列表 */
.record-list.data-v-98a9e0b2 {
  padding: 0 16px;
}
.date-group.data-v-98a9e0b2 {
  margin-bottom: 16px;
}
.date-header.data-v-98a9e0b2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid #e5e5e5;
}
.date-text.data-v-98a9e0b2 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
.date-summary.data-v-98a9e0b2 {
  display: flex;
  gap: 12px;
}
.date-income.data-v-98a9e0b2, .date-expense.data-v-98a9e0b2 {
  font-size: 12px;
}
.date-income.data-v-98a9e0b2 {
  color: #4CAF50;
}
.date-expense.data-v-98a9e0b2 {
  color: #F44336;
}
.record-items.data-v-98a9e0b2 {
  background-color: white;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}
.record-item.data-v-98a9e0b2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.3s ease;
}
.record-item.data-v-98a9e0b2:last-child {
  border-bottom: none;
}
.record-item.data-v-98a9e0b2:active {
  background-color: #f1f1f1;
}
.record-left.data-v-98a9e0b2 {
  display: flex;
  align-items: center;
  flex: 1;
}
.record-icon.data-v-98a9e0b2 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}
.record-info.data-v-98a9e0b2 {
  flex: 1;
}
.record-category.data-v-98a9e0b2 {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.record-note.data-v-98a9e0b2 {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 2px;
}
.record-time.data-v-98a9e0b2 {
  font-size: 12px;
  color: #808080;
}
.record-right.data-v-98a9e0b2 {
  text-align: right;
}
.record-amount.data-v-98a9e0b2 {
  font-size: 16px;
  font-weight: 600;
}
.record-amount.income.data-v-98a9e0b2 {
  color: #4CAF50;
}
.record-amount.expense.data-v-98a9e0b2 {
  color: #F44336;
}

/* 空状态 */
.empty-state.data-v-98a9e0b2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}
.empty-icon.data-v-98a9e0b2 {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}
.empty-text.data-v-98a9e0b2 {
  font-size: 18px;
  color: #999;
  margin-bottom: 8px;
}
.empty-desc.data-v-98a9e0b2 {
  font-size: 14px;
  color: #808080;
}

/* 浮动添加按钮 */
.fab.data-v-98a9e0b2 {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: #4CAF50;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}
.fab.data-v-98a9e0b2:active {
  transform: scale(0.95);
}
.fab-icon.data-v-98a9e0b2 {
  font-size: 24px;
  color: white;
  font-weight: 300;
}

/* 操作菜单 */
.action-menu.data-v-98a9e0b2 {
  background-color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 0;
}
.action-item.data-v-98a9e0b2 {
  padding: 16px 20px;
  text-align: center;
  border-bottom: 1px solid #e5e5e5;
}
.action-item.data-v-98a9e0b2:last-child {
  border-bottom: none;
}
.action-item.delete .action-text.data-v-98a9e0b2 {
  color: #F44336;
}
.action-item.cancel .action-text.data-v-98a9e0b2 {
  color: #999;
}
.action-item.data-v-98a9e0b2:active {
  background-color: #f1f1f1;
}
.action-text.data-v-98a9e0b2 {
  font-size: 16px;
  color: #333;
}