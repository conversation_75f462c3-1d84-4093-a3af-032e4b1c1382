# 记账本小程序

一款基于 uniapp 框架开发的跨平台记账小程序，支持微信小程序和抖音小程序。

## 功能特色

### 📊 核心功能
- **快速记账**: 支持收入和支出记录，分类管理
- **账单管理**: 查看、编辑、删除记账记录
- **统计分析**: 收支趋势、分类统计、图表展示
- **数据管理**: 本地存储、数据导入导出

### 🎨 界面设计
- **现代化UI**: 简洁美观的界面设计
- **动画效果**: 流畅的页面过渡和交互动画
- **响应式布局**: 适配不同屏幕尺寸
- **主题色彩**: 清新的绿色主题

### 🔧 技术特点
- **Vue3语法**: 使用Composition API
- **响应式状态管理**: 基于Vue3 reactive
- **模块化架构**: 清晰的代码结构
- **跨平台兼容**: 支持多个小程序平台

## 项目结构

```
bookkeepingBook/
├── pages/                  # 页面文件
│   ├── index/             # 首页
│   ├── add/               # 记账页面
│   ├── list/              # 账单列表
│   ├── statistics/        # 统计分析
│   └── settings/          # 设置页面
├── utils/                 # 工具函数
│   ├── storage.js         # 本地存储
│   ├── models.js          # 数据模型
│   └── helpers.js         # 辅助函数
├── store/                 # 状态管理
│   └── index.js           # 主store
├── static/                # 静态资源
│   └── tabbar/            # 底部导航图标
├── test/                  # 测试文件
│   └── basic-test.js      # 基础功能测试
├── pages.json             # 页面配置
├── manifest.json          # 应用配置
├── uni.scss              # 全局样式变量
└── App.vue               # 应用入口
```

## 快速开始

### 环境要求
- HBuilderX 3.0+
- Node.js 14+
- 微信开发者工具 / 抖音开发者工具

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd bookkeepingBook
   ```

2. **打开项目**
   - 使用 HBuilderX 打开项目文件夹
   - 或使用其他支持 uniapp 的开发工具

3. **运行项目**
   - 微信小程序：点击运行 -> 运行到小程序模拟器 -> 微信开发者工具
   - 抖音小程序：点击运行 -> 运行到小程序模拟器 -> 抖音开发者工具

### 配置说明

#### 1. 应用信息配置 (manifest.json)
```json
{
  "name": "记账本",
  "appid": "your-app-id",
  "description": "简洁易用的个人记账小程序"
}
```

#### 2. 页面路由配置 (pages.json)
- 已配置5个主要页面的路由
- 底部导航栏配置
- 页面样式和权限设置

#### 3. 全局样式配置 (uni.scss)
- 主题色彩变量
- 常用样式类
- 响应式断点

## 使用指南

### 基础操作

1. **添加记录**
   - 点击首页快速记账按钮
   - 选择收入/支出类型
   - 输入金额和选择分类
   - 添加备注（可选）
   - 保存记录

2. **查看账单**
   - 进入账单页面查看所有记录
   - 使用筛选功能按类型、日期、分类筛选
   - 点击记录可编辑
   - 长按记录可删除

3. **统计分析**
   - 查看收支趋势图表
   - 分类支出占比分析
   - 按时间段统计数据

4. **设置管理**
   - 修改货币符号
   - 设置月度预算
   - 管理收支分类
   - 导出/导入数据

### 高级功能

#### 数据导出
```javascript
// 在设置页面点击导出数据
// 数据格式包含：
{
  records: [],      // 所有记账记录
  categories: {},   // 分类数据
  settings: {},     // 用户设置
  exportTime: ""    // 导出时间
}
```

#### 自定义分类
- 支持添加自定义收入/支出分类
- 可设置分类图标和颜色
- 分类数据本地存储

## 开发指南

### 核心模块

#### 1. 数据模型 (utils/models.js)
```javascript
// 记录模型
const record = new Record({
  type: 'expense',
  amount: 100,
  categoryId: 'food',
  note: '午餐'
})

// 分类模型
const category = new Category({
  name: '餐饮',
  icon: '🍽️',
  color: '#FF9800'
})
```

#### 2. 状态管理 (store/index.js)
```javascript
// 添加记录
store.actions.addRecord(recordData)

// 获取统计数据
const stats = store.getters.statistics.value
```

#### 3. 本地存储 (utils/storage.js)
```javascript
// 存储记录
Storage.setRecords(records)

// 获取记录
const records = Storage.getRecords()
```

### 扩展开发

#### 添加新页面
1. 在 `pages/` 目录创建页面文件夹
2. 在 `pages.json` 中配置路由
3. 实现页面逻辑和样式

#### 添加新功能
1. 在对应的工具模块中添加函数
2. 在状态管理中添加相关状态和操作
3. 在页面中调用新功能

## 测试

### 运行测试
```bash
# 运行基础功能测试
node test/basic-test.js
```

### 测试覆盖
- 数据模型测试
- 工具函数测试
- 存储功能测试
- 统计计算测试

## 部署发布

### 微信小程序
1. 在微信开发者工具中预览和调试
2. 上传代码到微信后台
3. 提交审核和发布

### 抖音小程序
1. 在抖音开发者工具中预览和调试
2. 上传代码到抖音后台
3. 提交审核和发布

## 常见问题

### Q: 数据丢失怎么办？
A: 数据存储在本地，建议定期使用导出功能备份数据。

### Q: 如何添加新的分类？
A: 目前需要在代码中修改默认分类，后续版本将支持用户自定义添加。

### Q: 支持云端同步吗？
A: 当前版本仅支持本地存储，云端同步功能在开发计划中。

## 更新日志

### v1.0.0 (2024-01-15)
- ✨ 初始版本发布
- 📊 基础记账功能
- 📈 统计分析功能
- ⚙️ 设置管理功能
- 🎨 UI界面优化

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [your-email]
- 微信: [your-wechat]
