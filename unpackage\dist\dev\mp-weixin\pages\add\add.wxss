/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-e8d2fd40 {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-e8d2fd40 {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-e8d2fd40 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-e8d2fd40 {
  justify-content: space-between;
}
.flex-center.data-v-e8d2fd40 {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-e8d2fd40 {
  color: #4CAF50;
}
.text-expense.data-v-e8d2fd40 {
  color: #F44336;
}
.text-bold.data-v-e8d2fd40 {
  font-weight: bold;
}
.text-center.data-v-e8d2fd40 {
  text-align: center;
}
.text-right.data-v-e8d2fd40 {
  text-align: right;
}
.add-page.data-v-e8d2fd40 {
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);
  min-height: 100vh;
  padding: 0;
}

/* 顶部标题栏 */
.header-section.data-v-e8d2fd40 {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  padding: 20px 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}
.header-content.data-v-e8d2fd40 {
  flex: 1;
}
.page-title.data-v-e8d2fd40 {
  font-size: 24px;
  font-weight: 700;
  display: block;
  margin-bottom: 4px;
}
.page-subtitle.data-v-e8d2fd40 {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}
.header-actions.data-v-e8d2fd40 {
  display: flex;
  gap: 8px;
}
.action-btn.data-v-e8d2fd40 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.action-btn.data-v-e8d2fd40:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}
.delete-btn.data-v-e8d2fd40 {
  background: rgba(244, 67, 54, 0.3);
}
.action-icon.data-v-e8d2fd40 {
  font-size: 18px;
}

/* 类型切换区域 */
.type-section.data-v-e8d2fd40 {
  padding: 0 16px;
  margin-top: -12px;
  margin-bottom: 24px;
}
.type-tabs.data-v-e8d2fd40 {
  background: white;
  border-radius: 16px;
  padding: 8px;
  display: flex;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.type-tab.data-v-e8d2fd40 {
  flex: 1;
  padding: 16px 12px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.type-tab.data-v-e8d2fd40:active {
  transform: scale(0.98);
}
.type-tab.active.data-v-e8d2fd40 {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}
.type-tab.active .tab-indicator.data-v-e8d2fd40 {
  opacity: 1;
}
.tab-icon.data-v-e8d2fd40 {
  font-size: 20px;
}
.tab-text.data-v-e8d2fd40 {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}
.type-tab.active .tab-text.data-v-e8d2fd40 {
  color: white;
}
.tab-indicator.data-v-e8d2fd40 {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: white;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 金额输入区域 */
.amount-section.data-v-e8d2fd40 {
  padding: 0 16px;
  margin-bottom: 24px;
}
.amount-container.data-v-e8d2fd40 {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.amount-header.data-v-e8d2fd40 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.amount-label.data-v-e8d2fd40 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.quick-amounts.data-v-e8d2fd40 {
  display: flex;
  gap: 8px;
}
.quick-amount.data-v-e8d2fd40 {
  padding: 6px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}
.quick-amount.data-v-e8d2fd40:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.quick-text.data-v-e8d2fd40 {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}
.amount-display.data-v-e8d2fd40 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}
.amount-display.data-v-e8d2fd40:focus-within {
  border-color: #4CAF50;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}
.currency.data-v-e8d2fd40 {
  font-size: 32px;
  color: #666;
  margin-right: 8px;
  font-weight: 600;
}
.amount-input.data-v-e8d2fd40 {
  font-size: 48px;
  font-weight: 300;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
  text-align: left;
  min-width: 200px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
.amount-input.data-v-e8d2fd40::-webkit-input-placeholder {
  color: #ccc;
}
.amount-input.data-v-e8d2fd40::placeholder {
  color: #ccc;
}
.amount-tools.data-v-e8d2fd40 {
  display: flex;
  justify-content: center;
}
.calculator-btn.data-v-e8d2fd40 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
}
.calculator-btn.data-v-e8d2fd40:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.calc-icon.data-v-e8d2fd40 {
  font-size: 16px;
}
.calc-text.data-v-e8d2fd40 {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}

/* 分类选择 */
.category-section.data-v-e8d2fd40 {
  padding: 0 16px;
  margin-bottom: 24px;
}
.section-header.data-v-e8d2fd40 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.header-left.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.section-title.data-v-e8d2fd40 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.section-subtitle.data-v-e8d2fd40 {
  font-size: 12px;
  color: #666;
}
.header-right.data-v-e8d2fd40 {
  display: flex;
  align-items: center;
}
.manage-btn.data-v-e8d2fd40 {
  padding: 8px 16px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}
.manage-btn.data-v-e8d2fd40:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.manage-text.data-v-e8d2fd40 {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}
.category-grid.data-v-e8d2fd40 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  background: white;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.category-item.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeInUp-e8d2fd40 0.6s ease-out both;
}
.category-item.data-v-e8d2fd40:active {
  transform: scale(0.95);
}
.category-item.active.data-v-e8d2fd40 {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
}
.add-category.data-v-e8d2fd40 {
  border: 2px dashed #ddd;
  background: transparent;
}
.add-category.data-v-e8d2fd40:active {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}
.category-icon-wrapper.data-v-e8d2fd40 {
  position: relative;
  margin-bottom: 8px;
}
.category-icon.data-v-e8d2fd40 {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.category-item.active .category-icon.data-v-e8d2fd40 {
  background: rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}
.add-icon.data-v-e8d2fd40 {
  background: #f0f0f0 !important;
  color: #999;
}
.icon-text.data-v-e8d2fd40 {
  font-size: 20px;
  color: white;
}
.add-category .icon-text.data-v-e8d2fd40 {
  color: #999;
  font-size: 24px;
  font-weight: 300;
}
.selection-indicator.data-v-e8d2fd40 {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}
.check-icon.data-v-e8d2fd40 {
  font-size: 12px;
  color: #4CAF50;
  font-weight: bold;
}
.category-name.data-v-e8d2fd40 {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}
.category-item.active .category-name.data-v-e8d2fd40 {
  color: white;
}
.add-category .category-name.data-v-e8d2fd40 {
  color: #999;
}

/* 详细信息区域 */
.details-section.data-v-e8d2fd40 {
  padding: 0 16px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.detail-card.data-v-e8d2fd40 {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.card-header.data-v-e8d2fd40 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.card-title.data-v-e8d2fd40 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.quick-time-btns.data-v-e8d2fd40 {
  display: flex;
  gap: 8px;
}
.quick-btn.data-v-e8d2fd40 {
  padding: 6px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}
.quick-btn.data-v-e8d2fd40:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.quick-text.data-v-e8d2fd40 {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}
.char-count.data-v-e8d2fd40 {
  font-size: 12px;
  color: #999;
}

/* 日期时间卡片 */
.datetime-row.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.datetime-item.data-v-e8d2fd40 {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  transition: all 0.3s ease;
}
.datetime-item.data-v-e8d2fd40:active {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  transform: scale(0.98);
}
.datetime-icon.data-v-e8d2fd40 {
  font-size: 20px;
}
.datetime-content.data-v-e8d2fd40 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.datetime-label.data-v-e8d2fd40 {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}
.datetime-value.data-v-e8d2fd40 {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}
.datetime-arrow.data-v-e8d2fd40 {
  font-size: 16px;
  color: #ccc;
  font-weight: bold;
}
.datetime-divider.data-v-e8d2fd40 {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  margin: 8px 0;
}

/* 备注输入卡片 */
.note-input-wrapper.data-v-e8d2fd40 {
  position: relative;
}
.note-input.data-v-e8d2fd40 {
  width: 100%;
  min-height: 80px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
.note-input.data-v-e8d2fd40:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
  background: white;
}
.note-input.data-v-e8d2fd40::-webkit-input-placeholder {
  color: #999;
}
.note-input.data-v-e8d2fd40::placeholder {
  color: #999;
}
.note-suggestions.data-v-e8d2fd40 {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}
.suggestion-item.data-v-e8d2fd40 {
  padding: 8px 12px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}
.suggestion-item.data-v-e8d2fd40:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.suggestion-text.data-v-e8d2fd40 {
  font-size: 12px;
  color: #4CAF50;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeInUp-e8d2fd40 {
from {
    opacity: 0;
    transform: translateY(30px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes slideInDown-e8d2fd40 {
from {
    opacity: 0;
    transform: translateY(-30px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes bounceIn-e8d2fd40 {
0% {
    opacity: 0;
    transform: scale(0.3);
}
50% {
    opacity: 1;
    transform: scale(1.05);
}
70% {
    transform: scale(0.9);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}
/* 应用动画 */
.header-section.data-v-e8d2fd40 {
  animation: slideInDown-e8d2fd40 0.6s ease-out;
}
.type-section.data-v-e8d2fd40 {
  animation: fadeInUp-e8d2fd40 0.6s ease-out 0.1s both;
}
.amount-section.data-v-e8d2fd40 {
  animation: fadeInUp-e8d2fd40 0.6s ease-out 0.2s both;
}
.category-section.data-v-e8d2fd40 {
  animation: fadeInUp-e8d2fd40 0.6s ease-out 0.3s both;
}
.details-section.data-v-e8d2fd40 {
  animation: fadeInUp-e8d2fd40 0.6s ease-out 0.4s both;
}

/* 响应式设计 */
@media (max-width: 375px) {
.category-grid.data-v-e8d2fd40 {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}
.quick-amounts.data-v-e8d2fd40 {
    flex-wrap: wrap;
    gap: 6px;
}
.quick-amount.data-v-e8d2fd40 {
    padding: 4px 8px;
}
.amount-input.data-v-e8d2fd40 {
    font-size: 36px;
}
}