/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-e8d2fd40 {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-e8d2fd40 {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-e8d2fd40 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-e8d2fd40 {
  justify-content: space-between;
}
.flex-center.data-v-e8d2fd40 {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-e8d2fd40 {
  color: #4CAF50;
}
.text-expense.data-v-e8d2fd40 {
  color: #F44336;
}
.text-bold.data-v-e8d2fd40 {
  font-weight: bold;
}
.text-center.data-v-e8d2fd40 {
  text-align: center;
}
.text-right.data-v-e8d2fd40 {
  text-align: right;
}
.add-page.data-v-e8d2fd40 {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 类型切换 */
.type-tabs.data-v-e8d2fd40 {
  display: flex;
  background-color: white;
  margin-bottom: 16px;
}
.type-tab.data-v-e8d2fd40 {
  flex: 1;
  padding: 16px;
  text-align: center;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}
.type-tab.active.data-v-e8d2fd40 {
  border-bottom-color: #4CAF50;
}
.type-tab.active .tab-text.data-v-e8d2fd40 {
  color: #4CAF50;
  font-weight: 600;
}
.tab-text.data-v-e8d2fd40 {
  font-size: 16px;
  color: #999;
}

/* 金额输入 */
.amount-section.data-v-e8d2fd40 {
  background-color: white;
  padding: 40px 20px;
  margin-bottom: 16px;
  text-align: center;
}
.amount-display.data-v-e8d2fd40 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.currency.data-v-e8d2fd40 {
  font-size: 32px;
  color: #999;
  margin-right: 8px;
}
.amount-input.data-v-e8d2fd40 {
  font-size: 48px;
  font-weight: 300;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
  text-align: left;
  min-width: 200px;
}

/* 分类选择 */
.category-section.data-v-e8d2fd40 {
  background-color: white;
  padding: 20px;
  margin-bottom: 16px;
}
.section-title.data-v-e8d2fd40 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}
.category-grid.data-v-e8d2fd40 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
.category-item.data-v-e8d2fd40 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}
.category-item.active.data-v-e8d2fd40 {
  background-color: rgba(76, 175, 80, 0.1);
}
.category-item.active .category-icon.data-v-e8d2fd40 {
  transform: scale(1.1);
}
.category-item.active .category-name.data-v-e8d2fd40 {
  color: #4CAF50;
  font-weight: 600;
}
.category-item.data-v-e8d2fd40:active {
  transform: scale(0.95);
}
.category-icon.data-v-e8d2fd40 {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 8px;
  transition: transform 0.3s ease;
}
.category-name.data-v-e8d2fd40 {
  font-size: 12px;
  color: #333;
  text-align: center;
}

/* 日期时间 */
.datetime-section.data-v-e8d2fd40 {
  background-color: white;
  margin-bottom: 16px;
}
.datetime-item.data-v-e8d2fd40 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}
.datetime-item.data-v-e8d2fd40:last-child {
  border-bottom: none;
}
.datetime-item.data-v-e8d2fd40:active {
  background-color: #f1f1f1;
}
.datetime-label.data-v-e8d2fd40 {
  font-size: 16px;
  color: #333;
}
.datetime-value.data-v-e8d2fd40 {
  font-size: 16px;
  color: #999;
}

/* 备注输入 */
.note-section.data-v-e8d2fd40 {
  background-color: white;
  padding: 20px;
  margin-bottom: 32px;
}
.note-input.data-v-e8d2fd40 {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
}
.note-input.data-v-e8d2fd40:focus {
  border-color: #4CAF50;
  outline: none;
}

/* 操作按钮 */
.action-buttons.data-v-e8d2fd40 {
  display: flex;
  gap: 16px;
  padding: 0 20px 32px;
}
.action-buttons .btn.data-v-e8d2fd40 {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
}