<view class="list-page data-v-98a9e0b2"><view class="filter-bar data-v-98a9e0b2"><view class="filter-item data-v-98a9e0b2" bindtap="{{b}}"><text class="filter-text data-v-98a9e0b2">{{a}}</text><text class="filter-arrow data-v-98a9e0b2">▼</text></view><view class="filter-item data-v-98a9e0b2" bindtap="{{d}}"><text class="filter-text data-v-98a9e0b2">{{c}}</text><text class="filter-arrow data-v-98a9e0b2">▼</text></view><view class="filter-item data-v-98a9e0b2" bindtap="{{f}}"><text class="filter-text data-v-98a9e0b2">{{e}}</text><text class="filter-arrow data-v-98a9e0b2">▼</text></view></view><view wx:if="{{g}}" class="summary-card data-v-98a9e0b2"><view class="summary-item data-v-98a9e0b2"><text class="summary-label data-v-98a9e0b2">收入</text><text class="summary-value income data-v-98a9e0b2">{{h}}</text></view><view class="summary-item data-v-98a9e0b2"><text class="summary-label data-v-98a9e0b2">支出</text><text class="summary-value expense data-v-98a9e0b2">{{i}}</text></view><view class="summary-item data-v-98a9e0b2"><text class="summary-label data-v-98a9e0b2">结余</text><text class="{{['summary-value', 'data-v-98a9e0b2', k && 'negative']}}">{{j}}</text></view></view><view wx:if="{{l}}" class="record-list data-v-98a9e0b2"><view wx:for="{{m}}" wx:for-item="group" wx:key="g" class="date-group data-v-98a9e0b2"><view class="date-header data-v-98a9e0b2"><text class="date-text data-v-98a9e0b2">{{group.a}}</text><view class="date-summary data-v-98a9e0b2"><text wx:if="{{group.b}}" class="date-income data-v-98a9e0b2"> 收入 {{group.c}}</text><text wx:if="{{group.d}}" class="date-expense data-v-98a9e0b2"> 支出 {{group.e}}</text></view></view><view class="record-items data-v-98a9e0b2"><view wx:for="{{group.f}}" wx:for-item="record" wx:key="j" class="record-item data-v-98a9e0b2" bindtap="{{record.k}}" bindlongpress="{{record.l}}"><view class="record-left data-v-98a9e0b2"><view class="record-icon data-v-98a9e0b2" style="{{'background-color:' + record.b}}">{{record.a}}</view><view class="record-info data-v-98a9e0b2"><text class="record-category data-v-98a9e0b2">{{record.c}}</text><text wx:if="{{record.d}}" class="record-note data-v-98a9e0b2">{{record.e}}</text><text class="record-time data-v-98a9e0b2">{{record.f}}</text></view></view><view class="record-right data-v-98a9e0b2"><text class="{{['record-amount', 'data-v-98a9e0b2', record.i]}}">{{record.g}}{{record.h}}</text></view></view></view></view></view><view wx:else class="empty-state data-v-98a9e0b2"><view class="empty-icon data-v-98a9e0b2">📝</view><text class="empty-text data-v-98a9e0b2">暂无记录</text><text class="empty-desc data-v-98a9e0b2">点击右下角按钮开始记账</text></view><view class="fab data-v-98a9e0b2" bindtap="{{n}}"><text class="fab-icon data-v-98a9e0b2">+</text></view><uni-popup wx:if="{{s}}" class="r data-v-98a9e0b2" u-s="{{['d']}}" u-r="actionPopup" u-i="98a9e0b2-0" bind:__l="__l" u-p="{{s}}"><view class="action-menu data-v-98a9e0b2"><view class="action-item data-v-98a9e0b2" bindtap="{{o}}"><text class="action-text data-v-98a9e0b2">编辑</text></view><view class="action-item delete data-v-98a9e0b2" bindtap="{{p}}"><text class="action-text data-v-98a9e0b2">删除</text></view><view class="action-item cancel data-v-98a9e0b2" bindtap="{{q}}"><text class="action-text data-v-98a9e0b2">取消</text></view></view></uni-popup></view>