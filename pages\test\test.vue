<template>
  <view class="container">
    <view class="header">
      <text class="title">启动测试页面</text>
    </view>
    
    <view class="content">
      <view class="test-item">
        <text class="label">基础功能测试:</text>
        <text class="status" :class="{ success: basicTest }">{{ basicTest ? '✅ 正常' : '❌ 异常' }}</text>
      </view>
      
      <view class="test-item">
        <text class="label">云函数连接:</text>
        <text class="status" :class="{ success: cloudTest }">{{ cloudTestStatus }}</text>
      </view>
      
      <view class="test-item">
        <text class="label">存储功能:</text>
        <text class="status" :class="{ success: storageTest }">{{ storageTest ? '✅ 正常' : '❌ 异常' }}</text>
      </view>
    </view>
    
    <view class="actions">
      <button @click="runTests" class="test-btn">运行测试</button>
      <button @click="goHome" class="home-btn">返回首页</button>
    </view>
    
    <view class="logs">
      <text class="log-title">测试日志:</text>
      <view v-for="(log, index) in logs" :key="index" class="log-item">
        <text>{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      basicTest: false,
      cloudTest: false,
      storageTest: false,
      cloudTestStatus: '未测试',
      logs: []
    }
  },
  
  onLoad() {
    this.addLog('测试页面加载完成');
    this.runBasicTests();
  },
  
  methods: {
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.push(`[${timestamp}] ${message}`);
    },
    
    runBasicTests() {
      try {
        // 测试基础JavaScript功能
        const testObj = { test: true };
        const testArray = [1, 2, 3];
        const testString = `测试字符串 ${Date.now()}`;
        
        this.basicTest = true;
        this.addLog('✅ 基础功能测试通过');
        
        // 测试存储功能
        uni.setStorageSync('test_key', 'test_value');
        const stored = uni.getStorageSync('test_key');
        this.storageTest = stored === 'test_value';
        this.addLog(this.storageTest ? '✅ 存储功能测试通过' : '❌ 存储功能测试失败');
        
      } catch (error) {
        this.addLog(`❌ 基础测试失败: ${error.message}`);
      }
    },
    
    async runTests() {
      this.addLog('开始运行完整测试...');
      
      // 测试云函数连接
      try {
        this.cloudTestStatus = '测试中...';
        this.addLog('正在测试云函数连接...');
        
        const result = await uniCloud.callFunction({
          name: 'test',
          data: { message: 'hello from test page' }
        });
        
        if (result.result && result.result.code === 200) {
          this.cloudTest = true;
          this.cloudTestStatus = '✅ 连接正常';
          this.addLog('✅ 云函数连接测试通过');
        } else {
          this.cloudTestStatus = '❌ 连接失败';
          this.addLog('❌ 云函数返回异常');
        }
      } catch (error) {
        this.cloudTest = false;
        this.cloudTestStatus = '❌ 连接失败';
        this.addLog(`❌ 云函数连接失败: ${error.message}`);
      }
      
      this.addLog('测试完成');
    },
    
    goHome() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.content {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.test-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 16px;
  color: #333;
}

.status {
  font-size: 14px;
  color: #999;
}

.status.success {
  color: #4CAF50;
}

.actions {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.test-btn, .home-btn {
  flex: 1;
  padding: 15px;
  border-radius: 8px;
  font-size: 16px;
}

.test-btn {
  background-color: #4CAF50;
  color: white;
}

.home-btn {
  background-color: #2196F3;
  color: white;
}

.logs {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.log-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  display: block;
}

.log-item {
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  color: #666;
}

.log-item:last-child {
  border-bottom: none;
}
</style>
