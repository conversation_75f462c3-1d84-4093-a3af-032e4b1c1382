"use strict";
const common_vendor = require("../../common/vendor.js");
const store_index = require("../../store/index.js");
const utils_helpers = require("../../utils/helpers.js");
const _sfc_main = {
  name: "ListPage",
  setup() {
    const typeFilter = common_vendor.ref("all");
    const dateFilter = common_vendor.ref("all");
    const categoryFilter = common_vendor.ref("all");
    const selectedRecord = common_vendor.ref(null);
    const filteredRecords = common_vendor.computed(() => {
      let records = [...store_index.store.state.records];
      if (typeFilter.value !== "all") {
        records = records.filter((record) => record.type === typeFilter.value);
      }
      if (dateFilter.value !== "all") {
        const today = utils_helpers.formatDate(/* @__PURE__ */ new Date());
        const now = /* @__PURE__ */ new Date();
        switch (dateFilter.value) {
          case "today":
            records = records.filter((record) => record.date === today);
            break;
          case "week":
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
            const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
            records = records.filter(
              (record) => record.date >= utils_helpers.formatDate(weekStart) && record.date <= utils_helpers.formatDate(weekEnd)
            );
            break;
          case "month":
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            records = records.filter(
              (record) => record.date >= utils_helpers.formatDate(monthStart) && record.date <= utils_helpers.formatDate(monthEnd)
            );
            break;
        }
      }
      if (categoryFilter.value !== "all") {
        records = records.filter((record) => record.categoryId === categoryFilter.value);
      }
      return records.sort((a, b) => {
        const dateTimeA = /* @__PURE__ */ new Date(a.date + " " + a.time);
        const dateTimeB = /* @__PURE__ */ new Date(b.date + " " + b.time);
        return dateTimeB - dateTimeA;
      });
    });
    const groupedRecords = common_vendor.computed(() => {
      const groups = {};
      filteredRecords.value.forEach((record) => {
        if (!groups[record.date]) {
          groups[record.date] = {
            date: record.date,
            records: [],
            income: 0,
            expense: 0
          };
        }
        groups[record.date].records.push(record);
        if (record.type === "income") {
          groups[record.date].income += record.amount;
        } else {
          groups[record.date].expense += record.amount;
        }
      });
      return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date));
    });
    const summaryIncome = common_vendor.computed(
      () => filteredRecords.value.filter((record) => record.type === "income").reduce((total, record) => total + record.amount, 0)
    );
    const summaryExpense = common_vendor.computed(
      () => filteredRecords.value.filter((record) => record.type === "expense").reduce((total, record) => total + record.amount, 0)
    );
    const summaryBalance = common_vendor.computed(() => summaryIncome.value - summaryExpense.value);
    const typeFilterText = common_vendor.computed(() => {
      switch (typeFilter.value) {
        case "income":
          return "收入";
        case "expense":
          return "支出";
        default:
          return "全部";
      }
    });
    const dateFilterText = common_vendor.computed(() => {
      switch (dateFilter.value) {
        case "today":
          return "今天";
        case "week":
          return "本周";
        case "month":
          return "本月";
        default:
          return "全部";
      }
    });
    const categoryFilterText = common_vendor.computed(() => {
      if (categoryFilter.value === "all") {
        return "全部分类";
      }
      const category = store_index.store.actions.getCategoryById(categoryFilter.value, typeFilter.value);
      return category ? category.name : "全部分类";
    });
    const formatDateHeader = (date) => {
      return utils_helpers.getRelativeDateText(date);
    };
    const showTypeFilter = () => {
      common_vendor.index.showActionSheet({
        itemList: ["全部", "收入", "支出"],
        success: (res) => {
          const types = ["all", "income", "expense"];
          typeFilter.value = types[res.tapIndex];
        }
      });
    };
    const showDateFilter = () => {
      common_vendor.index.showActionSheet({
        itemList: ["全部", "今天", "本周", "本月"],
        success: (res) => {
          const dates = ["all", "today", "week", "month"];
          dateFilter.value = dates[res.tapIndex];
        }
      });
    };
    const showCategoryFilter = () => {
      const categories = store_index.store.state.categories[typeFilter.value === "income" ? "income" : "expense"];
      const itemList = ["全部分类", ...categories.map((cat) => cat.name)];
      common_vendor.index.showActionSheet({
        itemList,
        success: (res) => {
          if (res.tapIndex === 0) {
            categoryFilter.value = "all";
          } else {
            categoryFilter.value = categories[res.tapIndex - 1].id;
          }
        }
      });
    };
    const addRecord = () => {
      common_vendor.index.switchTab({
        url: "/pages/add/add"
      });
    };
    const editRecord = (record) => {
      common_vendor.index.setStorageSync("editRecordId", record.id);
      common_vendor.index.switchTab({
        url: "/pages/add/add"
      });
    };
    const showRecordActions = (record) => {
      selectedRecord.value = record;
      common_vendor.index.showActionSheet({
        itemList: ["编辑", "删除"],
        success: (res) => {
          if (res.tapIndex === 0) {
            editSelectedRecord();
          } else if (res.tapIndex === 1) {
            deleteSelectedRecord();
          }
        }
      });
    };
    const editSelectedRecord = () => {
      if (selectedRecord.value) {
        editRecord(selectedRecord.value);
      }
    };
    const deleteSelectedRecord = () => {
      if (selectedRecord.value) {
        common_vendor.index.showModal({
          title: "确认删除",
          content: "确定要删除这条记录吗？",
          success: (res) => {
            if (res.confirm) {
              store_index.store.actions.deleteRecord(selectedRecord.value.id);
              selectedRecord.value = null;
            }
          }
        });
      }
    };
    const closeActionMenu = () => {
      selectedRecord.value = null;
    };
    common_vendor.onMounted(() => {
      store_index.store.actions.loadRecords();
    });
    return {
      filteredRecords,
      groupedRecords,
      summaryIncome,
      summaryExpense,
      summaryBalance,
      typeFilterText,
      dateFilterText,
      categoryFilterText,
      formatAmount: utils_helpers.formatAmount,
      formatDateHeader,
      showTypeFilter,
      showDateFilter,
      showCategoryFilter,
      addRecord,
      editRecord,
      showRecordActions,
      editSelectedRecord,
      deleteSelectedRecord,
      closeActionMenu
    };
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  _component_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($setup.typeFilterText),
    b: common_vendor.o((...args) => $setup.showTypeFilter && $setup.showTypeFilter(...args)),
    c: common_vendor.t($setup.dateFilterText),
    d: common_vendor.o((...args) => $setup.showDateFilter && $setup.showDateFilter(...args)),
    e: common_vendor.t($setup.categoryFilterText),
    f: common_vendor.o((...args) => $setup.showCategoryFilter && $setup.showCategoryFilter(...args)),
    g: $setup.filteredRecords.length > 0
  }, $setup.filteredRecords.length > 0 ? {
    h: common_vendor.t($setup.formatAmount($setup.summaryIncome)),
    i: common_vendor.t($setup.formatAmount($setup.summaryExpense)),
    j: common_vendor.t($setup.formatAmount($setup.summaryBalance)),
    k: $setup.summaryBalance < 0 ? 1 : ""
  } : {}, {
    l: $setup.groupedRecords.length > 0
  }, $setup.groupedRecords.length > 0 ? {
    m: common_vendor.f($setup.groupedRecords, (group, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t($setup.formatDateHeader(group.date)),
        b: group.income > 0
      }, group.income > 0 ? {
        c: common_vendor.t($setup.formatAmount(group.income))
      } : {}, {
        d: group.expense > 0
      }, group.expense > 0 ? {
        e: common_vendor.t($setup.formatAmount(group.expense))
      } : {}, {
        f: common_vendor.f(group.records, (record, k1, i1) => {
          return common_vendor.e({
            a: common_vendor.t(record.categoryIcon),
            b: record.categoryColor,
            c: common_vendor.t(record.categoryName),
            d: record.note
          }, record.note ? {
            e: common_vendor.t(record.note)
          } : {}, {
            f: common_vendor.t(record.time),
            g: common_vendor.t(record.type === "expense" ? "-" : "+"),
            h: common_vendor.t($setup.formatAmount(record.amount)),
            i: common_vendor.n(record.type),
            j: record.id,
            k: common_vendor.o(($event) => $setup.editRecord(record), record.id),
            l: common_vendor.o(($event) => $setup.showRecordActions(record), record.id)
          });
        }),
        g: group.date
      });
    })
  } : {}, {
    n: common_vendor.o((...args) => $setup.addRecord && $setup.addRecord(...args)),
    o: common_vendor.o((...args) => $setup.editSelectedRecord && $setup.editSelectedRecord(...args)),
    p: common_vendor.o((...args) => $setup.deleteSelectedRecord && $setup.deleteSelectedRecord(...args)),
    q: common_vendor.o((...args) => $setup.closeActionMenu && $setup.closeActionMenu(...args)),
    r: common_vendor.sr("actionPopup", "98a9e0b2-0"),
    s: common_vendor.p({
      type: "bottom"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-98a9e0b2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/list/list.js.map
