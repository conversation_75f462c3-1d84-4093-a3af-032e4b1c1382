"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/add/add.js";
  "./pages/list/list.js";
  "./pages/statistics/statistics.js";
  "./pages/settings/settings.js";
  "./pages/test/test.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:6", "记账本启动");
    store_index.store.actions.init();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:11", "记账本显示");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:14", "记账本隐藏");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
