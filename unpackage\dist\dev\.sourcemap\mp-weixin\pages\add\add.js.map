{"version": 3, "file": "add.js", "sources": ["pages/add/add.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRkL2FkZC52dWU"], "sourcesContent": ["<template>\n  <view class=\"add-page\">\n    <!-- 类型切换 -->\n    <view class=\"type-tabs\">\n      <view \n        class=\"type-tab\" \n        :class=\"{ active: currentType === 'expense' }\"\n        @click=\"switchType('expense')\"\n      >\n        <text class=\"tab-text\">支出</text>\n      </view>\n      <view \n        class=\"type-tab\" \n        :class=\"{ active: currentType === 'income' }\"\n        @click=\"switchType('income')\"\n      >\n        <text class=\"tab-text\">收入</text>\n      </view>\n    </view>\n\n    <!-- 金额输入 -->\n    <view class=\"amount-section\">\n      <view class=\"amount-display\">\n        <text class=\"currency\">¥</text>\n        <input \n          class=\"amount-input\" \n          type=\"digit\"\n          :value=\"displayAmount\"\n          @input=\"onAmountInput\"\n          placeholder=\"0.00\"\n          :focus=\"amountFocused\"\n        />\n      </view>\n    </view>\n\n    <!-- 分类选择 -->\n    <view class=\"category-section\">\n      <view class=\"section-title\">选择分类</view>\n      <view class=\"category-grid\">\n        <view \n          class=\"category-item\" \n          v-for=\"category in currentCategories\" \n          :key=\"category.id\"\n          :class=\"{ active: selectedCategory?.id === category.id }\"\n          @click=\"selectCategory(category)\"\n        >\n          <view class=\"category-icon\" :style=\"{ backgroundColor: category.color }\">\n            {{ category.icon }}\n          </view>\n          <text class=\"category-name\">{{ category.name }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 日期时间 -->\n    <view class=\"datetime-section\">\n      <view class=\"datetime-item\" @click=\"showDatePicker\">\n        <text class=\"datetime-label\">日期</text>\n        <text class=\"datetime-value\">{{ formatDate(formData.date, 'YYYY年MM月DD日') }}</text>\n      </view>\n      <view class=\"datetime-item\" @click=\"showTimePicker\">\n        <text class=\"datetime-label\">时间</text>\n        <text class=\"datetime-value\">{{ formData.time }}</text>\n      </view>\n    </view>\n\n    <!-- 备注输入 -->\n    <view class=\"note-section\">\n      <view class=\"section-title\">备注</view>\n      <textarea \n        class=\"note-input\" \n        v-model=\"formData.note\"\n        placeholder=\"添加备注信息（可选）\"\n        maxlength=\"100\"\n      />\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"cancel\">取消</button>\n      <button class=\"btn btn-primary\" @click=\"save\" :disabled=\"!canSave\">\n        {{ isEdit ? '更新' : '保存' }}\n      </button>\n    </view>\n\n    <!-- 日期选择器 -->\n    <picker\n      mode=\"date\"\n      :value=\"formData.date\"\n      @change=\"onDateChange\"\n      :start=\"minDate\"\n      :end=\"maxDate\"\n      v-show=\"false\"\n      ref=\"datePicker\"\n    >\n      <view></view>\n    </picker>\n\n    <!-- 时间选择器 -->\n    <picker\n      mode=\"time\"\n      :value=\"formData.time\"\n      @change=\"onTimeChange\"\n      v-show=\"false\"\n      ref=\"timePicker\"\n    >\n      <view></view>\n    </picker>\n  </view>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport store from '../../store/index.js'\nimport { formatDate, getToday, showToast } from '../../utils/helpers.js'\n\nexport default {\n  name: 'AddPage',\n  setup() {\n    // 表单数据\n    const formData = ref({\n      type: 'expense',\n      amount: 0,\n      categoryId: null,\n      categoryName: '',\n      categoryIcon: '',\n      categoryColor: '',\n      note: '',\n      date: getToday(),\n      time: new Date().toTimeString().split(' ')[0].substring(0, 5)\n    })\n\n    // 界面状态\n    const currentType = ref('expense')\n    const selectedCategory = ref(null)\n    const displayAmount = ref('')\n    const amountFocused = ref(true)\n    const isEdit = ref(false)\n    const editId = ref('')\n\n    // 计算属性\n    const currentCategories = computed(() => \n      store.state.categories[currentType.value] || []\n    )\n\n    const canSave = computed(() => \n      formData.value.amount > 0 && selectedCategory.value\n    )\n\n    const minDate = computed(() => '2020-01-01')\n    const maxDate = computed(() => {\n      const future = new Date()\n      future.setFullYear(future.getFullYear() + 1)\n      return formatDate(future)\n    })\n\n    // 方法\n    const switchType = (type) => {\n      currentType.value = type\n      formData.value.type = type\n      selectedCategory.value = null\n      formData.value.categoryId = null\n      formData.value.categoryName = ''\n      formData.value.categoryIcon = ''\n      formData.value.categoryColor = ''\n    }\n\n    const onAmountInput = (e) => {\n      let value = e.detail.value\n      // 只允许数字和小数点\n      value = value.replace(/[^\\d.]/g, '')\n      \n      // 确保只有一个小数点\n      const parts = value.split('.')\n      if (parts.length > 2) {\n        value = parts[0] + '.' + parts.slice(1).join('')\n      }\n      \n      // 限制小数位数\n      if (parts[1] && parts[1].length > 2) {\n        value = parts[0] + '.' + parts[1].substring(0, 2)\n      }\n      \n      displayAmount.value = value\n      formData.value.amount = parseFloat(value) || 0\n    }\n\n    const selectCategory = (category) => {\n      selectedCategory.value = category\n      formData.value.categoryId = category.id\n      formData.value.categoryName = category.name\n      formData.value.categoryIcon = category.icon\n      formData.value.categoryColor = category.color\n    }\n\n    const showDatePicker = () => {\n      // 触发日期选择器\n      uni.showModal({\n        title: '提示',\n        content: '请使用系统日期选择器',\n        showCancel: false\n      })\n    }\n\n    const showTimePicker = () => {\n      // 触发时间选择器\n      uni.showModal({\n        title: '提示',\n        content: '请使用系统时间选择器',\n        showCancel: false\n      })\n    }\n\n    const onDateChange = (e) => {\n      formData.value.date = e.detail.value\n    }\n\n    const onTimeChange = (e) => {\n      formData.value.time = e.detail.value\n    }\n\n    const save = () => {\n      if (!canSave.value) {\n        showToast('请填写完整信息')\n        return\n      }\n\n      try {\n        if (isEdit.value) {\n          store.actions.updateRecord(editId.value, formData.value)\n        } else {\n          store.actions.addRecord(formData.value)\n        }\n        \n        // 返回上一页\n        uni.navigateBack()\n      } catch (error) {\n        console.error('保存失败:', error)\n        showToast('保存失败')\n      }\n    }\n\n    const cancel = () => {\n      uni.navigateBack()\n    }\n\n    // 页面加载\n    onMounted(() => {\n      // 加载分类数据\n      store.actions.loadCategories()\n\n      // 注意：在实际的uniapp环境中，页面参数会通过onLoad生命周期获取\n      // 这里暂时不处理编辑模式，可以在实际运行时通过onLoad获取参数\n    })\n\n    return {\n      formData,\n      currentType,\n      selectedCategory,\n      displayAmount,\n      amountFocused,\n      isEdit,\n      currentCategories,\n      canSave,\n      minDate,\n      maxDate,\n      switchType,\n      onAmountInput,\n      selectCategory,\n      showDatePicker,\n      showTimePicker,\n      onDateChange,\n      onTimeChange,\n      save,\n      cancel,\n      formatDate\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.add-page {\n  background-color: $uni-bg-color-grey;\n  min-height: 100vh;\n}\n\n/* 类型切换 */\n.type-tabs {\n  display: flex;\n  background-color: white;\n  margin-bottom: 16px;\n}\n\n.type-tab {\n  flex: 1;\n  padding: 16px;\n  text-align: center;\n  border-bottom: 3px solid transparent;\n  transition: all 0.3s ease;\n\n  &.active {\n    border-bottom-color: $uni-color-primary;\n\n    .tab-text {\n      color: $uni-color-primary;\n      font-weight: 600;\n    }\n  }\n}\n\n.tab-text {\n  font-size: 16px;\n  color: $uni-text-color-grey;\n}\n\n/* 金额输入 */\n.amount-section {\n  background-color: white;\n  padding: 40px 20px;\n  margin-bottom: 16px;\n  text-align: center;\n}\n\n.amount-display {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.currency {\n  font-size: 32px;\n  color: $uni-text-color-grey;\n  margin-right: 8px;\n}\n\n.amount-input {\n  font-size: 48px;\n  font-weight: 300;\n  color: $uni-text-color;\n  border: none;\n  outline: none;\n  background: transparent;\n  text-align: left;\n  min-width: 200px;\n}\n\n/* 分类选择 */\n.category-section {\n  background-color: white;\n  padding: 20px;\n  margin-bottom: 16px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: $uni-text-color;\n  margin-bottom: 16px;\n}\n\n.category-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 16px;\n}\n\n.category-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 12px 8px;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n\n  &.active {\n    background-color: rgba(76, 175, 80, 0.1);\n\n    .category-icon {\n      transform: scale(1.1);\n    }\n\n    .category-name {\n      color: $uni-color-primary;\n      font-weight: 600;\n    }\n  }\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.category-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  margin-bottom: 8px;\n  transition: transform 0.3s ease;\n}\n\n.category-name {\n  font-size: 12px;\n  color: $uni-text-color;\n  text-align: center;\n}\n\n/* 日期时间 */\n.datetime-section {\n  background-color: white;\n  margin-bottom: 16px;\n}\n\n.datetime-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid $uni-border-color;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &:active {\n    background-color: $uni-bg-color-hover;\n  }\n}\n\n.datetime-label {\n  font-size: 16px;\n  color: $uni-text-color;\n}\n\n.datetime-value {\n  font-size: 16px;\n  color: $uni-text-color-grey;\n}\n\n/* 备注输入 */\n.note-section {\n  background-color: white;\n  padding: 20px;\n  margin-bottom: 32px;\n}\n\n.note-input {\n  width: 100%;\n  min-height: 80px;\n  padding: 12px;\n  border: 1px solid $uni-border-color;\n  border-radius: 8px;\n  font-size: 16px;\n  line-height: 1.5;\n  resize: none;\n\n  &:focus {\n    border-color: $uni-color-primary;\n    outline: none;\n  }\n}\n\n/* 操作按钮 */\n.action-buttons {\n  display: flex;\n  gap: 16px;\n  padding: 0 20px 32px;\n\n  .btn {\n    flex: 1;\n    height: 48px;\n    border-radius: 24px;\n    font-size: 16px;\n    font-weight: 600;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/add/add.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "get<PERSON><PERSON>y", "computed", "store", "formatDate", "uni", "showToast", "onMounted"], "mappings": ";;;;AAoHA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAMC,cAAAA,SAAU;AAAA,MAChB,OAAM,oBAAI,QAAO,aAAc,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA,KAC7D;AAGD,UAAM,cAAcD,cAAG,IAAC,SAAS;AACjC,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AACjC,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,IAAI;AAC9B,UAAM,SAASA,cAAG,IAAC,KAAK;AACxB,UAAM,SAASA,cAAG,IAAC,EAAE;AAGrB,UAAM,oBAAoBE,cAAAA;AAAAA,MAAS,MACjCC,YAAAA,MAAM,MAAM,WAAW,YAAY,KAAK,KAAK,CAAC;AAAA,IAChD;AAEA,UAAM,UAAUD,cAAAA;AAAAA,MAAS,MACvB,SAAS,MAAM,SAAS,KAAK,iBAAiB;AAAA,IAChD;AAEA,UAAM,UAAUA,uBAAS,MAAM,YAAY;AAC3C,UAAM,UAAUA,cAAAA,SAAS,MAAM;AAC7B,YAAM,SAAS,oBAAI,KAAK;AACxB,aAAO,YAAY,OAAO,YAAW,IAAK,CAAC;AAC3C,aAAOE,cAAAA,WAAW,MAAM;AAAA,KACzB;AAGD,UAAM,aAAa,CAAC,SAAS;AAC3B,kBAAY,QAAQ;AACpB,eAAS,MAAM,OAAO;AACtB,uBAAiB,QAAQ;AACzB,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,gBAAgB;AAAA,IACjC;AAEA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,UAAI,QAAQ,EAAE,OAAO;AAErB,cAAQ,MAAM,QAAQ,WAAW,EAAE;AAGnC,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,MACjD;AAGA,UAAI,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,SAAS,GAAG;AACnC,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA,MAClD;AAEA,oBAAc,QAAQ;AACtB,eAAS,MAAM,SAAS,WAAW,KAAK,KAAK;AAAA,IAC/C;AAEA,UAAM,iBAAiB,CAAC,aAAa;AACnC,uBAAiB,QAAQ;AACzB,eAAS,MAAM,aAAa,SAAS;AACrC,eAAS,MAAM,eAAe,SAAS;AACvC,eAAS,MAAM,eAAe,SAAS;AACvC,eAAS,MAAM,gBAAgB,SAAS;AAAA,IAC1C;AAEA,UAAM,iBAAiB,MAAM;AAE3BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACb;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACb;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,OAAO,EAAE,OAAO;AAAA,IACjC;AAEA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,OAAO,EAAE,OAAO;AAAA,IACjC;AAEA,UAAM,OAAO,MAAM;AACjB,UAAI,CAAC,QAAQ,OAAO;AAClBC,sBAAAA,UAAU,SAAS;AACnB;AAAA,MACF;AAEA,UAAI;AACF,YAAI,OAAO,OAAO;AAChBH,sBAAK,MAAC,QAAQ,aAAa,OAAO,OAAO,SAAS,KAAK;AAAA,eAClD;AACLA,sBAAAA,MAAM,QAAQ,UAAU,SAAS,KAAK;AAAA,QACxC;AAGAE,sBAAAA,MAAI,aAAa;AAAA,MACjB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,SAAS,KAAK;AAC5BC,sBAAAA,UAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAEA,UAAM,SAAS,MAAM;AACnBD,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAGAE,kBAAAA,UAAU,MAAM;AAEdJ,kBAAK,MAAC,QAAQ,eAAe;AAAA,KAI9B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAAC,cAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrRA,GAAG,WAAW,eAAe;"}