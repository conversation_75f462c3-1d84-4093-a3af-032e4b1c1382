{"version": 3, "file": "add.js", "sources": ["pages/add/add.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRkL2FkZC52dWU"], "sourcesContent": ["<template>\n  <view class=\"add-page\">\n    <!-- 顶部标题栏 -->\n    <view class=\"header-section\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">{{ isEdit ? '编辑记录' : '记一笔' }}</text>\n        <text class=\"page-subtitle\">{{ currentType === 'expense' ? '记录支出' : '记录收入' }}</text>\n      </view>\n      <view class=\"header-actions\" v-if=\"isEdit\">\n        <view class=\"action-btn delete-btn\" @click=\"deleteRecord\">\n          <text class=\"action-icon\">🗑️</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 类型切换 -->\n    <view class=\"type-section\">\n      <view class=\"type-tabs\">\n        <view\n          class=\"type-tab\"\n          :class=\"{ active: currentType === 'expense' }\"\n          @click=\"switchType('expense')\"\n        >\n          <view class=\"tab-icon\">💸</view>\n          <text class=\"tab-text\">支出</text>\n          <view class=\"tab-indicator\"></view>\n        </view>\n        <view\n          class=\"type-tab\"\n          :class=\"{ active: currentType === 'income' }\"\n          @click=\"switchType('income')\"\n        >\n          <view class=\"tab-icon\">💰</view>\n          <text class=\"tab-text\">收入</text>\n          <view class=\"tab-indicator\"></view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 金额输入区域 -->\n    <view class=\"amount-section\">\n      <view class=\"amount-container\">\n        <view class=\"amount-header\">\n          <text class=\"amount-label\">金额</text>\n          <view class=\"quick-amounts\">\n            <view\n              class=\"quick-amount\"\n              v-for=\"amount in quickAmounts\"\n              :key=\"amount\"\n              @click=\"setQuickAmount(amount)\"\n            >\n              <text class=\"quick-text\">{{ amount }}</text>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"amount-display\">\n          <text class=\"currency\">¥</text>\n          <input\n            class=\"amount-input\"\n            type=\"digit\"\n            :value=\"displayAmount\"\n            @input=\"onAmountInput\"\n            @focus=\"onAmountFocus\"\n            @blur=\"onAmountBlur\"\n            placeholder=\"0.00\"\n            :focus=\"amountFocused\"\n          />\n        </view>\n\n        <view class=\"amount-tools\">\n          <view class=\"calculator-btn\" @click=\"showCalculator\">\n            <text class=\"calc-icon\">🧮</text>\n            <text class=\"calc-text\">计算器</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 分类选择 -->\n    <view class=\"category-section\">\n      <view class=\"section-header\">\n        <view class=\"header-left\">\n          <text class=\"section-title\">选择分类</text>\n          <text class=\"section-subtitle\">{{ currentCategories.length }}个分类</text>\n        </view>\n        <view class=\"header-right\">\n          <view class=\"manage-btn\" @click=\"manageCategories\">\n            <text class=\"manage-text\">管理</text>\n          </view>\n        </view>\n      </view>\n\n      <view class=\"category-grid\">\n        <view\n          class=\"category-item\"\n          v-for=\"(category, index) in currentCategories\"\n          :key=\"category.id\"\n          :class=\"{ active: selectedCategory?.id === category.id }\"\n          :style=\"{ animationDelay: (index * 0.05) + 's' }\"\n          @click=\"selectCategory(category)\"\n        >\n          <view class=\"category-icon-wrapper\">\n            <view class=\"category-icon\" :style=\"{ backgroundColor: category.color }\">\n              <text class=\"icon-text\">{{ category.icon }}</text>\n            </view>\n            <view class=\"selection-indicator\" v-if=\"selectedCategory?.id === category.id\">\n              <text class=\"check-icon\">✓</text>\n            </view>\n          </view>\n          <text class=\"category-name\">{{ category.name }}</text>\n        </view>\n\n        <!-- 添加新分类按钮 -->\n        <view class=\"category-item add-category\" @click=\"addNewCategory\">\n          <view class=\"category-icon-wrapper\">\n            <view class=\"category-icon add-icon\">\n              <text class=\"icon-text\">+</text>\n            </view>\n          </view>\n          <text class=\"category-name\">添加分类</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 详细信息卡片 -->\n    <view class=\"details-section\">\n      <!-- 日期时间 -->\n      <view class=\"detail-card datetime-card\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">日期时间</text>\n          <view class=\"quick-time-btns\">\n            <view class=\"quick-btn\" @click=\"setCurrentTime\">\n              <text class=\"quick-text\">现在</text>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"datetime-row\">\n          <view class=\"datetime-item\" @click=\"showDatePicker\">\n            <view class=\"datetime-icon\">📅</view>\n            <view class=\"datetime-content\">\n              <text class=\"datetime-label\">日期</text>\n              <text class=\"datetime-value\">{{ formatDate(formData.date, 'YYYY年MM月DD日') }}</text>\n            </view>\n            <view class=\"datetime-arrow\">→</view>\n          </view>\n\n          <view class=\"datetime-divider\"></view>\n\n          <view class=\"datetime-item\" @click=\"showTimePicker\">\n            <view class=\"datetime-icon\">🕐</view>\n            <view class=\"datetime-content\">\n              <text class=\"datetime-label\">时间</text>\n              <text class=\"datetime-value\">{{ formData.time }}</text>\n            </view>\n            <view class=\"datetime-arrow\">→</view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 备注输入 -->\n      <view class=\"detail-card note-card\">\n        <view class=\"card-header\">\n          <text class=\"card-title\">备注信息</text>\n          <text class=\"char-count\">{{ formData.note.length }}/100</text>\n        </view>\n\n        <view class=\"note-input-wrapper\">\n          <textarea\n            class=\"note-input\"\n            v-model=\"formData.note\"\n            placeholder=\"添加备注信息，如：在哪里消费、和谁一起等（可选）\"\n            maxlength=\"100\"\n            @focus=\"onNoteFocus\"\n            @blur=\"onNoteBlur\"\n          />\n          <view class=\"note-suggestions\" v-if=\"noteSuggestions.length > 0\">\n            <view\n              class=\"suggestion-item\"\n              v-for=\"suggestion in noteSuggestions\"\n              :key=\"suggestion\"\n              @click=\"selectNoteSuggestion(suggestion)\"\n            >\n              <text class=\"suggestion-text\">{{ suggestion }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <button class=\"btn btn-secondary\" @click=\"cancel\">取消</button>\n      <button class=\"btn btn-primary\" @click=\"save\" :disabled=\"!canSave\">\n        {{ isEdit ? '更新' : '保存' }}\n      </button>\n    </view>\n\n    <!-- 日期选择器 -->\n    <picker\n      mode=\"date\"\n      :value=\"formData.date\"\n      @change=\"onDateChange\"\n      :start=\"minDate\"\n      :end=\"maxDate\"\n      v-show=\"false\"\n      ref=\"datePicker\"\n    >\n      <view></view>\n    </picker>\n\n    <!-- 时间选择器 -->\n    <picker\n      mode=\"time\"\n      :value=\"formData.time\"\n      @change=\"onTimeChange\"\n      v-show=\"false\"\n      ref=\"timePicker\"\n    >\n      <view></view>\n    </picker>\n  </view>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport store from '../../store/index.js'\nimport { formatDate, getToday, showToast } from '../../utils/helpers.js'\n\nexport default {\n  name: 'AddPage',\n  setup() {\n    // 表单数据\n    const formData = ref({\n      type: 'expense',\n      amount: 0,\n      categoryId: null,\n      categoryName: '',\n      categoryIcon: '',\n      categoryColor: '',\n      note: '',\n      date: getToday(),\n      time: new Date().toTimeString().split(' ')[0].substring(0, 5)\n    })\n\n    // 界面状态\n    const currentType = ref('expense')\n    const selectedCategory = ref(null)\n    const displayAmount = ref('')\n    const amountFocused = ref(false)\n    const isEdit = ref(false)\n    const editId = ref(null)\n\n    // 快速金额选项\n    const quickAmounts = ref([10, 20, 50, 100, 200, 500])\n\n    // 备注建议\n    const noteSuggestions = computed(() => {\n      if (!formData.value.note || formData.value.note.length < 2) return []\n\n      // 根据分类提供建议\n      const suggestions = {\n        '餐饮': ['早餐', '午餐', '晚餐', '下午茶', '夜宵'],\n        '交通': ['地铁', '公交', '打车', '加油', '停车费'],\n        '购物': ['超市', '网购', '服装', '日用品'],\n        '娱乐': ['电影', '游戏', 'KTV', '聚会'],\n        '医疗': ['看病', '买药', '体检', '保健品'],\n        '教育': ['培训', '书籍', '课程', '学费']\n      }\n\n      const categoryName = selectedCategory.value?.name || ''\n      return suggestions[categoryName] || []\n    })\n\n    // 计算属性\n    const currentCategories = computed(() => \n      store.state.categories[currentType.value] || []\n    )\n\n    const canSave = computed(() => \n      formData.value.amount > 0 && selectedCategory.value\n    )\n\n    const minDate = computed(() => '2020-01-01')\n    const maxDate = computed(() => {\n      const future = new Date()\n      future.setFullYear(future.getFullYear() + 1)\n      return formatDate(future)\n    })\n\n    // 方法\n    const switchType = (type) => {\n      currentType.value = type\n      formData.value.type = type\n      selectedCategory.value = null\n      formData.value.categoryId = null\n      formData.value.categoryName = ''\n      formData.value.categoryIcon = ''\n      formData.value.categoryColor = ''\n    }\n\n    const onAmountInput = (e) => {\n      let value = e.detail.value\n      // 只允许数字和小数点\n      value = value.replace(/[^\\d.]/g, '')\n      \n      // 确保只有一个小数点\n      const parts = value.split('.')\n      if (parts.length > 2) {\n        value = parts[0] + '.' + parts.slice(1).join('')\n      }\n      \n      // 限制小数位数\n      if (parts[1] && parts[1].length > 2) {\n        value = parts[0] + '.' + parts[1].substring(0, 2)\n      }\n      \n      displayAmount.value = value\n      formData.value.amount = parseFloat(value) || 0\n    }\n\n    // 新增的UI支持方法\n    const onAmountFocus = () => {\n      amountFocused.value = true\n    }\n\n    const onAmountBlur = () => {\n      amountFocused.value = false\n    }\n\n    const setQuickAmount = (amount) => {\n      displayAmount.value = amount.toString()\n      formData.value.amount = amount\n    }\n\n    const showCalculator = () => {\n      // 显示计算器功能（可以后续实现）\n      showToast('计算器功能开发中')\n    }\n\n    const manageCategories = () => {\n      showToast('分类管理功能开发中')\n    }\n\n    const addNewCategory = () => {\n      showToast('添加分类功能开发中')\n    }\n\n    const setCurrentTime = () => {\n      const now = new Date()\n      formData.value.date = getToday()\n      formData.value.time = now.toTimeString().split(' ')[0].substring(0, 5)\n      showToast('已设置为当前时间')\n    }\n\n    const onNoteFocus = () => {\n      // 备注输入框获得焦点时的处理\n    }\n\n    const onNoteBlur = () => {\n      // 备注输入框失去焦点时的处理\n    }\n\n    const selectNoteSuggestion = (suggestion) => {\n      formData.value.note = suggestion\n    }\n\n    const deleteRecord = () => {\n      if (!isEdit.value) return\n\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这条记录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            store.actions.deleteRecord(editId.value)\n            uni.navigateBack()\n          }\n        }\n      })\n    }\n\n    const selectCategory = (category) => {\n      selectedCategory.value = category\n      formData.value.categoryId = category.id\n      formData.value.categoryName = category.name\n      formData.value.categoryIcon = category.icon\n      formData.value.categoryColor = category.color\n    }\n\n    const showDatePicker = () => {\n      // 触发日期选择器\n      uni.showModal({\n        title: '提示',\n        content: '请使用系统日期选择器',\n        showCancel: false\n      })\n    }\n\n    const showTimePicker = () => {\n      // 触发时间选择器\n      uni.showModal({\n        title: '提示',\n        content: '请使用系统时间选择器',\n        showCancel: false\n      })\n    }\n\n    const onDateChange = (e) => {\n      formData.value.date = e.detail.value\n    }\n\n    const onTimeChange = (e) => {\n      formData.value.time = e.detail.value\n    }\n\n    const save = () => {\n      if (!canSave.value) {\n        showToast('请填写完整信息')\n        return\n      }\n\n      try {\n        if (isEdit.value) {\n          store.actions.updateRecord(editId.value, formData.value)\n        } else {\n          store.actions.addRecord(formData.value)\n        }\n        \n        // 返回上一页\n        uni.navigateBack()\n      } catch (error) {\n        console.error('保存失败:', error)\n        showToast('保存失败')\n      }\n    }\n\n    const cancel = () => {\n      uni.navigateBack()\n    }\n\n    // 加载编辑记录\n    const loadEditRecord = (recordId) => {\n      const record = store.state.records.find(r => r.id === recordId)\n      if (record) {\n        isEdit.value = true\n        editId.value = recordId\n\n        // 设置表单数据\n        formData.value = {\n          type: record.type,\n          amount: record.amount,\n          categoryId: record.categoryId,\n          categoryName: record.categoryName,\n          categoryIcon: record.categoryIcon,\n          categoryColor: record.categoryColor,\n          note: record.note,\n          date: record.date,\n          time: record.time\n        }\n\n        // 设置界面状态\n        currentType.value = record.type\n        displayAmount.value = record.amount.toString()\n\n        // 设置选中的分类\n        const categories = store.state.categories[record.type] || []\n        selectedCategory.value = categories.find(cat => cat.id === record.categoryId)\n      }\n    }\n\n    // 页面加载\n    onMounted(() => {\n      // 加载分类数据\n      store.actions.loadCategories()\n\n      // 检查是否有快速记账类型\n      const quickAddType = uni.getStorageSync('quickAddType')\n      if (quickAddType) {\n        switchType(quickAddType)\n        // 清除存储的类型\n        uni.removeStorageSync('quickAddType')\n      }\n\n      // 检查是否有编辑记录ID\n      const editRecordId = uni.getStorageSync('editRecordId')\n      if (editRecordId) {\n        loadEditRecord(editRecordId)\n        // 清除存储的ID\n        uni.removeStorageSync('editRecordId')\n      }\n\n      // 注意：在实际的uniapp环境中，页面参数会通过onLoad生命周期获取\n      // 这里暂时不处理编辑模式，可以在实际运行时通过onLoad获取参数\n    })\n\n    return {\n      formData,\n      currentType,\n      selectedCategory,\n      displayAmount,\n      amountFocused,\n      isEdit,\n      editId,\n      quickAmounts,\n      noteSuggestions,\n      currentCategories,\n      canSave,\n      minDate,\n      maxDate,\n      switchType,\n      onAmountInput,\n      onAmountFocus,\n      onAmountBlur,\n      setQuickAmount,\n      showCalculator,\n      manageCategories,\n      addNewCategory,\n      setCurrentTime,\n      onNoteFocus,\n      onNoteBlur,\n      selectNoteSuggestion,\n      deleteRecord,\n      selectCategory,\n      showDatePicker,\n      showTimePicker,\n      onDateChange,\n      onTimeChange,\n      save,\n      cancel,\n      formatDate\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.add-page {\n  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);\n  min-height: 100vh;\n  padding: 0;\n}\n\n/* 顶部标题栏 */\n.header-section {\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\n  padding: 20px 16px 24px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  color: white;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 700;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.page-subtitle {\n  font-size: 14px;\n  opacity: 0.9;\n  display: block;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.action-btn {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  background: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n}\n\n.action-btn:active {\n  transform: scale(0.95);\n  background: rgba(255, 255, 255, 0.3);\n}\n\n.delete-btn {\n  background: rgba(244, 67, 54, 0.3);\n}\n\n.action-icon {\n  font-size: 18px;\n}\n\n/* 类型切换区域 */\n.type-section {\n  padding: 0 16px;\n  margin-top: -12px;\n  margin-bottom: 24px;\n}\n\n.type-tabs {\n  background: white;\n  border-radius: 16px;\n  padding: 8px;\n  display: flex;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.type-tab {\n  flex: 1;\n  padding: 16px 12px;\n  border-radius: 12px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.type-tab:active {\n  transform: scale(0.98);\n}\n\n.type-tab.active {\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\n  color: white;\n  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);\n}\n\n.type-tab.active .tab-indicator {\n  opacity: 1;\n}\n\n.tab-icon {\n  font-size: 20px;\n}\n\n.tab-text {\n  font-size: 14px;\n  font-weight: 600;\n  color: #666;\n}\n\n.type-tab.active .tab-text {\n  color: white;\n}\n\n.tab-indicator {\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 20px;\n  height: 3px;\n  background: white;\n  border-radius: 2px;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n/* 金额输入区域 */\n.amount-section {\n  padding: 0 16px;\n  margin-bottom: 24px;\n}\n\n.amount-container {\n  background: white;\n  border-radius: 20px;\n  padding: 24px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.amount-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.amount-label {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.quick-amounts {\n  display: flex;\n  gap: 8px;\n}\n\n.quick-amount {\n  padding: 6px 12px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.quick-amount:active {\n  background: rgba(76, 175, 80, 0.2);\n  transform: scale(0.95);\n}\n\n.quick-text {\n  font-size: 12px;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n.amount-display {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n  padding: 20px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 16px;\n  border: 2px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.amount-display:focus-within {\n  border-color: #4CAF50;\n  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);\n}\n\n.currency {\n  font-size: 32px;\n  color: #666;\n  margin-right: 8px;\n  font-weight: 600;\n}\n\n.amount-input {\n  font-size: 48px;\n  font-weight: 300;\n  color: #333;\n  border: none;\n  outline: none;\n  background: transparent;\n  text-align: left;\n  min-width: 200px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.amount-input::placeholder {\n  color: #ccc;\n}\n\n.amount-tools {\n  display: flex;\n  justify-content: center;\n}\n\n.calculator-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 20px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 20px;\n  transition: all 0.3s ease;\n}\n\n.calculator-btn:active {\n  background: rgba(76, 175, 80, 0.2);\n  transform: scale(0.95);\n}\n\n.calc-icon {\n  font-size: 16px;\n}\n\n.calc-text {\n  font-size: 14px;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n/* 分类选择 */\n.category-section {\n  padding: 0 16px;\n  margin-bottom: 24px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.header-left {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.section-title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #333;\n}\n\n.section-subtitle {\n  font-size: 12px;\n  color: #666;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n}\n\n.manage-btn {\n  padding: 8px 16px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 16px;\n  transition: all 0.3s ease;\n}\n\n.manage-btn:active {\n  background: rgba(76, 175, 80, 0.2);\n  transform: scale(0.95);\n}\n\n.manage-text {\n  font-size: 14px;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n.category-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 16px;\n  background: white;\n  padding: 20px;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.category-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 16px 8px;\n  border-radius: 16px;\n  transition: all 0.3s ease;\n  position: relative;\n  animation: fadeInUp 0.6s ease-out both;\n}\n\n.category-item:active {\n  transform: scale(0.95);\n}\n\n.category-item.active {\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\n  color: white;\n  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);\n}\n\n.add-category {\n  border: 2px dashed #ddd;\n  background: transparent;\n}\n\n.add-category:active {\n  border-color: #4CAF50;\n  background: rgba(76, 175, 80, 0.05);\n}\n\n.category-icon-wrapper {\n  position: relative;\n  margin-bottom: 8px;\n}\n\n.category-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.category-item.active .category-icon {\n  background: rgba(255, 255, 255, 0.2) !important;\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n}\n\n.add-icon {\n  background: #f0f0f0 !important;\n  color: #999;\n}\n\n.icon-text {\n  font-size: 20px;\n  color: white;\n}\n\n.add-category .icon-text {\n  color: #999;\n  font-size: 24px;\n  font-weight: 300;\n}\n\n.selection-indicator {\n  position: absolute;\n  top: -4px;\n  right: -4px;\n  width: 20px;\n  height: 20px;\n  background: white;\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.check-icon {\n  font-size: 12px;\n  color: #4CAF50;\n  font-weight: bold;\n}\n\n.category-name {\n  font-size: 12px;\n  color: #666;\n  font-weight: 500;\n  text-align: center;\n  line-height: 1.2;\n}\n\n.category-item.active .category-name {\n  color: white;\n}\n\n.add-category .category-name {\n  color: #999;\n}\n\n/* 详细信息区域 */\n.details-section {\n  padding: 0 16px;\n  margin-bottom: 24px;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.detail-card {\n  background: white;\n  border-radius: 20px;\n  padding: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #333;\n}\n\n.quick-time-btns {\n  display: flex;\n  gap: 8px;\n}\n\n.quick-btn {\n  padding: 6px 12px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 12px;\n  transition: all 0.3s ease;\n}\n\n.quick-btn:active {\n  background: rgba(76, 175, 80, 0.2);\n  transform: scale(0.95);\n}\n\n.quick-text {\n  font-size: 12px;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n.char-count {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 日期时间卡片 */\n.datetime-row {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.datetime-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 16px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 16px;\n  transition: all 0.3s ease;\n}\n\n.datetime-item:active {\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\n  transform: scale(0.98);\n}\n\n.datetime-icon {\n  font-size: 20px;\n}\n\n.datetime-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.datetime-label {\n  font-size: 12px;\n  color: #666;\n  font-weight: 500;\n}\n\n.datetime-value {\n  font-size: 16px;\n  color: #333;\n  font-weight: 600;\n}\n\n.datetime-arrow {\n  font-size: 16px;\n  color: #ccc;\n  font-weight: bold;\n}\n\n.datetime-divider {\n  height: 1px;\n  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);\n  margin: 8px 0;\n}\n\n/* 备注输入卡片 */\n.note-input-wrapper {\n  position: relative;\n}\n\n.note-input {\n  width: 100%;\n  min-height: 80px;\n  padding: 16px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border: 2px solid transparent;\n  border-radius: 16px;\n  font-size: 14px;\n  color: #333;\n  line-height: 1.5;\n  resize: none;\n  transition: all 0.3s ease;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.note-input:focus {\n  border-color: #4CAF50;\n  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);\n  background: white;\n}\n\n.note-input::placeholder {\n  color: #999;\n}\n\n.note-suggestions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 12px;\n}\n\n.suggestion-item {\n  padding: 8px 12px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 16px;\n  transition: all 0.3s ease;\n}\n\n.suggestion-item:active {\n  background: rgba(76, 175, 80, 0.2);\n  transform: scale(0.95);\n}\n\n.suggestion-text {\n  font-size: 12px;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n/* 动画效果 */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slideInDown {\n  from {\n    opacity: 0;\n    transform: translateY(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes bounceIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.3);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* 应用动画 */\n.header-section {\n  animation: slideInDown 0.6s ease-out;\n}\n\n.type-section {\n  animation: fadeInUp 0.6s ease-out 0.1s both;\n}\n\n.amount-section {\n  animation: fadeInUp 0.6s ease-out 0.2s both;\n}\n\n.category-section {\n  animation: fadeInUp 0.6s ease-out 0.3s both;\n}\n\n.details-section {\n  animation: fadeInUp 0.6s ease-out 0.4s both;\n}\n\n/* 响应式设计 */\n@media (max-width: 375px) {\n  .category-grid {\n    grid-template-columns: repeat(3, 1fr);\n    gap: 12px;\n  }\n\n  .quick-amounts {\n    flex-wrap: wrap;\n    gap: 6px;\n  }\n\n  .quick-amount {\n    padding: 4px 8px;\n  }\n\n  .amount-input {\n    font-size: 36px;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/add/add.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "get<PERSON><PERSON>y", "computed", "store", "formatDate", "showToast", "uni", "onMounted"], "mappings": ";;;;AAsOA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM;AAAA,MACN,MAAMC,cAAAA,SAAU;AAAA,MAChB,OAAM,oBAAI,QAAO,aAAc,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA,KAC7D;AAGD,UAAM,cAAcD,cAAG,IAAC,SAAS;AACjC,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AACjC,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,UAAM,gBAAgBA,cAAG,IAAC,KAAK;AAC/B,UAAM,SAASA,cAAG,IAAC,KAAK;AACxB,UAAM,SAASA,cAAG,IAAC,IAAI;AAGvB,UAAM,eAAeA,kBAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,GAAG,CAAC;AAGpD,UAAM,kBAAkBE,cAAAA,SAAS,MAAM;;AACrC,UAAI,CAAC,SAAS,MAAM,QAAQ,SAAS,MAAM,KAAK,SAAS;AAAG,eAAO,CAAC;AAGpE,YAAM,cAAc;AAAA,QAClB,MAAM,CAAC,MAAM,MAAM,MAAM,OAAO,IAAI;AAAA,QACpC,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK;AAAA,QACpC,MAAM,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,QAC9B,MAAM,CAAC,MAAM,MAAM,OAAO,IAAI;AAAA,QAC9B,MAAM,CAAC,MAAM,MAAM,MAAM,KAAK;AAAA,QAC9B,MAAM,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAC/B;AAEA,YAAM,iBAAe,sBAAiB,UAAjB,mBAAwB,SAAQ;AACrD,aAAO,YAAY,YAAY,KAAK,CAAC;AAAA,KACtC;AAGD,UAAM,oBAAoBA,cAAAA;AAAAA,MAAS,MACjCC,YAAAA,MAAM,MAAM,WAAW,YAAY,KAAK,KAAK,CAAC;AAAA,IAChD;AAEA,UAAM,UAAUD,cAAAA;AAAAA,MAAS,MACvB,SAAS,MAAM,SAAS,KAAK,iBAAiB;AAAA,IAChD;AAEA,UAAM,UAAUA,uBAAS,MAAM,YAAY;AAC3C,UAAM,UAAUA,cAAAA,SAAS,MAAM;AAC7B,YAAM,SAAS,oBAAI,KAAK;AACxB,aAAO,YAAY,OAAO,YAAW,IAAK,CAAC;AAC3C,aAAOE,cAAAA,WAAW,MAAM;AAAA,KACzB;AAGD,UAAM,aAAa,CAAC,SAAS;AAC3B,kBAAY,QAAQ;AACpB,eAAS,MAAM,OAAO;AACtB,uBAAiB,QAAQ;AACzB,eAAS,MAAM,aAAa;AAC5B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,eAAe;AAC9B,eAAS,MAAM,gBAAgB;AAAA,IACjC;AAEA,UAAM,gBAAgB,CAAC,MAAM;AAC3B,UAAI,QAAQ,EAAE,OAAO;AAErB,cAAQ,MAAM,QAAQ,WAAW,EAAE;AAGnC,YAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,UAAI,MAAM,SAAS,GAAG;AACpB,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,MACjD;AAGA,UAAI,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,SAAS,GAAG;AACnC,gBAAQ,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA,MAClD;AAEA,oBAAc,QAAQ;AACtB,eAAS,MAAM,SAAS,WAAW,KAAK,KAAK;AAAA,IAC/C;AAGA,UAAM,gBAAgB,MAAM;AAC1B,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,eAAe,MAAM;AACzB,oBAAc,QAAQ;AAAA,IACxB;AAEA,UAAM,iBAAiB,CAAC,WAAW;AACjC,oBAAc,QAAQ,OAAO,SAAS;AACtC,eAAS,MAAM,SAAS;AAAA,IAC1B;AAEA,UAAM,iBAAiB,MAAM;AAE3BC,oBAAAA,UAAU,UAAU;AAAA,IACtB;AAEA,UAAM,mBAAmB,MAAM;AAC7BA,oBAAAA,UAAU,WAAW;AAAA,IACvB;AAEA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,UAAU,WAAW;AAAA,IACvB;AAEA,UAAM,iBAAiB,MAAM;AAC3B,YAAM,MAAM,oBAAI,KAAK;AACrB,eAAS,MAAM,OAAOJ,uBAAS;AAC/B,eAAS,MAAM,OAAO,IAAI,aAAY,EAAG,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC;AACrEI,oBAAAA,UAAU,UAAU;AAAA,IACtB;AAEA,UAAM,cAAc,MAAM;AAAA,IAE1B;AAEA,UAAM,aAAa,MAAM;AAAA,IAEzB;AAEA,UAAM,uBAAuB,CAAC,eAAe;AAC3C,eAAS,MAAM,OAAO;AAAA,IACxB;AAEA,UAAM,eAAe,MAAM;AACzB,UAAI,CAAC,OAAO;AAAO;AAEnBC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfH,wBAAAA,MAAM,QAAQ,aAAa,OAAO,KAAK;AACvCG,0BAAAA,MAAI,aAAa;AAAA,UACnB;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAEA,UAAM,iBAAiB,CAAC,aAAa;AACnC,uBAAiB,QAAQ;AACzB,eAAS,MAAM,aAAa,SAAS;AACrC,eAAS,MAAM,eAAe,SAAS;AACvC,eAAS,MAAM,eAAe,SAAS;AACvC,eAAS,MAAM,gBAAgB,SAAS;AAAA,IAC1C;AAEA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACb;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAE3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACb;AAAA,IACH;AAEA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,OAAO,EAAE,OAAO;AAAA,IACjC;AAEA,UAAM,eAAe,CAAC,MAAM;AAC1B,eAAS,MAAM,OAAO,EAAE,OAAO;AAAA,IACjC;AAEA,UAAM,OAAO,MAAM;AACjB,UAAI,CAAC,QAAQ,OAAO;AAClBD,sBAAAA,UAAU,SAAS;AACnB;AAAA,MACF;AAEA,UAAI;AACF,YAAI,OAAO,OAAO;AAChBF,sBAAK,MAAC,QAAQ,aAAa,OAAO,OAAO,SAAS,KAAK;AAAA,eAClD;AACLA,sBAAAA,MAAM,QAAQ,UAAU,SAAS,KAAK;AAAA,QACxC;AAGAG,sBAAAA,MAAI,aAAa;AAAA,MACjB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,4BAAA,SAAS,KAAK;AAC5BD,sBAAAA,UAAU,MAAM;AAAA,MAClB;AAAA,IACF;AAEA,UAAM,SAAS,MAAM;AACnBC,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAGA,UAAM,iBAAiB,CAAC,aAAa;AACnC,YAAM,SAASH,kBAAM,MAAM,QAAQ,KAAK,OAAK,EAAE,OAAO,QAAQ;AAC9D,UAAI,QAAQ;AACV,eAAO,QAAQ;AACf,eAAO,QAAQ;AAGf,iBAAS,QAAQ;AAAA,UACf,MAAM,OAAO;AAAA,UACb,QAAQ,OAAO;AAAA,UACf,YAAY,OAAO;AAAA,UACnB,cAAc,OAAO;AAAA,UACrB,cAAc,OAAO;AAAA,UACrB,eAAe,OAAO;AAAA,UACtB,MAAM,OAAO;AAAA,UACb,MAAM,OAAO;AAAA,UACb,MAAM,OAAO;AAAA,QACf;AAGA,oBAAY,QAAQ,OAAO;AAC3B,sBAAc,QAAQ,OAAO,OAAO,SAAS;AAG7C,cAAM,aAAaA,YAAAA,MAAM,MAAM,WAAW,OAAO,IAAI,KAAK,CAAC;AAC3D,yBAAiB,QAAQ,WAAW,KAAK,SAAO,IAAI,OAAO,OAAO,UAAU;AAAA,MAC9E;AAAA,IACF;AAGAI,kBAAAA,UAAU,MAAM;AAEdJ,kBAAK,MAAC,QAAQ,eAAe;AAG7B,YAAM,eAAeG,cAAAA,MAAI,eAAe,cAAc;AACtD,UAAI,cAAc;AAChB,mBAAW,YAAY;AAEvBA,sBAAG,MAAC,kBAAkB,cAAc;AAAA,MACtC;AAGA,YAAM,eAAeA,cAAAA,MAAI,eAAe,cAAc;AACtD,UAAI,cAAc;AAChB,uBAAe,YAAY;AAE3BA,sBAAG,MAAC,kBAAkB,cAAc;AAAA,MACtC;AAAA,KAID;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAAF,cAAS;AAAA,IACX;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrhBA,GAAG,WAAW,eAAe;"}