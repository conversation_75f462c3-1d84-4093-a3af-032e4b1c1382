/**
 * 应用测试脚本
 * 用于测试uniCloud云函数连接
 */

// 模拟uniCloud环境（仅用于测试）
if (typeof uniCloud === 'undefined') {
  global.uniCloud = {
    callFunction: async (options) => {
      console.log('模拟云函数调用:', options);
      
      // 模拟云函数响应
      if (options.name === 'init-db') {
        return {
          result: {
            code: 200,
            message: '数据库初始化成功'
          }
        };
      }
      
      if (options.name === 'bookkeeping') {
        const { action, data } = options.data;
        
        switch (action) {
          case 'getCategories':
            return {
              result: {
                code: 200,
                message: '获取成功',
                data: [
                  { id: 'exp_food', name: '餐饮', icon: '🍽️', color: '#FF6B6B', type: 'expense' },
                  { id: 'exp_transport', name: '交通', icon: '🚗', color: '#4ECDC4', type: 'expense' }
                ]
              }
            };
          
          case 'addRecord':
            return {
              result: {
                code: 200,
                message: '添加成功',
                data: { id: 'test_record_' + Date.now(), ...data }
              }
            };
          
          case 'getRecords':
            return {
              result: {
                code: 200,
                message: '获取成功',
                data: {
                  records: [],
                  total: 0,
                  page: data.page || 1,
                  limit: data.limit || 20
                }
              }
            };
          
          default:
            return {
              result: {
                code: 400,
                message: '未知操作类型'
              }
            };
        }
      }
      
      return {
        result: {
          code: 404,
          message: '云函数不存在'
        }
      };
    }
  };
}

// 测试云函数
async function testCloudFunctions() {
  console.log('🚀 开始测试云函数...\n');
  
  try {
    // 测试数据库初始化
    console.log('1. 测试数据库初始化...');
    const initResult = await uniCloud.callFunction({
      name: 'init-db',
      data: {}
    });
    console.log('   结果:', initResult.result);
    
    // 测试获取分类
    console.log('\n2. 测试获取分类...');
    const categoriesResult = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'getCategories',
        data: { type: 'expense' }
      }
    });
    console.log('   结果:', categoriesResult.result);
    
    // 测试添加记录
    console.log('\n3. 测试添加记录...');
    const addResult = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'addRecord',
        data: {
          type: 'expense',
          amount: 10.5,
          categoryId: 'exp_food',
          categoryName: '餐饮',
          note: '测试记录',
          date: new Date().toISOString().split('T')[0],
          time: new Date().toTimeString().split(' ')[0].substring(0, 5)
        }
      }
    });
    console.log('   结果:', addResult.result);
    
    // 测试获取记录
    console.log('\n4. 测试获取记录...');
    const getResult = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'getRecords',
        data: { page: 1, limit: 10 }
      }
    });
    console.log('   结果:', getResult.result);
    
    console.log('\n✅ 云函数测试完成！');
    
  } catch (error) {
    console.error('\n❌ 云函数测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testCloudFunctions();
}

module.exports = {
  testCloudFunctions
};
