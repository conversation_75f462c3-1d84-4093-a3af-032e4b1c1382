/**
 * 云函数测试工具
 */

export async function testCloudFunction() {
  console.log('开始测试云函数连接...');

  try {
    // 先测试最简单的测试云函数
    console.log('1. 测试基础云函数...');
    const testResult = await uniCloud.callFunction({
      name: 'test',
      data: { message: 'hello from client' }
    });

    console.log('基础云函数测试结果:', testResult);

    if (testResult.result && testResult.result.code === 200) {
      console.log('✅ 基础云函数调用成功');

      // 测试数据库初始化
      console.log('2. 测试数据库初始化...');
      const initResult = await uniCloud.callFunction({
        name: 'init-db',
        data: {}
      });

      console.log('数据库初始化结果:', initResult);

      if (initResult.result && initResult.result.code === 200) {
        console.log('✅ 数据库初始化成功');

        // 测试获取分类
        console.log('3. 测试获取分类...');
        const categoriesResult = await uniCloud.callFunction({
          name: 'bookkeeping',
          data: {
            action: 'getCategories',
            data: { type: 'expense' }
          }
        });

        console.log('获取分类结果:', categoriesResult);

        if (categoriesResult.result && categoriesResult.result.code === 200) {
          console.log('✅ 获取分类成功，分类数量:', categoriesResult.result.data.length);
        } else {
          console.log('❌ 获取分类失败:', categoriesResult.result ? categoriesResult.result.message : '未知错误');
        }
      } else {
        console.log('❌ 数据库初始化失败:', initResult.result ? initResult.result.message : '未知错误');
      }
    } else {
      console.log('❌ 基础云函数调用失败:', testResult.result ? testResult.result.message : '未知错误');
    }

    console.log('🎉 云函数基础测试完成');

    return {
      success: true,
      message: '云函数测试完成'
    };
    
  } catch (error) {
    console.error('❌ 云函数测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function testCloudConnection() {
  try {
    console.log('测试云函数连接...');
    
    // 简单的ping测试
    const result = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'getCategories',
        data: {}
      }
    });
    
    console.log('连接测试结果:', result);
    
    if (result.result) {
      return {
        success: true,
        message: '云函数连接正常'
      };
    } else {
      return {
        success: false,
        message: '云函数连接失败'
      };
    }
  } catch (error) {
    console.error('云函数连接测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
