/**
 * 云函数测试工具
 */

export async function testCloudFunction() {
  console.log('开始测试云函数连接...');
  
  try {
    // 测试数据库初始化
    console.log('1. 测试数据库初始化...');
    const initResult = await uniCloud.callFunction({
      name: 'init-db',
      data: {}
    });
    
    console.log('数据库初始化结果:', initResult);
    
    if (initResult.result.code === 200) {
      console.log('✅ 数据库初始化成功');
    } else {
      console.log('❌ 数据库初始化失败:', initResult.result.message);
    }
    
    // 测试获取分类
    console.log('2. 测试获取分类...');
    const categoriesResult = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'getCategories',
        data: { type: 'expense' }
      }
    });
    
    console.log('获取分类结果:', categoriesResult);
    
    if (categoriesResult.result.code === 200) {
      console.log('✅ 获取分类成功，分类数量:', categoriesResult.result.data.length);
    } else {
      console.log('❌ 获取分类失败:', categoriesResult.result.message);
    }
    
    // 测试添加记录
    console.log('3. 测试添加记录...');
    const testRecord = {
      type: 'expense',
      amount: 10.5,
      categoryId: 'exp_food',
      categoryName: '餐饮',
      categoryIcon: '🍽️',
      categoryColor: '#FF6B6B',
      note: '测试记录',
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().split(' ')[0].substring(0, 5)
    };
    
    const addResult = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'addRecord',
        data: testRecord
      }
    });
    
    console.log('添加记录结果:', addResult);
    
    if (addResult.result.code === 200) {
      console.log('✅ 添加记录成功');
      
      // 测试获取记录
      console.log('4. 测试获取记录...');
      const getResult = await uniCloud.callFunction({
        name: 'bookkeeping',
        data: {
          action: 'getRecords',
          data: { page: 1, limit: 10 }
        }
      });
      
      console.log('获取记录结果:', getResult);
      
      if (getResult.result.code === 200) {
        console.log('✅ 获取记录成功，记录数量:', getResult.result.data.records.length);
      } else {
        console.log('❌ 获取记录失败:', getResult.result.message);
      }
    } else {
      console.log('❌ 添加记录失败:', addResult.result.message);
    }
    
    console.log('🎉 云函数测试完成');
    
    return {
      success: true,
      message: '云函数测试完成'
    };
    
  } catch (error) {
    console.error('❌ 云函数测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function testCloudConnection() {
  try {
    console.log('测试云函数连接...');
    
    // 简单的ping测试
    const result = await uniCloud.callFunction({
      name: 'bookkeeping',
      data: {
        action: 'getCategories',
        data: {}
      }
    });
    
    console.log('连接测试结果:', result);
    
    if (result.result) {
      return {
        success: true,
        message: '云函数连接正常'
      };
    } else {
      return {
        success: false,
        message: '云函数连接失败'
      };
    }
  } catch (error) {
    console.error('云函数连接测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
