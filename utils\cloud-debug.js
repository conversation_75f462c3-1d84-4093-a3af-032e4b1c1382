/**
 * 云函数调试工具
 * 用于诊断云函数调用问题
 */

export default {
  // 测试基础云函数连接
  async testBasicConnection() {
    try {
      console.log('测试基础云函数连接...');
      
      const result = await uniCloud.callFunction({
        name: 'test',
        data: { message: 'debug test' }
      });
      
      console.log('基础连接测试结果:', result);
      
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : '连接失败'
      };
    } catch (error) {
      console.error('基础连接测试失败:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },

  // 测试 bookkeeping 云函数基础调用
  async testBookkeepingBasic() {
    try {
      console.log('测试 bookkeeping 云函数基础调用...');
      
      const result = await uniCloud.callFunction({
        name: 'bookkeeping',
        data: {
          action: 'getCategories',
          data: {}
        }
      });
      
      console.log('bookkeeping 基础测试结果:', result);
      
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : '调用失败'
      };
    } catch (error) {
      console.error('bookkeeping 基础测试失败:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },

  // 测试数据库初始化
  async testDatabaseInit() {
    try {
      console.log('测试数据库初始化...');
      
      const result = await uniCloud.callFunction({
        name: 'init-db',
        data: {}
      });
      
      console.log('数据库初始化测试结果:', result);
      
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : '初始化失败'
      };
    } catch (error) {
      console.error('数据库初始化测试失败:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },

  // 测试添加记录（简化版）
  async testAddRecordSimple() {
    try {
      console.log('测试添加记录（简化版）...');
      
      const testRecord = {
        type: 'expense',
        amount: 1.0,
        categoryId: 'exp_food',
        categoryName: '餐饮',
        categoryIcon: '🍽️',
        categoryColor: '#FF6B6B',
        note: '调试测试',
        date: new Date().toISOString().split('T')[0],
        time: '12:00'
      };
      
      console.log('测试记录数据:', testRecord);
      
      const result = await uniCloud.callFunction({
        name: 'bookkeeping',
        data: {
          action: 'addRecord',
          data: testRecord
        }
      });
      
      console.log('添加记录测试结果:', result);
      
      return {
        success: result.result && result.result.code === 200,
        data: result,
        message: result.result ? result.result.message : '添加失败'
      };
    } catch (error) {
      console.error('添加记录测试失败:', error);
      return {
        success: false,
        error: error.message,
        details: error
      };
    }
  },

  // 完整的诊断流程
  async runFullDiagnosis() {
    const results = {
      basicConnection: null,
      bookkeepingBasic: null,
      databaseInit: null,
      addRecord: null
    };

    console.log('开始完整诊断...');

    // 1. 基础连接测试
    results.basicConnection = await this.testBasicConnection();
    
    // 2. bookkeeping 基础测试
    results.bookkeepingBasic = await this.testBookkeepingBasic();
    
    // 3. 数据库初始化测试
    results.databaseInit = await this.testDatabaseInit();
    
    // 4. 添加记录测试
    results.addRecord = await this.testAddRecordSimple();

    console.log('完整诊断结果:', results);

    return results;
  },

  // 生成诊断报告
  generateReport(results) {
    let report = '=== 云函数诊断报告 ===\n\n';
    
    report += `1. 基础连接: ${results.basicConnection?.success ? '✅ 正常' : '❌ 失败'}\n`;
    if (!results.basicConnection?.success) {
      report += `   错误: ${results.basicConnection?.error}\n`;
    }
    
    report += `2. bookkeeping 云函数: ${results.bookkeepingBasic?.success ? '✅ 正常' : '❌ 失败'}\n`;
    if (!results.bookkeepingBasic?.success) {
      report += `   错误: ${results.bookkeepingBasic?.error}\n`;
    }
    
    report += `3. 数据库初始化: ${results.databaseInit?.success ? '✅ 正常' : '❌ 失败'}\n`;
    if (!results.databaseInit?.success) {
      report += `   错误: ${results.databaseInit?.error}\n`;
    }
    
    report += `4. 添加记录: ${results.addRecord?.success ? '✅ 正常' : '❌ 失败'}\n`;
    if (!results.addRecord?.success) {
      report += `   错误: ${results.addRecord?.error}\n`;
    }
    
    report += '\n=== 建议操作 ===\n';
    
    if (!results.basicConnection?.success) {
      report += '- 检查 uniCloud 空间配置\n';
      report += '- 确保 test 云函数已部署\n';
    }
    
    if (!results.bookkeepingBasic?.success) {
      report += '- 重新部署 bookkeeping 云函数\n';
      report += '- 检查云函数代码是否有语法错误\n';
    }
    
    if (!results.databaseInit?.success) {
      report += '- 部署 init-db 云函数\n';
      report += '- 检查数据库权限配置\n';
    }
    
    if (!results.addRecord?.success) {
      report += '- 确保数据库已初始化\n';
      report += '- 检查 generateId 函数是否存在\n';
    }
    
    return report;
  }
};
