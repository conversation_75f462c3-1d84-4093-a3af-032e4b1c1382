<template>
  <view class="ai-record-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="icon">←</text>
        </view>
        <view class="nav-title">AI智能记账</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 功能选择区域 -->
    <view class="function-selector">
      <view class="function-tabs">
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'voice' }"
          @click="switchTab('voice')"
        >
          <text class="tab-icon">🎤</text>
          <text class="tab-text">语音记账</text>
        </view>
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'image' }"
          @click="switchTab('image')"
        >
          <text class="tab-icon">📷</text>
          <text class="tab-text">拍照记账</text>
        </view>
        <view 
          class="tab-item" 
          :class="{ active: currentTab === 'text' }"
          @click="switchTab('text')"
        >
          <text class="tab-icon">✏️</text>
          <text class="tab-text">文字记账</text>
        </view>
      </view>
    </view>

    <!-- 语音记账 -->
    <view v-if="currentTab === 'voice'" class="voice-section">
      <view class="voice-container">
        <view class="voice-tips">
          <text>说出您的消费内容，AI将自动识别并填写记账信息</text>
          <text class="example">例如："今天在麦当劳花了25块钱吃午餐"</text>
        </view>
        
        <view class="voice-controls">
          <view 
            class="record-btn" 
            :class="{ recording: isRecording }"
            @touchstart="startRecording"
            @touchend="stopRecording"
          >
            <text class="record-icon">{{ isRecording ? '🔴' : '🎤' }}</text>
            <text class="record-text">
              {{ isRecording ? '松开结束' : '按住说话' }}
            </text>
          </view>
          
          <view v-if="recordingDuration > 0" class="recording-info">
            <text>录音时长: {{ formatDuration(recordingDuration) }}</text>
          </view>
        </view>

        <view v-if="voiceResult" class="voice-result">
          <view class="result-title">识别结果:</view>
          <view class="result-text">{{ voiceResult }}</view>
        </view>
      </view>
    </view>

    <!-- 图片记账 -->
    <view v-if="currentTab === 'image'" class="image-section">
      <view class="image-container">
        <view class="image-tips">
          <text>拍摄或选择发票、收据等图片，AI将自动识别消费信息</text>
        </view>
        
        <view class="image-controls">
          <view class="image-btn" @click="takePhoto">
            <text class="btn-icon">📷</text>
            <text class="btn-text">拍照</text>
          </view>
          <view class="image-btn" @click="chooseFromAlbum">
            <text class="btn-icon">🖼️</text>
            <text class="btn-text">从相册选择</text>
          </view>
        </view>

        <view v-if="selectedImage" class="image-preview">
          <image 
            :src="selectedImage" 
            class="preview-image"
            @click="previewImage"
          />
          <view class="image-actions">
            <text class="action-btn" @click="removeImage">删除</text>
            <text class="action-btn" @click="analyzeImage">分析</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 文字记账 -->
    <view v-if="currentTab === 'text'" class="text-section">
      <view class="text-container">
        <view class="text-tips">
          <text>输入消费描述，AI将智能识别并填写记账信息</text>
        </view>
        
        <view class="text-input-area">
          <textarea 
            v-model="textInput"
            class="text-input"
            placeholder="请描述您的消费情况，例如：今天在星巴克买咖啡花了35元"
            maxlength="200"
            @input="onTextInput"
          />
          <view class="input-counter">{{ textInput.length }}/200</view>
        </view>

        <view class="text-controls">
          <button 
            class="analyze-btn" 
            :disabled="!textInput.trim()"
            @click="analyzeText"
          >
            AI分析
          </button>
        </view>
      </view>
    </view>

    <!-- AI处理状态 -->
    <view v-if="isProcessing" class="processing-overlay">
      <view class="processing-content">
        <view class="loading-spinner"></view>
        <text class="processing-text">{{ processingText }}</text>
      </view>
    </view>

    <!-- AI分析结果 -->
    <view v-if="aiResult" class="result-section">
      <view class="result-header">
        <text class="result-title">AI识别结果</text>
        <text class="confidence">置信度: {{ (aiResult.confidence * 100).toFixed(0) }}%</text>
      </view>

      <view class="result-form">
        <!-- 收支类型 -->
        <view class="form-row">
          <text class="form-label">类型</text>
          <view class="type-selector">
            <view 
              class="type-option" 
              :class="{ active: aiResult.type === 'expense' }"
              @click="aiResult.type = 'expense'"
            >
              <text class="type-icon">💸</text>
              <text>支出</text>
            </view>
            <view 
              class="type-option" 
              :class="{ active: aiResult.type === 'income' }"
              @click="aiResult.type = 'income'"
            >
              <text class="type-icon">💰</text>
              <text>收入</text>
            </view>
          </view>
        </view>

        <!-- 金额 -->
        <view class="form-row">
          <text class="form-label">金额</text>
          <input 
            v-model="aiResult.amount"
            class="form-input"
            type="digit"
            placeholder="0.00"
          />
        </view>

        <!-- 分类 -->
        <view class="form-row">
          <text class="form-label">分类</text>
          <view class="category-display" @click="showCategoryPicker">
            <text class="category-icon">{{ aiResult.categoryIcon }}</text>
            <text class="category-name">{{ aiResult.categoryName }}</text>
            <text class="arrow">></text>
          </view>
        </view>

        <!-- 备注 -->
        <view class="form-row">
          <text class="form-label">备注</text>
          <input 
            v-model="aiResult.note"
            class="form-input"
            placeholder="添加备注"
          />
        </view>

        <!-- 商家 -->
        <view v-if="aiResult.merchant" class="form-row">
          <text class="form-label">商家</text>
          <input 
            v-model="aiResult.merchant"
            class="form-input"
            placeholder="商家名称"
          />
        </view>
      </view>

      <view class="result-actions">
        <button class="cancel-btn" @click="clearResult">重新识别</button>
        <button class="save-btn" @click="saveRecord">保存记录</button>
      </view>
    </view>
  </view>
</template>

<script>
import AIService from '../../utils/ai-service.js'
import SpeechService from '../../utils/speech-service.js'
import ImageService from '../../utils/image-service.js'
import CloudStorage from '../../utils/cloud-storage.js'

export default {
  data() {
    return {
      currentTab: 'voice', // voice, image, text
      
      // 语音相关
      isRecording: false,
      recordingDuration: 0,
      recordingTimer: null,
      voiceResult: '',
      
      // 图片相关
      selectedImage: '',
      selectedImageBase64: '',
      
      // 文字相关
      textInput: '',
      
      // AI处理
      isProcessing: false,
      processingText: 'AI正在分析中...',
      aiResult: null
    }
  },

  onLoad() {
    this.checkAIConfig()
  },

  onUnload() {
    this.cleanup()
  },

  methods: {
    // 检查AI配置
    checkAIConfig() {
      if (!AIService.isConfigValid()) {
        uni.showModal({
          title: '配置提醒',
          content: '请先配置阿里云百炼API密钥才能使用AI功能',
          showCancel: false
        })
      }
    },

    // 切换功能标签
    switchTab(tab) {
      this.currentTab = tab
      this.clearResult()
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 开始录音
    async startRecording() {
      const result = await SpeechService.startRecording()
      if (result.success) {
        this.isRecording = true
        this.startRecordingTimer()
      } else {
        uni.showToast({
          title: result.error,
          icon: 'none'
        })
      }
    },

    // 停止录音
    async stopRecording() {
      if (!this.isRecording) return
      
      this.stopRecordingTimer()
      const result = await SpeechService.stopRecording()
      
      if (result.success) {
        this.processVoiceRecord(result.data.tempFilePath)
      } else {
        uni.showToast({
          title: result.error,
          icon: 'none'
        })
      }
    },

    // 处理语音记录
    async processVoiceRecord(filePath) {
      this.isProcessing = true
      this.processingText = '正在识别语音...'
      
      try {
        // 语音转文字
        const speechResult = await SpeechService.speechToText(filePath)
        
        if (speechResult.success) {
          this.voiceResult = speechResult.data.text
          this.processingText = 'AI正在分析语音内容...'
          
          // AI分析文字内容
          await this.analyzeTextContent(speechResult.data.text)
        } else {
          throw new Error(speechResult.error)
        }
      } catch (error) {
        console.error('语音处理失败:', error)
        uni.showToast({
          title: '语音识别失败',
          icon: 'none'
        })
      } finally {
        this.isProcessing = false
      }
    },

    // 拍照
    async takePhoto() {
      const result = await ImageService.takePhoto()
      if (result.success) {
        this.selectedImage = result.data.tempFilePath
        this.selectedImageBase64 = result.data.base64
      } else {
        uni.showToast({
          title: result.error,
          icon: 'none'
        })
      }
    },

    // 从相册选择
    async chooseFromAlbum() {
      const result = await ImageService.chooseFromAlbum()
      if (result.success) {
        this.selectedImage = result.data.tempFilePath
        this.selectedImageBase64 = result.data.base64
      } else {
        uni.showToast({
          title: result.error,
          icon: 'none'
        })
      }
    },

    // 预览图片
    previewImage() {
      ImageService.previewImage([this.selectedImage])
    },

    // 删除图片
    removeImage() {
      this.selectedImage = ''
      this.selectedImageBase64 = ''
    },

    // 分析图片
    async analyzeImage() {
      if (!this.selectedImageBase64) {
        uni.showToast({
          title: '请先选择图片',
          icon: 'none'
        })
        return
      }

      this.isProcessing = true
      this.processingText = 'AI正在分析图片...'
      
      try {
        const result = await AIService.analyzeImageForBookkeeping(this.selectedImageBase64)
        
        if (result.success) {
          this.aiResult = result.data
        } else {
          throw new Error(result.error)
        }
      } catch (error) {
        console.error('图片分析失败:', error)
        uni.showToast({
          title: '图片分析失败',
          icon: 'none'
        })
      } finally {
        this.isProcessing = false
      }
    },

    // 文字输入
    onTextInput(e) {
      this.textInput = e.detail.value
    },

    // 分析文字
    async analyzeText() {
      if (!this.textInput.trim()) {
        uni.showToast({
          title: '请输入描述内容',
          icon: 'none'
        })
        return
      }

      await this.analyzeTextContent(this.textInput)
    },

    // 分析文字内容
    async analyzeTextContent(text) {
      this.isProcessing = true
      this.processingText = 'AI正在分析文字内容...'
      
      try {
        const result = await AIService.analyzeTextForBookkeeping(text)
        
        if (result.success) {
          this.aiResult = result.data
        } else {
          throw new Error(result.error)
        }
      } catch (error) {
        console.error('文字分析失败:', error)
        uni.showToast({
          title: '文字分析失败',
          icon: 'none'
        })
      } finally {
        this.isProcessing = false
      }
    },

    // 显示分类选择器
    showCategoryPicker() {
      // TODO: 实现分类选择器
      uni.showToast({
        title: '分类选择功能开发中',
        icon: 'none'
      })
    },

    // 保存记录
    async saveRecord() {
      if (!this.aiResult || !this.aiResult.amount) {
        uni.showToast({
          title: '请完善记录信息',
          icon: 'none'
        })
        return
      }

      try {
        const result = await CloudStorage.addRecord(this.aiResult)
        
        if (result.success) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(result.error)
        }
      } catch (error) {
        console.error('保存记录失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    },

    // 清除结果
    clearResult() {
      this.aiResult = null
      this.voiceResult = ''
      this.selectedImage = ''
      this.selectedImageBase64 = ''
      this.textInput = ''
    },

    // 开始录音计时
    startRecordingTimer() {
      this.recordingDuration = 0
      this.recordingTimer = setInterval(() => {
        this.recordingDuration += 100
      }, 100)
    },

    // 停止录音计时
    stopRecordingTimer() {
      if (this.recordingTimer) {
        clearInterval(this.recordingTimer)
        this.recordingTimer = null
      }
    },

    // 格式化时长
    formatDuration(ms) {
      const seconds = Math.floor(ms / 1000)
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 清理资源
    cleanup() {
      this.stopRecordingTimer()
      SpeechService.destroy()
    }
  }
}
</script>

<style scoped>
.ai-record-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 20px;
}

/* 顶部导航 */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
  padding-top: var(--status-bar-height);
}

.nav-left, .nav-right {
  width: 60px;
}

.nav-left .icon {
  font-size: 20px;
  color: white;
  font-weight: bold;
}

.nav-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

/* 功能选择器 */
.function-selector {
  padding: 20px 15px;
}

.function-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 4px;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.tab-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.tab-text {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 语音记账区域 */
.voice-section {
  padding: 0 15px;
}

.voice-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
}

.voice-tips {
  text-align: center;
  margin-bottom: 20px;
}

.voice-tips text {
  display: block;
  color: #666;
  line-height: 1.5;
}

.example {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.voice-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.record-btn {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.record-btn.recording {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  transform: scale(1.1);
  box-shadow: 0 12px 30px rgba(231, 76, 60, 0.4);
}

.record-icon {
  font-size: 32px;
  margin-bottom: 4px;
}

.record-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.recording-info {
  margin-top: 15px;
  padding: 8px 16px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 20px;
}

.recording-info text {
  color: #e74c3c;
  font-size: 14px;
}

.voice-result {
  margin-top: 20px;
  padding: 15px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.result-title {
  color: #667eea;
  font-weight: 600;
  margin-bottom: 8px;
}

.result-text {
  color: #333;
  line-height: 1.5;
}

/* 图片记账区域 */
.image-section {
  padding: 0 15px;
}

.image-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
}

.image-tips {
  text-align: center;
  margin-bottom: 20px;
  color: #666;
  line-height: 1.5;
}

.image-controls {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.image-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  border-radius: 12px;
  min-width: 100px;
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
}

.btn-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.btn-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.image-preview {
  text-align: center;
}

.preview-image {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  object-fit: cover;
  margin-bottom: 15px;
}

.image-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.action-btn {
  padding: 8px 20px;
  background: #667eea;
  color: white;
  border-radius: 20px;
  font-size: 14px;
}

/* 文字记账区域 */
.text-section {
  padding: 0 15px;
}

.text-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
}

.text-tips {
  text-align: center;
  margin-bottom: 20px;
  color: #666;
  line-height: 1.5;
}

.text-input-area {
  position: relative;
  margin-bottom: 20px;
}

.text-input {
  width: 100%;
  min-height: 120px;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  line-height: 1.5;
  resize: none;
}

.text-input:focus {
  border-color: #667eea;
  outline: none;
}

.input-counter {
  position: absolute;
  bottom: 10px;
  right: 15px;
  color: #999;
  font-size: 12px;
}

.text-controls {
  text-align: center;
}

.analyze-btn {
  background: linear-gradient(135deg, #00b894, #00a085);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
}

.analyze-btn:disabled {
  background: #ccc;
}

/* 处理状态覆盖层 */
.processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.processing-content {
  background: white;
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  min-width: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text {
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

/* AI结果区域 */
.result-section {
  margin: 0 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.confidence {
  font-size: 14px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.result-form {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px 0;
}

.form-label {
  width: 60px;
  color: #666;
  font-weight: 500;
  margin-right: 15px;
}

.form-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
}

.form-input:focus {
  border-color: #667eea;
  outline: none;
}

.type-selector {
  display: flex;
  gap: 10px;
}

.type-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.type-option.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.type-icon {
  font-size: 16px;
}

.category-display {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f9fa;
}

.category-icon {
  font-size: 18px;
  margin-right: 10px;
}

.category-name {
  flex: 1;
  color: #333;
}

.arrow {
  color: #999;
  font-size: 14px;
}

.result-actions {
  display: flex;
  gap: 15px;
}

.cancel-btn, .save-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e0e0e0;
}

.save-btn {
  background: linear-gradient(135deg, #00b894, #00a085);
  color: white;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .function-tabs {
    padding: 2px;
  }

  .tab-item {
    padding: 8px 4px;
  }

  .tab-text {
    font-size: 11px;
  }

  .record-btn {
    width: 100px;
    height: 100px;
  }

  .record-icon {
    font-size: 28px;
  }

  .image-controls {
    flex-direction: column;
    gap: 15px;
  }

  .image-btn {
    width: 100%;
  }
}
</style>
