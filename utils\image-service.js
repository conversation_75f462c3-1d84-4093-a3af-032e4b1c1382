/**
 * 图片识别服务模块
 * 处理图片选择、拍照、压缩和base64转换
 */

class ImageService {
  constructor() {
    this.maxImageSize = 5 * 1024 * 1024; // 最大图片大小5MB
    this.compressQuality = 0.8; // 压缩质量
    this.maxWidth = 1920; // 最大宽度
    this.maxHeight = 1920; // 最大高度
  }

  /**
   * 选择图片 (从相册或拍照)
   * @param {Object} options - 选择选项
   * @returns {Promise<Object>} 选择结果
   */
  async chooseImage(options = {}) {
    return new Promise((resolve) => {
      const defaultOptions = {
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        ...options
      };

      uni.chooseImage({
        ...defaultOptions,
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0];
            
            // 检查图片大小
            const fileInfo = await this.getFileInfo(tempFilePath);
            if (fileInfo.size > this.maxImageSize) {
              resolve({
                success: false,
                error: '图片大小超过限制(5MB)'
              });
              return;
            }

            // 压缩图片
            const compressedPath = await this.compressImage(tempFilePath);
            
            // 转换为base64
            const base64 = await this.imageToBase64(compressedPath);
            
            resolve({
              success: true,
              data: {
                tempFilePath: compressedPath,
                base64: base64,
                originalPath: tempFilePath,
                size: fileInfo.size
              }
            });
          } catch (error) {
            console.error('处理图片失败:', error);
            resolve({
              success: false,
              error: error.message
            });
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error);
          resolve({
            success: false,
            error: error.errMsg || '选择图片失败'
          });
        }
      });
    });
  }

  /**
   * 拍照
   * @param {Object} options - 拍照选项
   * @returns {Promise<Object>} 拍照结果
   */
  async takePhoto(options = {}) {
    return this.chooseImage({
      count: 1,
      sourceType: ['camera'],
      ...options
    });
  }

  /**
   * 从相册选择
   * @param {Object} options - 选择选项
   * @returns {Promise<Object>} 选择结果
   */
  async chooseFromAlbum(options = {}) {
    return this.chooseImage({
      count: 1,
      sourceType: ['album'],
      ...options
    });
  }

  /**
   * 获取文件信息
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 文件信息
   */
  async getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      uni.getFileInfo({
        filePath: filePath,
        success: (res) => {
          resolve({
            size: res.size,
            digest: res.digest
          });
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '获取文件信息失败'));
        }
      });
    });
  }

  /**
   * 压缩图片
   * @param {string} filePath - 原始图片路径
   * @returns {Promise<string>} 压缩后的图片路径
   */
  async compressImage(filePath) {
    return new Promise((resolve, reject) => {
      uni.compressImage({
        src: filePath,
        quality: this.compressQuality,
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (error) => {
          console.warn('图片压缩失败，使用原图:', error);
          // 如果压缩失败，返回原图路径
          resolve(filePath);
        }
      });
    });
  }

  /**
   * 图片转base64
   * @param {string} filePath - 图片文件路径
   * @returns {Promise<string>} base64字符串
   */
  async imageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      uni.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '转换base64失败'));
        }
      });
    });
  }

  /**
   * 预览图片
   * @param {Array|string} urls - 图片URL数组或单个URL
   * @param {number} current - 当前显示图片的索引
   */
  previewImage(urls, current = 0) {
    const urlArray = Array.isArray(urls) ? urls : [urls];
    
    uni.previewImage({
      urls: urlArray,
      current: current,
      fail: (error) => {
        console.error('预览图片失败:', error);
        uni.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  }

  /**
   * 保存图片到相册
   * @param {string} filePath - 图片文件路径
   * @returns {Promise<Object>} 保存结果
   */
  async saveImageToPhotosAlbum(filePath) {
    return new Promise((resolve) => {
      // 检查保存图片权限
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.writePhotosAlbum'] === false) {
            // 用户拒绝了权限
            uni.showModal({
              title: '需要相册权限',
              content: '保存图片需要相册权限，请在设置中开启',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting();
                }
                resolve({
                  success: false,
                  error: '相册权限未开启'
                });
              }
            });
          } else {
            // 有权限或首次请求
            uni.saveImageToPhotosAlbum({
              filePath: filePath,
              success: () => {
                resolve({
                  success: true,
                  message: '保存成功'
                });
              },
              fail: (error) => {
                if (error.errMsg.includes('auth')) {
                  // 权限问题
                  uni.authorize({
                    scope: 'scope.writePhotosAlbum',
                    success: () => {
                      // 重新尝试保存
                      this.saveImageToPhotosAlbum(filePath).then(resolve);
                    },
                    fail: () => {
                      resolve({
                        success: false,
                        error: '相册权限被拒绝'
                      });
                    }
                  });
                } else {
                  resolve({
                    success: false,
                    error: error.errMsg || '保存失败'
                  });
                }
              }
            });
          }
        }
      });
    });
  }

  /**
   * 获取图片信息
   * @param {string} src - 图片路径
   * @returns {Promise<Object>} 图片信息
   */
  async getImageInfo(src) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src: src,
        success: (res) => {
          resolve({
            width: res.width,
            height: res.height,
            path: res.path,
            orientation: res.orientation,
            type: res.type
          });
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '获取图片信息失败'));
        }
      });
    });
  }

  /**
   * 裁剪图片
   * @param {string} src - 原图片路径
   * @param {Object} cropOptions - 裁剪选项
   * @returns {Promise<Object>} 裁剪结果
   */
  async cropImage(src, cropOptions = {}) {
    const defaultOptions = {
      width: 300,
      height: 300,
      ...cropOptions
    };

    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      uni.cropImage({
        src: src,
        cropScale: '1:1',
        success: (res) => {
          resolve({
            success: true,
            data: {
              tempFilePath: res.tempFilePath
            }
          });
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '裁剪失败'
          });
        }
      });
      // #endif

      // #ifndef MP-WEIXIN
      // 其他平台暂不支持裁剪，返回原图
      resolve({
        success: true,
        data: {
          tempFilePath: src
        }
      });
      // #endif
    });
  }

  /**
   * 批量处理图片
   * @param {Array} filePaths - 图片路径数组
   * @returns {Promise<Array>} 处理结果数组
   */
  async batchProcessImages(filePaths) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const compressedPath = await this.compressImage(filePath);
        const base64 = await this.imageToBase64(compressedPath);
        
        results.push({
          success: true,
          originalPath: filePath,
          compressedPath: compressedPath,
          base64: base64
        });
      } catch (error) {
        results.push({
          success: false,
          originalPath: filePath,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 清理临时文件
   * @param {Array} filePaths - 要清理的文件路径数组
   */
  cleanupTempFiles(filePaths) {
    const fileManager = uni.getFileSystemManager();
    
    filePaths.forEach(filePath => {
      try {
        fileManager.unlinkSync(filePath);
        console.log('清理临时文件:', filePath);
      } catch (error) {
        console.warn('清理临时文件失败:', filePath, error);
      }
    });
  }
}

export default new ImageService();
