{"version": 3, "file": "settings.js", "sources": ["pages/settings/settings.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2V0dGluZ3Mvc2V0dGluZ3MudnVl"], "sourcesContent": ["<template>\n  <view class=\"settings-page\">\n    <!-- 用户信息卡片 -->\n    <view class=\"user-card\">\n      <view class=\"user-avatar\">\n        <text class=\"avatar-text\">记</text>\n      </view>\n      <view class=\"user-info\">\n        <text class=\"user-name\">记账本用户</text>\n        <text class=\"user-desc\">让记账变得简单</text>\n      </view>\n    </view>\n\n    <!-- 统计概览 -->\n    <view class=\"stats-overview\">\n      <view class=\"stats-item\">\n        <text class=\"stats-number\">{{ totalRecords }}</text>\n        <text class=\"stats-label\">总记录</text>\n      </view>\n      <view class=\"stats-item\">\n        <text class=\"stats-number\">{{ totalDays }}</text>\n        <text class=\"stats-label\">记账天数</text>\n      </view>\n      <view class=\"stats-item\">\n        <text class=\"stats-number\">{{ formatAmount(totalAmount) }}</text>\n        <text class=\"stats-label\">累计金额</text>\n      </view>\n    </view>\n\n    <!-- 设置选项 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">基本设置</view>\n      \n      <view class=\"setting-item\" @click=\"showCurrencyPicker\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">💰</text>\n          <text class=\"setting-name\">货币符号</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-value\">{{ settings.currency }}</text>\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\" @click=\"showBudgetSetting\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">🎯</text>\n          <text class=\"setting-name\">月度预算</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-value\">{{ formatAmount(settings.monthlyBudget) }}</text>\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">🔔</text>\n          <text class=\"setting-name\">预算提醒</text>\n        </view>\n        <view class=\"setting-right\">\n          <switch \n            :checked=\"settings.budgetAlert\" \n            @change=\"onBudgetAlertChange\"\n            color=\"#4CAF50\"\n          />\n        </view>\n      </view>\n    </view>\n\n    <!-- 分类管理 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">分类管理</view>\n      \n      <view class=\"setting-item\" @click=\"manageCategoriesExpense\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">💸</text>\n          <text class=\"setting-name\">支出分类</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-value\">{{ expenseCategories.length }}个</text>\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\" @click=\"manageCategoriesIncome\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">💰</text>\n          <text class=\"setting-name\">收入分类</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-value\">{{ incomeCategories.length }}个</text>\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 数据管理 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">数据管理</view>\n      \n      <view class=\"setting-item\" @click=\"exportData\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">📤</text>\n          <text class=\"setting-name\">导出数据</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\" @click=\"importData\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">📥</text>\n          <text class=\"setting-name\">导入数据</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\" @click=\"clearData\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">🗑️</text>\n          <text class=\"setting-name\">清空数据</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 关于 -->\n    <view class=\"settings-section\">\n      <view class=\"section-title\">关于</view>\n      \n      <view class=\"setting-item\" @click=\"showAbout\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">ℹ️</text>\n          <text class=\"setting-name\">关于记账本</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-value\">v1.0.0</text>\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n\n      <view class=\"setting-item\" @click=\"showHelp\">\n        <view class=\"setting-left\">\n          <text class=\"setting-icon\">❓</text>\n          <text class=\"setting-name\">使用帮助</text>\n        </view>\n        <view class=\"setting-right\">\n          <text class=\"setting-arrow\">></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport store from '../../store/index.js'\nimport { formatAmount, showToast } from '../../utils/helpers.js'\n\nexport default {\n  name: 'SettingsPage',\n  setup() {\n    const settings = computed(() => store.state.settings)\n    const expenseCategories = computed(() => store.state.categories.expense)\n    const incomeCategories = computed(() => store.state.categories.income)\n\n    // 统计数据\n    const totalRecords = computed(() => store.state.records.length)\n    \n    const totalDays = computed(() => {\n      const dates = [...new Set(store.state.records.map(record => record.date))]\n      return dates.length\n    })\n    \n    const totalAmount = computed(() => \n      store.state.records.reduce((total, record) => total + record.amount, 0)\n    )\n\n    // 方法\n    const showCurrencyPicker = () => {\n      const currencies = ['¥', '$', '€', '£', '₹', '₩']\n      uni.showActionSheet({\n        itemList: currencies,\n        success: (res) => {\n          const newCurrency = currencies[res.tapIndex]\n          store.actions.updateSettings({ currency: newCurrency })\n        }\n      })\n    }\n\n    const showBudgetSetting = () => {\n      uni.showModal({\n        title: '设置月度预算',\n        editable: true,\n        placeholderText: '请输入预算金额',\n        success: (res) => {\n          if (res.confirm && res.content) {\n            const budget = parseFloat(res.content) || 0\n            store.actions.updateSettings({ monthlyBudget: budget })\n          }\n        }\n      })\n    }\n\n    const onBudgetAlertChange = (e) => {\n      store.actions.updateSettings({ budgetAlert: e.detail.value })\n    }\n\n    const manageCategoriesExpense = () => {\n      showToast('分类管理功能开发中')\n    }\n\n    const manageCategoriesIncome = () => {\n      showToast('分类管理功能开发中')\n    }\n\n    const exportData = () => {\n      try {\n        const data = {\n          records: store.state.records,\n          categories: store.state.categories,\n          settings: store.state.settings,\n          exportTime: new Date().toISOString()\n        }\n        \n        // 在实际应用中，这里应该生成文件并提供下载\n        console.log('导出数据:', data)\n        showToast('数据导出成功', 'success')\n      } catch (error) {\n        console.error('导出失败:', error)\n        showToast('数据导出失败')\n      }\n    }\n\n    const importData = () => {\n      showToast('数据导入功能开发中')\n    }\n\n    const clearData = () => {\n      uni.showModal({\n        title: '确认清空',\n        content: '此操作将清空所有记账数据，且无法恢复，确定要继续吗？',\n        confirmColor: '#F44336',\n        success: (res) => {\n          if (res.confirm) {\n            try {\n              // 清空记录\n              store.state.records = []\n              store.actions.saveRecords()\n              \n              // 重置分类为默认\n              store.state.categories = store.actions.getDefaultCategories()\n              store.actions.saveCategories()\n              \n              showToast('数据清空成功', 'success')\n            } catch (error) {\n              console.error('清空失败:', error)\n              showToast('数据清空失败')\n            }\n          }\n        }\n      })\n    }\n\n    const showAbout = () => {\n      uni.showModal({\n        title: '关于记账本',\n        content: '记账本 v1.0.0\\n\\n一款简洁易用的个人记账小程序\\n\\n功能特色：\\n• 快速记账\\n• 分类管理\\n• 统计分析\\n• 数据导出',\n        showCancel: false,\n        confirmText: '知道了'\n      })\n    }\n\n    const showHelp = () => {\n      uni.showModal({\n        title: '使用帮助',\n        content: '1. 点击首页的快速记账按钮开始记账\\n2. 在记账页面选择分类和输入金额\\n3. 在账单页面查看所有记录\\n4. 在统计页面查看收支分析\\n5. 在设置页面管理分类和导出数据',\n        showCancel: false,\n        confirmText: '知道了'\n      })\n    }\n\n    onMounted(() => {\n      store.actions.loadSettings()\n      store.actions.loadCategories()\n      store.actions.loadRecords()\n    })\n\n    return {\n      settings,\n      expenseCategories,\n      incomeCategories,\n      totalRecords,\n      totalDays,\n      totalAmount,\n      formatAmount,\n      showCurrencyPicker,\n      showBudgetSetting,\n      onBudgetAlertChange,\n      manageCategoriesExpense,\n      manageCategoriesIncome,\n      exportData,\n      importData,\n      clearData,\n      showAbout,\n      showHelp\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.settings-page {\n  background-color: $uni-bg-color-grey;\n  min-height: 100vh;\n  padding: 16px;\n}\n\n/* 用户信息卡片 */\n.user-card {\n  display: flex;\n  align-items: center;\n  background-color: white;\n  padding: 24px;\n  border-radius: 12px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 30px;\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.avatar-text {\n  font-size: 24px;\n  font-weight: bold;\n  color: white;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 18px;\n  font-weight: 600;\n  color: $uni-text-color;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.user-desc {\n  font-size: 14px;\n  color: $uni-text-color-grey;\n}\n\n/* 统计概览 */\n.stats-overview {\n  display: flex;\n  background-color: white;\n  border-radius: 12px;\n  margin-bottom: 24px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.stats-item {\n  flex: 1;\n  padding: 20px;\n  text-align: center;\n  border-right: 1px solid $uni-border-color;\n\n  &:last-child {\n    border-right: none;\n  }\n}\n\n.stats-number {\n  font-size: 20px;\n  font-weight: bold;\n  color: $uni-color-primary;\n  display: block;\n  margin-bottom: 8px;\n}\n\n.stats-label {\n  font-size: 12px;\n  color: $uni-text-color-grey;\n}\n\n/* 设置区域 */\n.settings-section {\n  margin-bottom: 24px;\n}\n\n.section-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: $uni-text-color-grey;\n  margin-bottom: 12px;\n  padding: 0 4px;\n}\n\n.setting-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background-color: white;\n  padding: 16px 20px;\n  border-bottom: 1px solid $uni-border-color;\n  transition: background-color 0.3s ease;\n\n  &:first-child {\n    border-radius: 12px 12px 0 0;\n  }\n\n  &:last-child {\n    border-bottom: none;\n    border-radius: 0 0 12px 12px;\n  }\n\n  &:only-child {\n    border-radius: 12px;\n  }\n\n  &:active {\n    background-color: $uni-bg-color-hover;\n  }\n}\n\n.setting-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.setting-icon {\n  font-size: 20px;\n  margin-right: 12px;\n}\n\n.setting-name {\n  font-size: 16px;\n  color: $uni-text-color;\n}\n\n.setting-right {\n  display: flex;\n  align-items: center;\n}\n\n.setting-value {\n  font-size: 14px;\n  color: $uni-text-color-grey;\n  margin-right: 8px;\n}\n\n.setting-arrow {\n  font-size: 12px;\n  color: $uni-text-color-placeholder;\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/settings/settings.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "store", "uni", "showToast", "onMounted", "formatAmount"], "mappings": ";;;;AAqKA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AACN,UAAM,WAAWA,cAAQ,SAAC,MAAMC,YAAAA,MAAM,MAAM,QAAQ;AACpD,UAAM,oBAAoBD,cAAAA,SAAS,MAAMC,YAAK,MAAC,MAAM,WAAW,OAAO;AACvE,UAAM,mBAAmBD,cAAAA,SAAS,MAAMC,YAAK,MAAC,MAAM,WAAW,MAAM;AAGrE,UAAM,eAAeD,cAAAA,SAAS,MAAMC,YAAK,MAAC,MAAM,QAAQ,MAAM;AAE9D,UAAM,YAAYD,cAAAA,SAAS,MAAM;AAC/B,YAAM,QAAQ,CAAC,GAAG,IAAI,IAAIC,YAAAA,MAAM,MAAM,QAAQ,IAAI,YAAU,OAAO,IAAI,CAAC,CAAC;AACzE,aAAO,MAAM;AAAA,KACd;AAED,UAAM,cAAcD,cAAAA;AAAAA,MAAS,MAC3BC,YAAAA,MAAM,MAAM,QAAQ,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACxE;AAGA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAChDC,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,cAAc,WAAW,IAAI,QAAQ;AAC3CD,sBAAAA,MAAM,QAAQ,eAAe,EAAE,UAAU,YAAU,CAAG;AAAA,QACxD;AAAA,OACD;AAAA,IACH;AAEA,UAAM,oBAAoB,MAAM;AAC9BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,WAAW,IAAI,SAAS;AAC9B,kBAAM,SAAS,WAAW,IAAI,OAAO,KAAK;AAC1CD,wBAAAA,MAAM,QAAQ,eAAe,EAAE,eAAe,OAAK,CAAG;AAAA,UACxD;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAEA,UAAM,sBAAsB,CAAC,MAAM;AACjCA,kBAAK,MAAC,QAAQ,eAAe,EAAE,aAAa,EAAE,OAAO,OAAO;AAAA,IAC9D;AAEA,UAAM,0BAA0B,MAAM;AACpCE,oBAAAA,UAAU,WAAW;AAAA,IACvB;AAEA,UAAM,yBAAyB,MAAM;AACnCA,oBAAAA,UAAU,WAAW;AAAA,IACvB;AAEA,UAAM,aAAa,MAAM;AACvB,UAAI;AACF,cAAM,OAAO;AAAA,UACX,SAASF,YAAAA,MAAM,MAAM;AAAA,UACrB,YAAYA,YAAAA,MAAM,MAAM;AAAA,UACxB,UAAUA,YAAAA,MAAM,MAAM;AAAA,UACtB,aAAY,oBAAI,KAAM,GAAC,YAAY;AAAA,QACrC;AAGAC,sBAAAA,yDAAY,SAAS,IAAI;AACzBC,sBAAS,UAAC,UAAU,SAAS;AAAA,MAC7B,SAAO,OAAO;AACdD,sBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,KAAK;AAC5BC,sBAAAA,UAAU,QAAQ;AAAA,MACpB;AAAA,IACF;AAEA,UAAM,aAAa,MAAM;AACvBA,oBAAAA,UAAU,WAAW;AAAA,IACvB;AAEA,UAAM,YAAY,MAAM;AACtBD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,gBAAI;AAEFD,gCAAM,MAAM,UAAU,CAAC;AACvBA,0BAAK,MAAC,QAAQ,YAAY;AAG1BA,0BAAAA,MAAM,MAAM,aAAaA,YAAAA,MAAM,QAAQ,qBAAqB;AAC5DA,0BAAK,MAAC,QAAQ,eAAe;AAE7BE,4BAAS,UAAC,UAAU,SAAS;AAAA,YAC7B,SAAO,OAAO;AACdD,4BAAAA,MAAA,MAAA,SAAA,sCAAc,SAAS,KAAK;AAC5BC,4BAAAA,UAAU,QAAQ;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AACtBD,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,OACd;AAAA,IACH;AAEA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,OACd;AAAA,IACH;AAEAE,kBAAAA,UAAU,MAAM;AACdH,kBAAK,MAAC,QAAQ,aAAa;AAC3BA,kBAAK,MAAC,QAAQ,eAAe;AAC7BA,kBAAK,MAAC,QAAQ,YAAY;AAAA,KAC3B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,oBACAI,cAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;ACzTA,GAAG,WAAW,eAAe;"}