{"version": 3, "file": "list.js", "sources": ["pages/list/list.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbGlzdC9saXN0LnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"list-page\">\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <view class=\"filter-item\" @click=\"showTypeFilter\">\n        <text class=\"filter-text\">{{ typeFilterText }}</text>\n        <text class=\"filter-arrow\">▼</text>\n      </view>\n      \n      <view class=\"filter-item\" @click=\"showDateFilter\">\n        <text class=\"filter-text\">{{ dateFilterText }}</text>\n        <text class=\"filter-arrow\">▼</text>\n      </view>\n      \n      <view class=\"filter-item\" @click=\"showCategoryFilter\">\n        <text class=\"filter-text\">{{ categoryFilterText }}</text>\n        <text class=\"filter-arrow\">▼</text>\n      </view>\n    </view>\n\n    <!-- 统计信息 -->\n    <view class=\"summary-card\" v-if=\"filteredRecords.length > 0\">\n      <view class=\"summary-item\">\n        <text class=\"summary-label\">收入</text>\n        <text class=\"summary-value income\">{{ formatAmount(summaryIncome) }}</text>\n      </view>\n      <view class=\"summary-item\">\n        <text class=\"summary-label\">支出</text>\n        <text class=\"summary-value expense\">{{ formatAmount(summaryExpense) }}</text>\n      </view>\n      <view class=\"summary-item\">\n        <text class=\"summary-label\">结余</text>\n        <text class=\"summary-value\" :class=\"{ 'negative': summaryBalance < 0 }\">\n          {{ formatAmount(summaryBalance) }}\n        </text>\n      </view>\n    </view>\n\n    <!-- 记录列表 -->\n    <view class=\"record-list\" v-if=\"groupedRecords.length > 0\">\n      <view class=\"date-group\" v-for=\"group in groupedRecords\" :key=\"group.date\">\n        <view class=\"date-header\">\n          <text class=\"date-text\">{{ formatDateHeader(group.date) }}</text>\n          <view class=\"date-summary\">\n            <text class=\"date-income\" v-if=\"group.income > 0\">\n              收入 {{ formatAmount(group.income) }}\n            </text>\n            <text class=\"date-expense\" v-if=\"group.expense > 0\">\n              支出 {{ formatAmount(group.expense) }}\n            </text>\n          </view>\n        </view>\n        \n        <view class=\"record-items\">\n          <view \n            class=\"record-item\" \n            v-for=\"record in group.records\" \n            :key=\"record.id\"\n            @click=\"editRecord(record)\"\n            @longpress=\"showRecordActions(record)\"\n          >\n            <view class=\"record-left\">\n              <view class=\"record-icon\" :style=\"{ backgroundColor: record.categoryColor }\">\n                {{ record.categoryIcon }}\n              </view>\n              <view class=\"record-info\">\n                <text class=\"record-category\">{{ record.categoryName }}</text>\n                <text class=\"record-note\" v-if=\"record.note\">{{ record.note }}</text>\n                <text class=\"record-time\">{{ record.time }}</text>\n              </view>\n            </view>\n            \n            <view class=\"record-right\">\n              <text class=\"record-amount\" :class=\"record.type\">\n                {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}\n              </text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 空状态 -->\n    <view class=\"empty-state\" v-else>\n      <view class=\"empty-icon\">📝</view>\n      <text class=\"empty-text\">暂无记录</text>\n      <text class=\"empty-desc\">点击右下角按钮开始记账</text>\n    </view>\n\n    <!-- 浮动添加按钮 -->\n    <view class=\"fab\" @click=\"addRecord\">\n      <text class=\"fab-icon\">+</text>\n    </view>\n\n    <!-- 操作菜单 -->\n    <uni-popup ref=\"actionPopup\" type=\"bottom\">\n      <view class=\"action-menu\">\n        <view class=\"action-item\" @click=\"editSelectedRecord\">\n          <text class=\"action-text\">编辑</text>\n        </view>\n        <view class=\"action-item delete\" @click=\"deleteSelectedRecord\">\n          <text class=\"action-text\">删除</text>\n        </view>\n        <view class=\"action-item cancel\" @click=\"closeActionMenu\">\n          <text class=\"action-text\">取消</text>\n        </view>\n      </view>\n    </uni-popup>\n  </view>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from 'vue'\nimport store from '../../store/index.js'\nimport { formatAmount, formatDate, getRelativeDateText, showToast } from '../../utils/helpers.js'\n\nexport default {\n  name: 'ListPage',\n  setup() {\n    // 筛选状态\n    const typeFilter = ref('all') // all, income, expense\n    const dateFilter = ref('all') // all, today, week, month, custom\n    const categoryFilter = ref('all') // all, categoryId\n    const selectedRecord = ref(null)\n\n    // 计算属性\n    const filteredRecords = computed(() => {\n      let records = [...store.state.records]\n\n      // 类型筛选\n      if (typeFilter.value !== 'all') {\n        records = records.filter(record => record.type === typeFilter.value)\n      }\n\n      // 日期筛选\n      if (dateFilter.value !== 'all') {\n        const today = formatDate(new Date())\n        const now = new Date()\n        \n        switch (dateFilter.value) {\n          case 'today':\n            records = records.filter(record => record.date === today)\n            break\n          case 'week':\n            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))\n            const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6))\n            records = records.filter(record => \n              record.date >= formatDate(weekStart) && record.date <= formatDate(weekEnd)\n            )\n            break\n          case 'month':\n            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)\n            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)\n            records = records.filter(record => \n              record.date >= formatDate(monthStart) && record.date <= formatDate(monthEnd)\n            )\n            break\n        }\n      }\n\n      // 分类筛选\n      if (categoryFilter.value !== 'all') {\n        records = records.filter(record => record.categoryId === categoryFilter.value)\n      }\n\n      return records.sort((a, b) => {\n        const dateTimeA = new Date(a.date + ' ' + a.time)\n        const dateTimeB = new Date(b.date + ' ' + b.time)\n        return dateTimeB - dateTimeA\n      })\n    })\n\n    // 按日期分组的记录\n    const groupedRecords = computed(() => {\n      const groups = {}\n      \n      filteredRecords.value.forEach(record => {\n        if (!groups[record.date]) {\n          groups[record.date] = {\n            date: record.date,\n            records: [],\n            income: 0,\n            expense: 0\n          }\n        }\n        \n        groups[record.date].records.push(record)\n        \n        if (record.type === 'income') {\n          groups[record.date].income += record.amount\n        } else {\n          groups[record.date].expense += record.amount\n        }\n      })\n\n      return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date))\n    })\n\n    // 汇总统计\n    const summaryIncome = computed(() => \n      filteredRecords.value\n        .filter(record => record.type === 'income')\n        .reduce((total, record) => total + record.amount, 0)\n    )\n\n    const summaryExpense = computed(() => \n      filteredRecords.value\n        .filter(record => record.type === 'expense')\n        .reduce((total, record) => total + record.amount, 0)\n    )\n\n    const summaryBalance = computed(() => summaryIncome.value - summaryExpense.value)\n\n    // 筛选文本\n    const typeFilterText = computed(() => {\n      switch (typeFilter.value) {\n        case 'income': return '收入'\n        case 'expense': return '支出'\n        default: return '全部'\n      }\n    })\n\n    const dateFilterText = computed(() => {\n      switch (dateFilter.value) {\n        case 'today': return '今天'\n        case 'week': return '本周'\n        case 'month': return '本月'\n        default: return '全部'\n      }\n    })\n\n    const categoryFilterText = computed(() => {\n      if (categoryFilter.value === 'all') {\n        return '全部分类'\n      }\n      // 查找分类名称\n      const category = store.actions.getCategoryById(categoryFilter.value, typeFilter.value)\n      return category ? category.name : '全部分类'\n    })\n\n    // 方法\n    const formatDateHeader = (date) => {\n      return getRelativeDateText(date)\n    }\n\n    const showTypeFilter = () => {\n      uni.showActionSheet({\n        itemList: ['全部', '收入', '支出'],\n        success: (res) => {\n          const types = ['all', 'income', 'expense']\n          typeFilter.value = types[res.tapIndex]\n        }\n      })\n    }\n\n    const showDateFilter = () => {\n      uni.showActionSheet({\n        itemList: ['全部', '今天', '本周', '本月'],\n        success: (res) => {\n          const dates = ['all', 'today', 'week', 'month']\n          dateFilter.value = dates[res.tapIndex]\n        }\n      })\n    }\n\n    const showCategoryFilter = () => {\n      const categories = store.state.categories[typeFilter.value === 'income' ? 'income' : 'expense']\n      const itemList = ['全部分类', ...categories.map(cat => cat.name)]\n      \n      uni.showActionSheet({\n        itemList,\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            categoryFilter.value = 'all'\n          } else {\n            categoryFilter.value = categories[res.tapIndex - 1].id\n          }\n        }\n      })\n    }\n\n    const addRecord = () => {\n      // 记账页面是tabbar页面，使用switchTab\n      uni.switchTab({\n        url: '/pages/add/add'\n      })\n    }\n\n    const editRecord = (record) => {\n      // 编辑记录需要传递参数，但switchTab不支持传参\n      // 先存储记录ID到本地，然后跳转\n      uni.setStorageSync('editRecordId', record.id)\n      uni.switchTab({\n        url: '/pages/add/add'\n      })\n    }\n\n    const showRecordActions = (record) => {\n      selectedRecord.value = record\n      // 这里应该显示操作菜单，但由于uni-popup组件需要额外安装，我们使用系统菜单\n      uni.showActionSheet({\n        itemList: ['编辑', '删除'],\n        success: (res) => {\n          if (res.tapIndex === 0) {\n            editSelectedRecord()\n          } else if (res.tapIndex === 1) {\n            deleteSelectedRecord()\n          }\n        }\n      })\n    }\n\n    const editSelectedRecord = () => {\n      if (selectedRecord.value) {\n        editRecord(selectedRecord.value)\n      }\n    }\n\n    const deleteSelectedRecord = () => {\n      if (selectedRecord.value) {\n        uni.showModal({\n          title: '确认删除',\n          content: '确定要删除这条记录吗？',\n          success: (res) => {\n            if (res.confirm) {\n              store.actions.deleteRecord(selectedRecord.value.id)\n              selectedRecord.value = null\n            }\n          }\n        })\n      }\n    }\n\n    const closeActionMenu = () => {\n      selectedRecord.value = null\n    }\n\n    onMounted(() => {\n      store.actions.loadRecords()\n    })\n\n    return {\n      filteredRecords,\n      groupedRecords,\n      summaryIncome,\n      summaryExpense,\n      summaryBalance,\n      typeFilterText,\n      dateFilterText,\n      categoryFilterText,\n      formatAmount,\n      formatDateHeader,\n      showTypeFilter,\n      showDateFilter,\n      showCategoryFilter,\n      addRecord,\n      editRecord,\n      showRecordActions,\n      editSelectedRecord,\n      deleteSelectedRecord,\n      closeActionMenu\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.list-page {\n  background-color: $uni-bg-color-grey;\n  min-height: 100vh;\n  padding-bottom: 80px; // 为浮动按钮留出空间\n}\n\n/* 筛选栏 */\n.filter-bar {\n  display: flex;\n  background-color: white;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.filter-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 12px;\n  border-radius: 6px;\n  margin: 0 4px;\n  background-color: $uni-bg-color-grey;\n  transition: background-color 0.3s ease;\n\n  &:active {\n    background-color: $uni-bg-color-hover;\n  }\n}\n\n.filter-text {\n  font-size: 14px;\n  color: $uni-text-color;\n  margin-right: 4px;\n}\n\n.filter-arrow {\n  font-size: 10px;\n  color: $uni-text-color-grey;\n}\n\n/* 统计卡片 */\n.summary-card {\n  display: flex;\n  background-color: white;\n  padding: 20px;\n  margin: 0 16px 16px;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.summary-item {\n  flex: 1;\n  text-align: center;\n}\n\n.summary-label {\n  display: block;\n  font-size: 12px;\n  color: $uni-text-color-grey;\n  margin-bottom: 8px;\n}\n\n.summary-value {\n  font-size: 18px;\n  font-weight: 600;\n\n  &.income {\n    color: $income-color;\n  }\n\n  &.expense {\n    color: $expense-color;\n  }\n\n  &.negative {\n    color: $expense-color;\n  }\n}\n\n/* 记录列表 */\n.record-list {\n  padding: 0 16px;\n}\n\n.date-group {\n  margin-bottom: 16px;\n}\n\n.date-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background-color: rgba(255, 255, 255, 0.8);\n  border-radius: 8px 8px 0 0;\n  border-bottom: 1px solid $uni-border-color;\n}\n\n.date-text {\n  font-size: 14px;\n  font-weight: 600;\n  color: $uni-text-color;\n}\n\n.date-summary {\n  display: flex;\n  gap: 12px;\n}\n\n.date-income, .date-expense {\n  font-size: 12px;\n}\n\n.date-income {\n  color: $income-color;\n}\n\n.date-expense {\n  color: $expense-color;\n}\n\n.record-items {\n  background-color: white;\n  border-radius: 0 0 8px 8px;\n  overflow: hidden;\n}\n\n.record-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid $uni-border-color;\n  transition: background-color 0.3s ease;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &:active {\n    background-color: $uni-bg-color-hover;\n  }\n}\n\n.record-left {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.record-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  margin-right: 12px;\n}\n\n.record-info {\n  flex: 1;\n}\n\n.record-category {\n  font-size: 16px;\n  font-weight: 500;\n  color: $uni-text-color;\n  display: block;\n  margin-bottom: 4px;\n}\n\n.record-note {\n  font-size: 12px;\n  color: $uni-text-color-grey;\n  display: block;\n  margin-bottom: 2px;\n}\n\n.record-time {\n  font-size: 12px;\n  color: $uni-text-color-placeholder;\n}\n\n.record-right {\n  text-align: right;\n}\n\n.record-amount {\n  font-size: 16px;\n  font-weight: 600;\n\n  &.income {\n    color: $income-color;\n  }\n\n  &.expense {\n    color: $expense-color;\n  }\n}\n\n/* 空状态 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  text-align: center;\n}\n\n.empty-icon {\n  font-size: 64px;\n  margin-bottom: 20px;\n  opacity: 0.5;\n}\n\n.empty-text {\n  font-size: 18px;\n  color: $uni-text-color-grey;\n  margin-bottom: 8px;\n}\n\n.empty-desc {\n  font-size: 14px;\n  color: $uni-text-color-placeholder;\n}\n\n/* 浮动添加按钮 */\n.fab {\n  position: fixed;\n  right: 20px;\n  bottom: 100px; // 避开tabbar\n  width: 56px;\n  height: 56px;\n  border-radius: 28px;\n  background-color: $uni-color-primary;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);\n  z-index: 999;\n  transition: all 0.3s ease;\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.fab-icon {\n  font-size: 24px;\n  color: white;\n  font-weight: 300;\n}\n\n/* 操作菜单 */\n.action-menu {\n  background-color: white;\n  border-radius: 12px 12px 0 0;\n  padding: 20px 0;\n}\n\n.action-item {\n  padding: 16px 20px;\n  text-align: center;\n  border-bottom: 1px solid $uni-border-color;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  &.delete .action-text {\n    color: $uni-color-error;\n  }\n\n  &.cancel .action-text {\n    color: $uni-text-color-grey;\n  }\n\n  &:active {\n    background-color: $uni-bg-color-hover;\n  }\n}\n\n.action-text {\n  font-size: 16px;\n  color: $uni-text-color;\n}\n</style>\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/list/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "store", "formatDate", "getRelativeDateText", "uni", "onMounted", "formatAmount"], "mappings": ";;;;AAoHA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,iBAAiBA,cAAG,IAAC,KAAK;AAChC,UAAM,iBAAiBA,cAAG,IAAC,IAAI;AAG/B,UAAM,kBAAkBC,cAAAA,SAAS,MAAM;AACrC,UAAI,UAAU,CAAC,GAAGC,kBAAM,MAAM,OAAO;AAGrC,UAAI,WAAW,UAAU,OAAO;AAC9B,kBAAU,QAAQ,OAAO,YAAU,OAAO,SAAS,WAAW,KAAK;AAAA,MACrE;AAGA,UAAI,WAAW,UAAU,OAAO;AAC9B,cAAM,QAAQC,cAAAA,WAAW,oBAAI,MAAM;AACnC,cAAM,MAAM,oBAAI,KAAK;AAErB,gBAAQ,WAAW,OAAK;AAAA,UACtB,KAAK;AACH,sBAAU,QAAQ,OAAO,YAAU,OAAO,SAAS,KAAK;AACxD;AAAA,UACF,KAAK;AACH,kBAAM,YAAY,IAAI,KAAK,IAAI,QAAQ,IAAI,YAAY,IAAI,OAAM,CAAE,CAAC;AACpE,kBAAM,UAAU,IAAI,KAAK,IAAI,QAAQ,IAAI,QAAO,IAAK,IAAI,OAAO,IAAI,CAAC,CAAC;AACtE,sBAAU,QAAQ;AAAA,cAAO,YACvB,OAAO,QAAQA,cAAAA,WAAW,SAAS,KAAK,OAAO,QAAQA,cAAU,WAAC,OAAO;AAAA,YAC3E;AACA;AAAA,UACF,KAAK;AACH,kBAAM,aAAa,IAAI,KAAK,IAAI,YAAW,GAAI,IAAI,SAAU,GAAE,CAAC;AAChE,kBAAM,WAAW,IAAI,KAAK,IAAI,eAAe,IAAI,SAAQ,IAAK,GAAG,CAAC;AAClE,sBAAU,QAAQ;AAAA,cAAO,YACvB,OAAO,QAAQA,cAAAA,WAAW,UAAU,KAAK,OAAO,QAAQA,cAAU,WAAC,QAAQ;AAAA,YAC7E;AACA;AAAA,QACJ;AAAA,MACF;AAGA,UAAI,eAAe,UAAU,OAAO;AAClC,kBAAU,QAAQ,OAAO,YAAU,OAAO,eAAe,eAAe,KAAK;AAAA,MAC/E;AAEA,aAAO,QAAQ,KAAK,CAAC,GAAG,MAAM;AAC5B,cAAM,YAAY,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI;AAChD,cAAM,YAAY,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI;AAChD,eAAO,YAAY;AAAA,OACpB;AAAA,KACF;AAGD,UAAM,iBAAiBF,cAAAA,SAAS,MAAM;AACpC,YAAM,SAAS,CAAC;AAEhB,sBAAgB,MAAM,QAAQ,YAAU;AACtC,YAAI,CAAC,OAAO,OAAO,IAAI,GAAG;AACxB,iBAAO,OAAO,IAAI,IAAI;AAAA,YACpB,MAAM,OAAO;AAAA,YACb,SAAS,CAAE;AAAA,YACX,QAAQ;AAAA,YACR,SAAS;AAAA,UACX;AAAA,QACF;AAEA,eAAO,OAAO,IAAI,EAAE,QAAQ,KAAK,MAAM;AAEvC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,OAAO,IAAI,EAAE,UAAU,OAAO;AAAA,eAChC;AACL,iBAAO,OAAO,IAAI,EAAE,WAAW,OAAO;AAAA,QACxC;AAAA,OACD;AAED,aAAO,OAAO,OAAO,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC;AAAA,KAChF;AAGD,UAAM,gBAAgBA,cAAAA;AAAAA,MAAS,MAC7B,gBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAEA,UAAM,iBAAiBA,cAAAA;AAAAA,MAAS,MAC9B,gBAAgB,MACb,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAEA,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,cAAc,QAAQ,eAAe,KAAK;AAGhF,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,cAAQ,WAAW,OAAK;AAAA,QACtB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAW,iBAAO;AAAA,QACvB;AAAS,iBAAO;AAAA,MAClB;AAAA,KACD;AAED,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,cAAQ,WAAW,OAAK;AAAA,QACtB,KAAK;AAAS,iBAAO;AAAA,QACrB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAS,iBAAO;AAAA,QACrB;AAAS,iBAAO;AAAA,MAClB;AAAA,KACD;AAED,UAAM,qBAAqBA,cAAAA,SAAS,MAAM;AACxC,UAAI,eAAe,UAAU,OAAO;AAClC,eAAO;AAAA,MACT;AAEA,YAAM,WAAWC,YAAK,MAAC,QAAQ,gBAAgB,eAAe,OAAO,WAAW,KAAK;AACrF,aAAO,WAAW,SAAS,OAAO;AAAA,KACnC;AAGD,UAAM,mBAAmB,CAAC,SAAS;AACjC,aAAOE,cAAAA,oBAAoB,IAAI;AAAA,IACjC;AAEA,UAAM,iBAAiB,MAAM;AAC3BC,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,MAAM,IAAI;AAAA,QAC3B,SAAS,CAAC,QAAQ;AAChB,gBAAM,QAAQ,CAAC,OAAO,UAAU,SAAS;AACzC,qBAAW,QAAQ,MAAM,IAAI,QAAQ;AAAA,QACvC;AAAA,OACD;AAAA,IACH;AAEA,UAAM,iBAAiB,MAAM;AAC3BA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,QACjC,SAAS,CAAC,QAAQ;AAChB,gBAAM,QAAQ,CAAC,OAAO,SAAS,QAAQ,OAAO;AAC9C,qBAAW,QAAQ,MAAM,IAAI,QAAQ;AAAA,QACvC;AAAA,OACD;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,aAAaH,kBAAM,MAAM,WAAW,WAAW,UAAU,WAAW,WAAW,SAAS;AAC9F,YAAM,WAAW,CAAC,QAAQ,GAAG,WAAW,IAAI,SAAO,IAAI,IAAI,CAAC;AAE5DG,oBAAAA,MAAI,gBAAgB;AAAA,QAClB;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AACtB,2BAAe,QAAQ;AAAA,iBAClB;AACL,2BAAe,QAAQ,WAAW,IAAI,WAAW,CAAC,EAAE;AAAA,UACtD;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAEA,UAAM,YAAY,MAAM;AAEtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,WAAW;AAG7BA,oBAAAA,MAAI,eAAe,gBAAgB,OAAO,EAAE;AAC5CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAEA,UAAM,oBAAoB,CAAC,WAAW;AACpC,qBAAe,QAAQ;AAEvBA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,CAAC,MAAM,IAAI;AAAA,QACrB,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,aAAa,GAAG;AACtB,+BAAmB;AAAA,qBACV,IAAI,aAAa,GAAG;AAC7B,iCAAqB;AAAA,UACvB;AAAA,QACF;AAAA,OACD;AAAA,IACH;AAEA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,eAAe,OAAO;AACxB,mBAAW,eAAe,KAAK;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,uBAAuB,MAAM;AACjC,UAAI,eAAe,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACfH,0BAAAA,MAAM,QAAQ,aAAa,eAAe,MAAM,EAAE;AAClD,6BAAe,QAAQ;AAAA,YACzB;AAAA,UACF;AAAA,SACD;AAAA,MACH;AAAA,IACF;AAEA,UAAM,kBAAkB,MAAM;AAC5B,qBAAe,QAAQ;AAAA,IACzB;AAEAI,kBAAAA,UAAU,MAAM;AACdJ,kBAAK,MAAC,QAAQ,YAAY;AAAA,KAC3B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,oBACAK,cAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1WA,GAAG,WAAW,eAAe;"}