{"version": 3, "file": "models.js", "sources": ["utils/models.js"], "sourcesContent": ["/**\n * 数据模型定义\n */\n\n/**\n * 记账记录模型\n */\nexport class Record {\n  constructor(data = {}) {\n    this.id = data.id || this.generateId()\n    this.type = data.type || 'expense' // expense: 支出, income: 收入\n    this.amount = data.amount || 0\n    this.categoryId = data.categoryId || null\n    this.categoryName = data.categoryName || ''\n    this.categoryIcon = data.categoryIcon || ''\n    this.categoryColor = data.categoryColor || ''\n    this.note = data.note || ''\n    this.date = data.date || new Date().toISOString().split('T')[0]\n    this.time = data.time || new Date().toTimeString().split(' ')[0].substring(0, 5)\n    this.createTime = data.createTime || Date.now()\n    this.updateTime = data.updateTime || Date.now()\n  }\n\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2)\n  }\n\n  // 更新记录\n  update(data) {\n    Object.keys(data).forEach(key => {\n      if (key !== 'id' && key !== 'createTime') {\n        this[key] = data[key]\n      }\n    })\n    this.updateTime = Date.now()\n    return this\n  }\n\n  // 转换为普通对象\n  toObject() {\n    return {\n      id: this.id,\n      type: this.type,\n      amount: this.amount,\n      categoryId: this.categoryId,\n      categoryName: this.categoryName,\n      categoryIcon: this.categoryIcon,\n      categoryColor: this.categoryColor,\n      note: this.note,\n      date: this.date,\n      time: this.time,\n      createTime: this.createTime,\n      updateTime: this.updateTime\n    }\n  }\n}\n\n/**\n * 分类模型\n */\nexport class Category {\n  constructor(data = {}) {\n    this.id = data.id || this.generateId()\n    this.name = data.name || ''\n    this.icon = data.icon || '📝'\n    this.color = data.color || '#999999'\n    this.type = data.type || 'expense' // expense: 支出分类, income: 收入分类\n    this.isDefault = data.isDefault || false\n    this.createTime = data.createTime || Date.now()\n    this.updateTime = data.updateTime || Date.now()\n  }\n\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2)\n  }\n\n  update(data) {\n    Object.keys(data).forEach(key => {\n      if (key !== 'id' && key !== 'createTime') {\n        this[key] = data[key]\n      }\n    })\n    this.updateTime = Date.now()\n    return this\n  }\n\n  toObject() {\n    return {\n      id: this.id,\n      name: this.name,\n      icon: this.icon,\n      color: this.color,\n      type: this.type,\n      isDefault: this.isDefault,\n      createTime: this.createTime,\n      updateTime: this.updateTime\n    }\n  }\n}\n\n/**\n * 统计数据模型\n */\nexport class Statistics {\n  constructor(records = []) {\n    this.records = records\n  }\n\n  // 获取总收入\n  getTotalIncome() {\n    return this.records\n      .filter(record => record.type === 'income')\n      .reduce((total, record) => total + record.amount, 0)\n  }\n\n  // 获取总支出\n  getTotalExpense() {\n    return this.records\n      .filter(record => record.type === 'expense')\n      .reduce((total, record) => total + record.amount, 0)\n  }\n\n  // 获取净收入（收入-支出）\n  getNetIncome() {\n    return this.getTotalIncome() - this.getTotalExpense()\n  }\n\n  // 按分类统计\n  getStatsByCategory() {\n    const stats = {}\n    \n    this.records.forEach(record => {\n      const key = `${record.type}_${record.categoryId}`\n      if (!stats[key]) {\n        stats[key] = {\n          categoryId: record.categoryId,\n          categoryName: record.categoryName,\n          categoryIcon: record.categoryIcon,\n          categoryColor: record.categoryColor,\n          type: record.type,\n          amount: 0,\n          count: 0\n        }\n      }\n      stats[key].amount += record.amount\n      stats[key].count += 1\n    })\n\n    return Object.values(stats)\n  }\n\n  // 按日期统计\n  getStatsByDate() {\n    const stats = {}\n    \n    this.records.forEach(record => {\n      if (!stats[record.date]) {\n        stats[record.date] = {\n          date: record.date,\n          income: 0,\n          expense: 0,\n          count: 0\n        }\n      }\n      \n      if (record.type === 'income') {\n        stats[record.date].income += record.amount\n      } else {\n        stats[record.date].expense += record.amount\n      }\n      stats[record.date].count += 1\n    })\n\n    return Object.values(stats).sort((a, b) => new Date(a.date) - new Date(b.date))\n  }\n\n  // 按月份统计\n  getStatsByMonth() {\n    const stats = {}\n    \n    this.records.forEach(record => {\n      const month = record.date.substring(0, 7) // YYYY-MM\n      if (!stats[month]) {\n        stats[month] = {\n          month: month,\n          income: 0,\n          expense: 0,\n          count: 0\n        }\n      }\n      \n      if (record.type === 'income') {\n        stats[month].income += record.amount\n      } else {\n        stats[month].expense += record.amount\n      }\n      stats[month].count += 1\n    })\n\n    return Object.values(stats).sort((a, b) => a.month.localeCompare(b.month))\n  }\n}\n"], "names": [], "mappings": ";AAOO,MAAM,OAAO;AAAA,EAClB,YAAY,OAAO,IAAI;AACrB,SAAK,KAAK,KAAK,MAAM,KAAK,WAAY;AACtC,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,SAAS,KAAK,UAAU;AAC7B,SAAK,aAAa,KAAK,cAAc;AACrC,SAAK,eAAe,KAAK,gBAAgB;AACzC,SAAK,eAAe,KAAK,gBAAgB;AACzC,SAAK,gBAAgB,KAAK,iBAAiB;AAC3C,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,OAAO,KAAK,SAAQ,oBAAI,QAAO,cAAc,MAAM,GAAG,EAAE,CAAC;AAC9D,SAAK,OAAO,KAAK,SAAQ,oBAAI,KAAM,GAAC,aAAY,EAAG,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC;AAC/E,SAAK,aAAa,KAAK,cAAc,KAAK,IAAK;AAC/C,SAAK,aAAa,KAAK,cAAc,KAAK,IAAK;AAAA,EAChD;AAAA,EAED,aAAa;AACX,WAAO,KAAK,IAAG,EAAG,SAAS,EAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,CAAC;AAAA,EACrE;AAAA;AAAA,EAGD,OAAO,MAAM;AACX,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC/B,UAAI,QAAQ,QAAQ,QAAQ,cAAc;AACxC,aAAK,GAAG,IAAI,KAAK,GAAG;AAAA,MACrB;AAAA,IACP,CAAK;AACD,SAAK,aAAa,KAAK,IAAK;AAC5B,WAAO;AAAA,EACR;AAAA;AAAA,EAGD,WAAW;AACT,WAAO;AAAA,MACL,IAAI,KAAK;AAAA,MACT,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,cAAc,KAAK;AAAA,MACnB,cAAc,KAAK;AAAA,MACnB,eAAe,KAAK;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,IAClB;AAAA,EACF;AACH;AAgDO,MAAM,WAAW;AAAA,EACtB,YAAY,UAAU,IAAI;AACxB,SAAK,UAAU;AAAA,EAChB;AAAA;AAAA,EAGD,iBAAiB;AACf,WAAO,KAAK,QACT,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,kBAAkB;AAChB,WAAO,KAAK,QACT,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,eAAe;AACb,WAAO,KAAK,mBAAmB,KAAK,gBAAiB;AAAA,EACtD;AAAA;AAAA,EAGD,qBAAqB;AACnB,UAAM,QAAQ,CAAE;AAEhB,SAAK,QAAQ,QAAQ,YAAU;AAC7B,YAAM,MAAM,GAAG,OAAO,IAAI,IAAI,OAAO,UAAU;AAC/C,UAAI,CAAC,MAAM,GAAG,GAAG;AACf,cAAM,GAAG,IAAI;AAAA,UACX,YAAY,OAAO;AAAA,UACnB,cAAc,OAAO;AAAA,UACrB,cAAc,OAAO;AAAA,UACrB,eAAe,OAAO;AAAA,UACtB,MAAM,OAAO;AAAA,UACb,QAAQ;AAAA,UACR,OAAO;AAAA,QACR;AAAA,MACF;AACD,YAAM,GAAG,EAAE,UAAU,OAAO;AAC5B,YAAM,GAAG,EAAE,SAAS;AAAA,IAC1B,CAAK;AAED,WAAO,OAAO,OAAO,KAAK;AAAA,EAC3B;AAAA;AAAA,EAGD,iBAAiB;AACf,UAAM,QAAQ,CAAE;AAEhB,SAAK,QAAQ,QAAQ,YAAU;AAC7B,UAAI,CAAC,MAAM,OAAO,IAAI,GAAG;AACvB,cAAM,OAAO,IAAI,IAAI;AAAA,UACnB,MAAM,OAAO;AAAA,UACb,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,MACF;AAED,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,OAAO,IAAI,EAAE,UAAU,OAAO;AAAA,MAC5C,OAAa;AACL,cAAM,OAAO,IAAI,EAAE,WAAW,OAAO;AAAA,MACtC;AACD,YAAM,OAAO,IAAI,EAAE,SAAS;AAAA,IAClC,CAAK;AAED,WAAO,OAAO,OAAO,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,CAAC;AAAA,EAC/E;AAAA;AAAA,EAGD,kBAAkB;AAChB,UAAM,QAAQ,CAAE;AAEhB,SAAK,QAAQ,QAAQ,YAAU;AAC7B,YAAM,QAAQ,OAAO,KAAK,UAAU,GAAG,CAAC;AACxC,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB,cAAM,KAAK,IAAI;AAAA,UACb;AAAA,UACA,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,QACR;AAAA,MACF;AAED,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,KAAK,EAAE,UAAU,OAAO;AAAA,MACtC,OAAa;AACL,cAAM,KAAK,EAAE,WAAW,OAAO;AAAA,MAChC;AACD,YAAM,KAAK,EAAE,SAAS;AAAA,IAC5B,CAAK;AAED,WAAO,OAAO,OAAO,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;AAAA,EAC1E;AACH;;;"}