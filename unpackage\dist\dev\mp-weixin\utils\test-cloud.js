"use strict";
const common_vendor = require("../common/vendor.js");
async function testCloudFunction() {
  common_vendor.index.__f__("log", "at utils/test-cloud.js:6", "开始测试云函数连接...");
  try {
    common_vendor.index.__f__("log", "at utils/test-cloud.js:10", "1. 测试基础云函数...");
    const testResult = await common_vendor.nr.callFunction({
      name: "test",
      data: { message: "hello from client" }
    });
    common_vendor.index.__f__("log", "at utils/test-cloud.js:16", "基础云函数测试结果:", testResult);
    if (testResult.result && testResult.result.code === 200) {
      common_vendor.index.__f__("log", "at utils/test-cloud.js:19", "✅ 基础云函数调用成功");
      common_vendor.index.__f__("log", "at utils/test-cloud.js:22", "2. 测试数据库初始化...");
      const initResult = await common_vendor.nr.callFunction({
        name: "init-db",
        data: {}
      });
      common_vendor.index.__f__("log", "at utils/test-cloud.js:28", "数据库初始化结果:", initResult);
      if (initResult.result && initResult.result.code === 200) {
        common_vendor.index.__f__("log", "at utils/test-cloud.js:31", "✅ 数据库初始化成功");
        common_vendor.index.__f__("log", "at utils/test-cloud.js:34", "3. 测试获取分类...");
        const categoriesResult = await common_vendor.nr.callFunction({
          name: "bookkeeping",
          data: {
            action: "getCategories",
            data: { type: "expense" }
          }
        });
        common_vendor.index.__f__("log", "at utils/test-cloud.js:43", "获取分类结果:", categoriesResult);
        if (categoriesResult.result && categoriesResult.result.code === 200) {
          common_vendor.index.__f__("log", "at utils/test-cloud.js:46", "✅ 获取分类成功，分类数量:", categoriesResult.result.data.length);
        } else {
          common_vendor.index.__f__("log", "at utils/test-cloud.js:48", "❌ 获取分类失败:", categoriesResult.result ? categoriesResult.result.message : "未知错误");
        }
      } else {
        common_vendor.index.__f__("log", "at utils/test-cloud.js:51", "❌ 数据库初始化失败:", initResult.result ? initResult.result.message : "未知错误");
      }
    } else {
      common_vendor.index.__f__("log", "at utils/test-cloud.js:54", "❌ 基础云函数调用失败:", testResult.result ? testResult.result.message : "未知错误");
    }
    common_vendor.index.__f__("log", "at utils/test-cloud.js:57", "🎉 云函数基础测试完成");
    return {
      success: true,
      message: "云函数测试完成"
    };
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/test-cloud.js:65", "❌ 云函数测试失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
exports.testCloudFunction = testCloudFunction;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/test-cloud.js.map
