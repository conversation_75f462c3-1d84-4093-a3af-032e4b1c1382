<template>
  <view class="list-page">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item" @click="showTypeFilter">
        <text class="filter-text">{{ typeFilterText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" @click="showDateFilter">
        <text class="filter-text">{{ dateFilterText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
      
      <view class="filter-item" @click="showCategoryFilter">
        <text class="filter-text">{{ categoryFilterText }}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="summary-card" v-if="filteredRecords.length > 0">
      <view class="summary-item">
        <text class="summary-label">收入</text>
        <text class="summary-value income">{{ formatAmount(summaryIncome) }}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">支出</text>
        <text class="summary-value expense">{{ formatAmount(summaryExpense) }}</text>
      </view>
      <view class="summary-item">
        <text class="summary-label">结余</text>
        <text class="summary-value" :class="{ 'negative': summaryBalance < 0 }">
          {{ formatAmount(summaryBalance) }}
        </text>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="record-list" v-if="groupedRecords.length > 0">
      <view class="date-group" v-for="group in groupedRecords" :key="group.date">
        <view class="date-header">
          <text class="date-text">{{ formatDateHeader(group.date) }}</text>
          <view class="date-summary">
            <text class="date-income" v-if="group.income > 0">
              收入 {{ formatAmount(group.income) }}
            </text>
            <text class="date-expense" v-if="group.expense > 0">
              支出 {{ formatAmount(group.expense) }}
            </text>
          </view>
        </view>
        
        <view class="record-items">
          <view 
            class="record-item" 
            v-for="record in group.records" 
            :key="record.id"
            @click="editRecord(record)"
            @longpress="showRecordActions(record)"
          >
            <view class="record-left">
              <view class="record-icon" :style="{ backgroundColor: record.categoryColor }">
                {{ record.categoryIcon }}
              </view>
              <view class="record-info">
                <text class="record-category">{{ record.categoryName }}</text>
                <text class="record-note" v-if="record.note">{{ record.note }}</text>
                <text class="record-time">{{ record.time }}</text>
              </view>
            </view>
            
            <view class="record-right">
              <text class="record-amount" :class="record.type">
                {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <view class="empty-icon">📝</view>
      <text class="empty-text">暂无记录</text>
      <text class="empty-desc">点击右下角按钮开始记账</text>
    </view>

    <!-- 浮动添加按钮 -->
    <view class="fab" @click="addRecord">
      <text class="fab-icon">+</text>
    </view>

    <!-- 操作菜单 -->
    <uni-popup ref="actionPopup" type="bottom">
      <view class="action-menu">
        <view class="action-item" @click="editSelectedRecord">
          <text class="action-text">编辑</text>
        </view>
        <view class="action-item delete" @click="deleteSelectedRecord">
          <text class="action-text">删除</text>
        </view>
        <view class="action-item cancel" @click="closeActionMenu">
          <text class="action-text">取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatAmount, formatDate, getRelativeDateText, showToast } from '../../utils/helpers.js'

export default {
  name: 'ListPage',
  setup() {
    // 筛选状态
    const typeFilter = ref('all') // all, income, expense
    const dateFilter = ref('all') // all, today, week, month, custom
    const categoryFilter = ref('all') // all, categoryId
    const selectedRecord = ref(null)

    // 计算属性
    const filteredRecords = computed(() => {
      let records = [...store.state.records]

      // 类型筛选
      if (typeFilter.value !== 'all') {
        records = records.filter(record => record.type === typeFilter.value)
      }

      // 日期筛选
      if (dateFilter.value !== 'all') {
        const today = formatDate(new Date())
        const now = new Date()
        
        switch (dateFilter.value) {
          case 'today':
            records = records.filter(record => record.date === today)
            break
          case 'week':
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
            const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6))
            records = records.filter(record => 
              record.date >= formatDate(weekStart) && record.date <= formatDate(weekEnd)
            )
            break
          case 'month':
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
            const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)
            records = records.filter(record => 
              record.date >= formatDate(monthStart) && record.date <= formatDate(monthEnd)
            )
            break
        }
      }

      // 分类筛选
      if (categoryFilter.value !== 'all') {
        records = records.filter(record => record.categoryId === categoryFilter.value)
      }

      return records.sort((a, b) => {
        const dateTimeA = new Date(a.date + ' ' + a.time)
        const dateTimeB = new Date(b.date + ' ' + b.time)
        return dateTimeB - dateTimeA
      })
    })

    // 按日期分组的记录
    const groupedRecords = computed(() => {
      const groups = {}
      
      filteredRecords.value.forEach(record => {
        if (!groups[record.date]) {
          groups[record.date] = {
            date: record.date,
            records: [],
            income: 0,
            expense: 0
          }
        }
        
        groups[record.date].records.push(record)
        
        if (record.type === 'income') {
          groups[record.date].income += record.amount
        } else {
          groups[record.date].expense += record.amount
        }
      })

      return Object.values(groups).sort((a, b) => new Date(b.date) - new Date(a.date))
    })

    // 汇总统计
    const summaryIncome = computed(() => 
      filteredRecords.value
        .filter(record => record.type === 'income')
        .reduce((total, record) => total + record.amount, 0)
    )

    const summaryExpense = computed(() => 
      filteredRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
    )

    const summaryBalance = computed(() => summaryIncome.value - summaryExpense.value)

    // 筛选文本
    const typeFilterText = computed(() => {
      switch (typeFilter.value) {
        case 'income': return '收入'
        case 'expense': return '支出'
        default: return '全部'
      }
    })

    const dateFilterText = computed(() => {
      switch (dateFilter.value) {
        case 'today': return '今天'
        case 'week': return '本周'
        case 'month': return '本月'
        default: return '全部'
      }
    })

    const categoryFilterText = computed(() => {
      if (categoryFilter.value === 'all') {
        return '全部分类'
      }
      // 查找分类名称
      const category = store.actions.getCategoryById(categoryFilter.value, typeFilter.value)
      return category ? category.name : '全部分类'
    })

    // 方法
    const formatDateHeader = (date) => {
      return getRelativeDateText(date)
    }

    const showTypeFilter = () => {
      uni.showActionSheet({
        itemList: ['全部', '收入', '支出'],
        success: (res) => {
          const types = ['all', 'income', 'expense']
          typeFilter.value = types[res.tapIndex]
        }
      })
    }

    const showDateFilter = () => {
      uni.showActionSheet({
        itemList: ['全部', '今天', '本周', '本月'],
        success: (res) => {
          const dates = ['all', 'today', 'week', 'month']
          dateFilter.value = dates[res.tapIndex]
        }
      })
    }

    const showCategoryFilter = () => {
      const categories = store.state.categories[typeFilter.value === 'income' ? 'income' : 'expense']
      const itemList = ['全部分类', ...categories.map(cat => cat.name)]
      
      uni.showActionSheet({
        itemList,
        success: (res) => {
          if (res.tapIndex === 0) {
            categoryFilter.value = 'all'
          } else {
            categoryFilter.value = categories[res.tapIndex - 1].id
          }
        }
      })
    }

    const addRecord = () => {
      // 记账页面是tabbar页面，使用switchTab
      uni.switchTab({
        url: '/pages/add/add'
      })
    }

    const editRecord = (record) => {
      // 编辑记录需要传递参数，但switchTab不支持传参
      // 先存储记录ID到本地，然后跳转
      uni.setStorageSync('editRecordId', record.id)
      uni.switchTab({
        url: '/pages/add/add'
      })
    }

    const showRecordActions = (record) => {
      selectedRecord.value = record
      // 这里应该显示操作菜单，但由于uni-popup组件需要额外安装，我们使用系统菜单
      uni.showActionSheet({
        itemList: ['编辑', '删除'],
        success: (res) => {
          if (res.tapIndex === 0) {
            editSelectedRecord()
          } else if (res.tapIndex === 1) {
            deleteSelectedRecord()
          }
        }
      })
    }

    const editSelectedRecord = () => {
      if (selectedRecord.value) {
        editRecord(selectedRecord.value)
      }
    }

    const deleteSelectedRecord = () => {
      if (selectedRecord.value) {
        uni.showModal({
          title: '确认删除',
          content: '确定要删除这条记录吗？',
          success: (res) => {
            if (res.confirm) {
              store.actions.deleteRecord(selectedRecord.value.id)
              selectedRecord.value = null
            }
          }
        })
      }
    }

    const closeActionMenu = () => {
      selectedRecord.value = null
    }

    onMounted(() => {
      store.actions.loadRecords()
    })

    return {
      filteredRecords,
      groupedRecords,
      summaryIncome,
      summaryExpense,
      summaryBalance,
      typeFilterText,
      dateFilterText,
      categoryFilterText,
      formatAmount,
      formatDateHeader,
      showTypeFilter,
      showDateFilter,
      showCategoryFilter,
      addRecord,
      editRecord,
      showRecordActions,
      editSelectedRecord,
      deleteSelectedRecord,
      closeActionMenu
    }
  }
}
</script>

<style lang="scss" scoped>
.list-page {
  background-color: $uni-bg-color-grey;
  min-height: 100vh;
  padding-bottom: 80px; // 为浮动按钮留出空间
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background-color: white;
  padding: 12px 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 6px;
  margin: 0 4px;
  background-color: $uni-bg-color-grey;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.filter-text {
  font-size: 14px;
  color: $uni-text-color;
  margin-right: 4px;
}

.filter-arrow {
  font-size: 10px;
  color: $uni-text-color-grey;
}

/* 统计卡片 */
.summary-card {
  display: flex;
  background-color: white;
  padding: 20px;
  margin: 0 16px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-item {
  flex: 1;
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: $uni-text-color-grey;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 18px;
  font-weight: 600;

  &.income {
    color: $income-color;
  }

  &.expense {
    color: $expense-color;
  }

  &.negative {
    color: $expense-color;
  }
}

/* 记录列表 */
.record-list {
  padding: 0 16px;
}

.date-group {
  margin-bottom: 16px;
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid $uni-border-color;
}

.date-text {
  font-size: 14px;
  font-weight: 600;
  color: $uni-text-color;
}

.date-summary {
  display: flex;
  gap: 12px;
}

.date-income, .date-expense {
  font-size: 12px;
}

.date-income {
  color: $income-color;
}

.date-expense {
  color: $expense-color;
}

.record-items {
  background-color: white;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid $uni-border-color;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.record-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.record-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}

.record-info {
  flex: 1;
}

.record-category {
  font-size: 16px;
  font-weight: 500;
  color: $uni-text-color;
  display: block;
  margin-bottom: 4px;
}

.record-note {
  font-size: 12px;
  color: $uni-text-color-grey;
  display: block;
  margin-bottom: 2px;
}

.record-time {
  font-size: 12px;
  color: $uni-text-color-placeholder;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 16px;
  font-weight: 600;

  &.income {
    color: $income-color;
  }

  &.expense {
    color: $expense-color;
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-text {
  font-size: 18px;
  color: $uni-text-color-grey;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: $uni-text-color-placeholder;
}

/* 浮动添加按钮 */
.fab {
  position: fixed;
  right: 20px;
  bottom: 100px; // 避开tabbar
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: $uni-color-primary;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  z-index: 999;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.fab-icon {
  font-size: 24px;
  color: white;
  font-weight: 300;
}

/* 操作菜单 */
.action-menu {
  background-color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 0;
}

.action-item {
  padding: 16px 20px;
  text-align: center;
  border-bottom: 1px solid $uni-border-color;

  &:last-child {
    border-bottom: none;
  }

  &.delete .action-text {
    color: $uni-color-error;
  }

  &.cancel .action-text {
    color: $uni-text-color-grey;
  }

  &:active {
    background-color: $uni-bg-color-hover;
  }
}

.action-text {
  font-size: 16px;
  color: $uni-text-color;
}
</style>
