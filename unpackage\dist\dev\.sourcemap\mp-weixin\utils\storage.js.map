{"version": 3, "file": "storage.js", "sources": ["utils/storage.js"], "sourcesContent": ["/**\n * 本地存储工具类\n */\n\nconst STORAGE_KEYS = {\n  RECORDS: 'bookkeeping_records',\n  CATEGORIES: 'bookkeeping_categories',\n  SETTINGS: 'bookkeeping_settings'\n}\n\nclass Storage {\n  /**\n   * 设置存储数据\n   * @param {string} key 存储键\n   * @param {any} data 存储数据\n   */\n  static set(key, data) {\n    try {\n      const jsonData = JSON.stringify(data)\n      uni.setStorageSync(key, jsonData)\n      return true\n    } catch (error) {\n      console.error('Storage set error:', error)\n      return false\n    }\n  }\n\n  /**\n   * 获取存储数据\n   * @param {string} key 存储键\n   * @param {any} defaultValue 默认值\n   */\n  static get(key, defaultValue = null) {\n    try {\n      const jsonData = uni.getStorageSync(key)\n      if (jsonData) {\n        return JSON.parse(jsonData)\n      }\n      return defaultValue\n    } catch (error) {\n      console.error('Storage get error:', error)\n      return defaultValue\n    }\n  }\n\n  /**\n   * 删除存储数据\n   * @param {string} key 存储键\n   */\n  static remove(key) {\n    try {\n      uni.removeStorageSync(key)\n      return true\n    } catch (error) {\n      console.error('Storage remove error:', error)\n      return false\n    }\n  }\n\n  /**\n   * 清空所有存储数据\n   */\n  static clear() {\n    try {\n      uni.clearStorageSync()\n      return true\n    } catch (error) {\n      console.error('Storage clear error:', error)\n      return false\n    }\n  }\n\n  // 记账记录相关\n  static getRecords() {\n    return this.get(STORAGE_KEYS.RECORDS, [])\n  }\n\n  static setRecords(records) {\n    return this.set(STORAGE_KEYS.RECORDS, records)\n  }\n\n  // 分类相关\n  static getCategories() {\n    return this.get(STORAGE_KEYS.CATEGORIES, this.getDefaultCategories())\n  }\n\n  static setCategories(categories) {\n    return this.set(STORAGE_KEYS.CATEGORIES, categories)\n  }\n\n  // 设置相关\n  static getSettings() {\n    return this.get(STORAGE_KEYS.SETTINGS, this.getDefaultSettings())\n  }\n\n  static setSettings(settings) {\n    return this.set(STORAGE_KEYS.SETTINGS, settings)\n  }\n\n  // 默认分类\n  static getDefaultCategories() {\n    return {\n      expense: [\n        { id: 1, name: '餐饮', icon: '🍽️', color: '#FF6B6B' },\n        { id: 2, name: '交通', icon: '🚗', color: '#4ECDC4' },\n        { id: 3, name: '购物', icon: '🛍️', color: '#45B7D1' },\n        { id: 4, name: '娱乐', icon: '🎮', color: '#96CEB4' },\n        { id: 5, name: '医疗', icon: '🏥', color: '#FFEAA7' },\n        { id: 6, name: '教育', icon: '📚', color: '#DDA0DD' },\n        { id: 7, name: '住房', icon: '🏠', color: '#98D8C8' },\n        { id: 8, name: '其他', icon: '📝', color: '#F7DC6F' }\n      ],\n      income: [\n        { id: 101, name: '工资', icon: '💰', color: '#2ECC71' },\n        { id: 102, name: '奖金', icon: '🎁', color: '#3498DB' },\n        { id: 103, name: '投资', icon: '📈', color: '#9B59B6' },\n        { id: 104, name: '兼职', icon: '💼', color: '#E67E22' },\n        { id: 105, name: '其他', icon: '📝', color: '#1ABC9C' }\n      ]\n    }\n  }\n\n  // 默认设置\n  static getDefaultSettings() {\n    return {\n      currency: '¥',\n      theme: 'light',\n      budgetAlert: true,\n      monthlyBudget: 0,\n      firstDayOfWeek: 1, // 1: 周一, 0: 周日\n      autoBackup: false\n    }\n  }\n}\n\nexport default Storage\nexport { STORAGE_KEYS }\n"], "names": ["uni"], "mappings": ";;AAIA,MAAM,eAAe;AAAA,EACnB,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AACZ;AAEA,MAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,OAAO,IAAI,KAAK,MAAM;AACpB,QAAI;AACF,YAAM,WAAW,KAAK,UAAU,IAAI;AACpCA,0BAAI,eAAe,KAAK,QAAQ;AAChC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,0BAAA,sBAAsB,KAAK;AACzC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,IAAI,KAAK,eAAe,MAAM;AACnC,QAAI;AACF,YAAM,WAAWA,cAAAA,MAAI,eAAe,GAAG;AACvC,UAAI,UAAU;AACZ,eAAO,KAAK,MAAM,QAAQ;AAAA,MAC3B;AACD,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,0BAAA,sBAAsB,KAAK;AACzC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,OAAO,KAAK;AACjB,QAAI;AACFA,oBAAG,MAAC,kBAAkB,GAAG;AACzB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,+CAAc,yBAAyB,KAAK;AAC5C,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,QAAQ;AACb,QAAI;AACFA,oBAAAA,MAAI,iBAAkB;AACtB,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,0BAAA,wBAAwB,KAAK;AAC3C,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,OAAO,aAAa;AAClB,WAAO,KAAK,IAAI,aAAa,SAAS,CAAA,CAAE;AAAA,EACzC;AAAA,EAED,OAAO,WAAW,SAAS;AACzB,WAAO,KAAK,IAAI,aAAa,SAAS,OAAO;AAAA,EAC9C;AAAA;AAAA,EAGD,OAAO,gBAAgB;AACrB,WAAO,KAAK,IAAI,aAAa,YAAY,KAAK,sBAAsB;AAAA,EACrE;AAAA,EAED,OAAO,cAAc,YAAY;AAC/B,WAAO,KAAK,IAAI,aAAa,YAAY,UAAU;AAAA,EACpD;AAAA;AAAA,EAGD,OAAO,cAAc;AACnB,WAAO,KAAK,IAAI,aAAa,UAAU,KAAK,oBAAoB;AAAA,EACjE;AAAA,EAED,OAAO,YAAY,UAAU;AAC3B,WAAO,KAAK,IAAI,aAAa,UAAU,QAAQ;AAAA,EAChD;AAAA;AAAA,EAGD,OAAO,uBAAuB;AAC5B,WAAO;AAAA,MACL,SAAS;AAAA,QACP,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,UAAW;AAAA,QACpD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACnD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,OAAO,OAAO,UAAW;AAAA,QACpD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACnD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACnD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACnD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACnD,EAAE,IAAI,GAAG,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,MACpD;AAAA,MACD,QAAQ;AAAA,QACN,EAAE,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACrD,EAAE,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACrD,EAAE,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACrD,EAAE,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,QACrD,EAAE,IAAI,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,UAAW;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,OAAO,qBAAqB;AAC1B,WAAO;AAAA,MACL,UAAU;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA;AAAA,MAChB,YAAY;AAAA,IACb;AAAA,EACF;AACH;;"}