'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  try {
    console.log('开始初始化数据库...');

    // 初始化分类数据
    await initCategories();

    return {
      code: 200,
      message: '数据库初始化成功'
    };
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      code: 500,
      message: '数据库初始化失败',
      error: error.message
    };
  }
};

// 初始化分类数据
async function initCategories() {
  // 支出分类
  const expenseCategories = [
    { id: 'exp_food', name: '餐饮', icon: '🍽️', color: '#FF6B6B', type: 'expense', sort: 1 },
    { id: 'exp_transport', name: '交通', icon: '🚗', color: '#4ECDC4', type: 'expense', sort: 2 },
    { id: 'exp_shopping', name: '购物', icon: '🛍️', color: '#45B7D1', type: 'expense', sort: 3 },
    { id: 'exp_other', name: '其他', icon: '📦', color: '#AEB6BF', type: 'expense', sort: 4 }
  ];

  // 收入分类
  const incomeCategories = [
    { id: 'inc_salary', name: '工资', icon: '💰', color: '#2ECC71', type: 'income', sort: 1 },
    { id: 'inc_bonus', name: '奖金', icon: '🎉', color: '#3498DB', type: 'income', sort: 2 },
    { id: 'inc_other', name: '其他', icon: '💎', color: '#34495E', type: 'income', sort: 3 }
  ];

  // 合并所有分类
  const allCategories = [...expenseCategories, ...incomeCategories];

  // 批量插入分类数据
  for (const category of allCategories) {
    try {
      await db.collection('categories').add(category);
    } catch (error) {
      console.log('添加分类失败:', category.name, error.message);
    }
  }

  console.log('成功初始化分类数据');
}
