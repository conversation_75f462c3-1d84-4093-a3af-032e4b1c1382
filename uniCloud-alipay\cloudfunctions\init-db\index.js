'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  try {
    console.log('开始初始化数据库...');
    
    // 初始化分类数据
    await initCategories();
    
    console.log('数据库初始化完成');
    
    return {
      code: 200,
      message: '数据库初始化成功'
    };
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      code: 500,
      message: '数据库初始化失败',
      error: error.message
    };
  }
};

// 初始化分类数据
async function initCategories() {
  // 检查是否已经初始化过
  const existingCategories = await db.collection('categories').count();
  if (existingCategories.total > 0) {
    console.log('分类数据已存在，跳过初始化');
    return;
  }
  
  // 支出分类
  const expenseCategories = [
    { id: 'exp_food', name: '餐饮', icon: '🍽️', color: '#FF6B6B', type: 'expense', sort: 1 },
    { id: 'exp_transport', name: '交通', icon: '🚗', color: '#4ECDC4', type: 'expense', sort: 2 },
    { id: 'exp_shopping', name: '购物', icon: '🛍️', color: '#45B7D1', type: 'expense', sort: 3 },
    { id: 'exp_entertainment', name: '娱乐', icon: '🎮', color: '#96CEB4', type: 'expense', sort: 4 },
    { id: 'exp_medical', name: '医疗', icon: '🏥', color: '#FFEAA7', type: 'expense', sort: 5 },
    { id: 'exp_education', name: '教育', icon: '📚', color: '#DDA0DD', type: 'expense', sort: 6 },
    { id: 'exp_housing', name: '住房', icon: '🏠', color: '#98D8C8', type: 'expense', sort: 7 },
    { id: 'exp_utilities', name: '水电费', icon: '💡', color: '#F7DC6F', type: 'expense', sort: 8 },
    { id: 'exp_communication', name: '通讯', icon: '📱', color: '#BB8FCE', type: 'expense', sort: 9 },
    { id: 'exp_clothing', name: '服饰', icon: '👕', color: '#F8C471', type: 'expense', sort: 10 },
    { id: 'exp_beauty', name: '美容', icon: '💄', color: '#F1948A', type: 'expense', sort: 11 },
    { id: 'exp_social', name: '社交', icon: '🍻', color: '#85C1E9', type: 'expense', sort: 12 },
    { id: 'exp_travel', name: '旅行', icon: '✈️', color: '#82E0AA', type: 'expense', sort: 13 },
    { id: 'exp_pets', name: '宠物', icon: '🐕', color: '#D7BDE2', type: 'expense', sort: 14 },
    { id: 'exp_gifts', name: '礼品', icon: '🎁', color: '#A9DFBF', type: 'expense', sort: 15 },
    { id: 'exp_other', name: '其他', icon: '📦', color: '#AEB6BF', type: 'expense', sort: 16 }
  ];
  
  // 收入分类
  const incomeCategories = [
    { id: 'inc_salary', name: '工资', icon: '💰', color: '#2ECC71', type: 'income', sort: 1 },
    { id: 'inc_bonus', name: '奖金', icon: '🎉', color: '#3498DB', type: 'income', sort: 2 },
    { id: 'inc_investment', name: '投资', icon: '📈', color: '#9B59B6', type: 'income', sort: 3 },
    { id: 'inc_business', name: '生意', icon: '🏢', color: '#E67E22', type: 'income', sort: 4 },
    { id: 'inc_freelance', name: '兼职', icon: '💻', color: '#1ABC9C', type: 'income', sort: 5 },
    { id: 'inc_gift', name: '礼金', icon: '🧧', color: '#E74C3C', type: 'income', sort: 6 },
    { id: 'inc_refund', name: '退款', icon: '↩️', color: '#F39C12', type: 'income', sort: 7 },
    { id: 'inc_rental', name: '租金', icon: '🏠', color: '#27AE60', type: 'income', sort: 8 },
    { id: 'inc_interest', name: '利息', icon: '🏦', color: '#8E44AD', type: 'income', sort: 9 },
    { id: 'inc_other', name: '其他', icon: '💎', color: '#34495E', type: 'income', sort: 10 }
  ];
  
  // 添加创建时间和更新时间
  const now = new Date();
  const allCategories = [...expenseCategories, ...incomeCategories].map(category => ({
    ...category,
    createTime: now,
    updateTime: now
  }));
  
  // 批量插入分类数据
  for (const category of allCategories) {
    await db.collection('categories').add(category);
  }
  
  console.log(`成功初始化 ${allCategories.length} 个分类`);
}
