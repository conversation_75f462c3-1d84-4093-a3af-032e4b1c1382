/**
 * 语音识别服务模块
 * 使用uni-app内置语音识别API
 */

class SpeechService {
  constructor() {
    this.recorderManager = null;
    this.isRecording = false;
    this.recordingStartTime = 0;
    this.maxRecordTime = 60000; // 最大录音时长60秒
  }

  /**
   * 初始化录音管理器
   */
  initRecorderManager() {
    if (!this.recorderManager) {
      this.recorderManager = uni.getRecorderManager();
      this.setupRecorderEvents();
    }
    return this.recorderManager;
  }

  /**
   * 设置录音事件监听
   */
  setupRecorderEvents() {
    // 录音开始事件
    this.recorderManager.onStart(() => {
      console.log('录音开始');
      this.isRecording = true;
      this.recordingStartTime = Date.now();
    });

    // 录音暂停事件
    this.recorderManager.onPause(() => {
      console.log('录音暂停');
    });

    // 录音停止事件
    this.recorderManager.onStop((res) => {
      console.log('录音停止', res);
      this.isRecording = false;
      this.recordingStartTime = 0;
    });

    // 录音错误事件
    this.recorderManager.onError((res) => {
      console.error('录音错误', res);
      this.isRecording = false;
      this.recordingStartTime = 0;
    });
  }

  /**
   * 开始录音
   * @param {Object} options - 录音配置选项
   * @returns {Promise<boolean>} 是否成功开始录音
   */
  async startRecording(options = {}) {
    try {
      // 检查录音权限
      const authResult = await this.checkRecordPermission();
      if (!authResult.success) {
        return {
          success: false,
          error: authResult.error
        };
      }

      this.initRecorderManager();

      const recordOptions = {
        duration: options.duration || this.maxRecordTime,
        sampleRate: options.sampleRate || 16000,
        numberOfChannels: options.numberOfChannels || 1,
        encodeBitRate: options.encodeBitRate || 96000,
        format: options.format || 'mp3',
        frameSize: options.frameSize || 50,
        ...options
      };

      this.recorderManager.start(recordOptions);

      return {
        success: true,
        message: '开始录音'
      };
    } catch (error) {
      console.error('开始录音失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 停止录音
   * @returns {Promise<Object>} 录音结果
   */
  async stopRecording() {
    return new Promise((resolve) => {
      if (!this.recorderManager || !this.isRecording) {
        resolve({
          success: false,
          error: '当前没有进行录音'
        });
        return;
      }

      // 监听录音停止事件
      this.recorderManager.onStop((res) => {
        if (res.tempFilePath) {
          resolve({
            success: true,
            data: {
              tempFilePath: res.tempFilePath,
              duration: res.duration,
              fileSize: res.fileSize
            }
          });
        } else {
          resolve({
            success: false,
            error: '录音文件生成失败'
          });
        }
      });

      // 停止录音
      this.recorderManager.stop();
    });
  }

  /**
   * 暂停录音
   */
  pauseRecording() {
    if (this.recorderManager && this.isRecording) {
      this.recorderManager.pause();
    }
  }

  /**
   * 恢复录音
   */
  resumeRecording() {
    if (this.recorderManager) {
      this.recorderManager.resume();
    }
  }

  /**
   * 检查录音权限
   * @returns {Promise<Object>} 权限检查结果
   */
  async checkRecordPermission() {
    return new Promise((resolve) => {
      uni.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === false) {
            // 用户拒绝了录音权限
            uni.showModal({
              title: '需要录音权限',
              content: '使用语音记账功能需要录音权限，请在设置中开启',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        resolve({ success: true });
                      } else {
                        resolve({ 
                          success: false, 
                          error: '录音权限未开启' 
                        });
                      }
                    }
                  });
                } else {
                  resolve({ 
                    success: false, 
                    error: '用户取消授权' 
                  });
                }
              }
            });
          } else if (res.authSetting['scope.record'] === undefined) {
            // 首次请求权限
            uni.authorize({
              scope: 'scope.record',
              success: () => {
                resolve({ success: true });
              },
              fail: () => {
                resolve({ 
                  success: false, 
                  error: '录音权限被拒绝' 
                });
              }
            });
          } else {
            // 已有权限
            resolve({ success: true });
          }
        },
        fail: () => {
          resolve({ 
            success: false, 
            error: '获取权限设置失败' 
          });
        }
      });
    });
  }

  /**
   * 语音转文字 (使用uni-app内置API)
   * @param {string} filePath - 音频文件路径
   * @returns {Promise<Object>} 识别结果
   */
  async speechToText(filePath) {
    return new Promise((resolve) => {
      // #ifdef MP-WEIXIN
      // 微信小程序语音识别
      const plugin = requirePlugin('WechatSI');
      
      plugin.textToSpeech({
        lang: 'zh_CN',
        tts: true,
        content: filePath,
        success: (res) => {
          resolve({
            success: true,
            data: {
              text: res.result,
              confidence: res.confidence || 0.8
            }
          });
        },
        fail: (error) => {
          console.error('语音识别失败:', error);
          resolve({
            success: false,
            error: error.errMsg || '语音识别失败'
          });
        }
      });
      // #endif

      // #ifdef MP-TOUTIAO
      // 抖音小程序语音识别
      tt.uploadFile({
        url: 'https://your-speech-api-endpoint', // 需要配置语音识别服务端点
        filePath: filePath,
        name: 'audio',
        success: (res) => {
          try {
            const result = JSON.parse(res.data);
            resolve({
              success: true,
              data: {
                text: result.text,
                confidence: result.confidence || 0.8
              }
            });
          } catch (error) {
            resolve({
              success: false,
              error: '解析语音识别结果失败'
            });
          }
        },
        fail: (error) => {
          resolve({
            success: false,
            error: error.errMsg || '语音识别失败'
          });
        }
      });
      // #endif

      // #ifndef MP-WEIXIN || MP-TOUTIAO
      // 其他平台或H5，使用模拟数据
      setTimeout(() => {
        resolve({
          success: false,
          error: '当前平台暂不支持语音识别'
        });
      }, 1000);
      // #endif
    });
  }

  /**
   * 获取录音状态
   * @returns {Object} 录音状态信息
   */
  getRecordingStatus() {
    return {
      isRecording: this.isRecording,
      duration: this.isRecording ? Date.now() - this.recordingStartTime : 0,
      maxDuration: this.maxRecordTime
    };
  }

  /**
   * 播放音频文件
   * @param {string} filePath - 音频文件路径
   * @returns {Promise<Object>} 播放结果
   */
  async playAudio(filePath) {
    return new Promise((resolve) => {
      const audioContext = uni.createInnerAudioContext();
      
      audioContext.src = filePath;
      
      audioContext.onPlay(() => {
        console.log('开始播放');
      });
      
      audioContext.onEnded(() => {
        console.log('播放结束');
        audioContext.destroy();
        resolve({
          success: true,
          message: '播放完成'
        });
      });
      
      audioContext.onError((error) => {
        console.error('播放失败:', error);
        audioContext.destroy();
        resolve({
          success: false,
          error: '播放失败'
        });
      });
      
      audioContext.play();
    });
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.recorderManager) {
      if (this.isRecording) {
        this.recorderManager.stop();
      }
      this.recorderManager = null;
    }
    this.isRecording = false;
    this.recordingStartTime = 0;
  }
}

export default new SpeechService();
