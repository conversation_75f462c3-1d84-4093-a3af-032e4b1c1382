/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-1cf27b2a {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-1cf27b2a {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-1cf27b2a {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-1cf27b2a {
  justify-content: space-between;
}
.flex-center.data-v-1cf27b2a {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-1cf27b2a {
  color: #4CAF50;
}
.text-expense.data-v-1cf27b2a {
  color: #F44336;
}
.text-bold.data-v-1cf27b2a {
  font-weight: bold;
}
.text-center.data-v-1cf27b2a {
  text-align: center;
}
.text-right.data-v-1cf27b2a {
  text-align: right;
}
.home-page.data-v-1cf27b2a {
  padding: 0;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);
  min-height: 100vh;
}

/* 问候语区域 */
.greeting-section.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 16px;
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  color: white;
}
.greeting-content.data-v-1cf27b2a {
  flex: 1;
}
.greeting-title.data-v-1cf27b2a {
  font-size: 24px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}
.greeting-subtitle.data-v-1cf27b2a {
  font-size: 14px;
  opacity: 0.9;
  display: block;
}
.profile-section.data-v-1cf27b2a {
  display: flex;
  align-items: center;
}
.sync-status.data-v-1cf27b2a {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}
.sync-status.syncing.data-v-1cf27b2a {
  animation: spin-1cf27b2a 1s linear infinite;
}
.sync-status.offline.data-v-1cf27b2a {
  background: rgba(255, 87, 87, 0.3);
}
.sync-icon.data-v-1cf27b2a {
  font-size: 18px;
}

/* 主要统计卡片 */
.main-stats-card.data-v-1cf27b2a {
  margin: -20px 16px 24px;
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.balance-section.data-v-1cf27b2a {
  text-align: center;
}
.balance-header.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  gap: 8px;
}
.balance-label.data-v-1cf27b2a {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}
.trend-indicator.data-v-1cf27b2a {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}
.trend-indicator.up.data-v-1cf27b2a {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}
.trend-indicator.down.data-v-1cf27b2a {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}
.trend-indicator.stable.data-v-1cf27b2a {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}
.trend-icon.data-v-1cf27b2a {
  font-size: 14px;
}
.balance-amount.data-v-1cf27b2a {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8px;
}
.currency.data-v-1cf27b2a {
  font-size: 20px;
  color: #666;
  margin-right: 4px;
}
.amount.data-v-1cf27b2a {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  letter-spacing: -1px;
}
.amount.negative.data-v-1cf27b2a {
  color: #f44336;
}
.balance-change.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.change-text.data-v-1cf27b2a {
  font-size: 14px;
  font-weight: 600;
}
.change-text.positive.data-v-1cf27b2a {
  color: #4CAF50;
}
.change-text.negative.data-v-1cf27b2a {
  color: #f44336;
}
.change-label.data-v-1cf27b2a {
  font-size: 12px;
  color: #999;
}

/* 收支统计卡片 */
.income-expense-cards.data-v-1cf27b2a {
  display: flex;
  gap: 12px;
  padding: 0 16px;
  margin-bottom: 24px;
}
.stat-card.data-v-1cf27b2a {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}
.stat-card.data-v-1cf27b2a:active {
  transform: scale(0.98);
}
.card-header.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}
.card-icon.data-v-1cf27b2a {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}
.income-icon.data-v-1cf27b2a {
  background: rgba(76, 175, 80, 0.1);
}
.expense-icon.data-v-1cf27b2a {
  background: rgba(244, 67, 54, 0.1);
}
.card-title.data-v-1cf27b2a {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}
.card-amount.data-v-1cf27b2a {
  margin-bottom: 12px;
}
.amount-text.data-v-1cf27b2a {
  font-size: 20px;
  font-weight: 700;
  display: block;
}
.income .amount-text.data-v-1cf27b2a {
  color: #4CAF50;
}
.expense .amount-text.data-v-1cf27b2a {
  color: #f44336;
}
.card-progress.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.progress-bar.data-v-1cf27b2a {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}
.progress-fill.data-v-1cf27b2a {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}
.income-progress.data-v-1cf27b2a {
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
}
.expense-progress.data-v-1cf27b2a {
  background: linear-gradient(90deg, #f44336, #EF5350);
}
.progress-text.data-v-1cf27b2a {
  font-size: 12px;
  color: #999;
}

/* 快速操作区域 */
.quick-actions-section.data-v-1cf27b2a {
  padding: 0 16px;
  margin-bottom: 24px;
}
.section-title.data-v-1cf27b2a {
  margin-bottom: 16px;
}
.title-text.data-v-1cf27b2a {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.title-subtitle.data-v-1cf27b2a {
  font-size: 14px;
  color: #666;
  display: block;
}
.quick-actions.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.quick-btn.data-v-1cf27b2a {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.quick-btn.data-v-1cf27b2a::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}
.quick-btn.data-v-1cf27b2a:active::before {
  left: 100%;
}
.quick-btn.data-v-1cf27b2a:active {
  transform: scale(0.98);
}
.expense-btn.data-v-1cf27b2a {
  border-left: 4px solid #f44336;
}
.income-btn.data-v-1cf27b2a {
  border-left: 4px solid #4CAF50;
}
.btn-content.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  gap: 16px;
}
.btn-icon.data-v-1cf27b2a {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.expense-icon.data-v-1cf27b2a {
  background: rgba(244, 67, 54, 0.1);
}
.income-icon.data-v-1cf27b2a {
  background: rgba(76, 175, 80, 0.1);
}
.btn-text.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.btn-title.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}
.btn-subtitle.data-v-1cf27b2a {
  font-size: 12px;
  color: #666;
  display: block;
}
.btn-arrow.data-v-1cf27b2a {
  font-size: 18px;
  color: #ccc;
  font-weight: bold;
}

/* 最近记录区域 */
.recent-section.data-v-1cf27b2a {
  padding: 0 16px;
  margin-bottom: 24px;
}
.section-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.header-left.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.section-title.data-v-1cf27b2a {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.section-subtitle.data-v-1cf27b2a {
  font-size: 12px;
  color: #666;
}
.header-right.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 20px;
  background: rgba(76, 175, 80, 0.1);
  transition: all 0.3s ease;
}
.header-right.data-v-1cf27b2a:active {
  background: rgba(76, 175, 80, 0.2);
  transform: scale(0.95);
}
.more-text.data-v-1cf27b2a {
  font-size: 14px;
  color: #4CAF50;
  font-weight: 500;
}
.more-arrow.data-v-1cf27b2a {
  font-size: 12px;
  color: #4CAF50;
  font-weight: bold;
}
.recent-list.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.record-item.data-v-1cf27b2a {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  animation: slideInUp-1cf27b2a 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}
.record-item.data-v-1cf27b2a:active {
  transform: scale(0.98);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.record-left.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}
.record-icon-wrapper.data-v-1cf27b2a {
  position: relative;
}
.record-icon.data-v-1cf27b2a {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.icon-text.data-v-1cf27b2a {
  font-size: 20px;
  color: white;
}
.type-indicator.data-v-1cf27b2a {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 8px;
  border: 2px solid white;
}
.type-indicator.income.data-v-1cf27b2a {
  background: #4CAF50;
}
.type-indicator.expense.data-v-1cf27b2a {
  background: #f44336;
}
.record-info.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}
.record-category.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
}
.record-note.data-v-1cf27b2a {
  font-size: 12px;
  color: #666;
  display: block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.record-time.data-v-1cf27b2a {
  font-size: 12px;
  color: #999;
  display: block;
}
.record-right.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}
.record-amount.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 700;
}
.record-amount.income.data-v-1cf27b2a {
  color: #4CAF50;
}
.record-amount.expense.data-v-1cf27b2a {
  color: #f44336;
}
.edit-indicator.data-v-1cf27b2a {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}
.record-item:active .edit-indicator.data-v-1cf27b2a {
  opacity: 1;
}
.edit-icon.data-v-1cf27b2a {
  font-size: 12px;
}

/* 空状态 */
.empty-state.data-v-1cf27b2a {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}
.empty-icon.data-v-1cf27b2a {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}
.empty-text.data-v-1cf27b2a {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}
.empty-hint.data-v-1cf27b2a {
  font-size: 14px;
  color: #999;
  display: block;
}

/* 动画效果 */
@keyframes spin-1cf27b2a {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes slideInDown-1cf27b2a {
from {
    opacity: 0;
    transform: translateY(-30px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes slideInLeft-1cf27b2a {
from {
    opacity: 0;
    transform: translateX(-30px);
}
to {
    opacity: 1;
    transform: translateX(0);
}
}
@keyframes slideInRight-1cf27b2a {
from {
    opacity: 0;
    transform: translateX(30px);
}
to {
    opacity: 1;
    transform: translateX(0);
}
}
@keyframes slideInUp-1cf27b2a {
from {
    opacity: 0;
    transform: translateY(20px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes bounceIn-1cf27b2a {
0% {
    opacity: 0;
    transform: scale(0.3);
}
50% {
    opacity: 1;
    transform: scale(1.05);
}
70% {
    transform: scale(0.9);
}
100% {
    opacity: 1;
    transform: scale(1);
}
}
@keyframes fadeInUp-1cf27b2a {
from {
    opacity: 0;
    transform: translateY(30px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
/* 应用动画 */
.slide-in-down.data-v-1cf27b2a {
  animation: slideInDown-1cf27b2a 0.6s ease-out;
}
.slide-in-left.data-v-1cf27b2a {
  animation: slideInLeft-1cf27b2a 0.6s ease-out 0.2s both;
}
.slide-in-right.data-v-1cf27b2a {
  animation: slideInRight-1cf27b2a 0.6s ease-out 0.3s both;
}
.bounce-in.data-v-1cf27b2a {
  animation: bounceIn-1cf27b2a 0.8s ease-out 0.4s both;
}
.fade-in-up.data-v-1cf27b2a {
  animation: fadeInUp-1cf27b2a 0.6s ease-out 0.5s both;
}

/* 测试按钮样式 */
.test-btn.data-v-1cf27b2a, .test-page-btn.data-v-1cf27b2a {
  padding: 6px 12px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 12px;
  margin-left: 8px;
  transition: all 0.3s ease;
  margin-left: 8px;
}
.test-page-btn.data-v-1cf27b2a {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
}
.test-btn.data-v-1cf27b2a:active, .test-page-btn.data-v-1cf27b2a:active {
  background: rgba(255, 193, 7, 0.2);
  transform: scale(0.95);
}
.test-page-btn.data-v-1cf27b2a:active {
  background: rgba(33, 150, 243, 0.2);
}
.test-text.data-v-1cf27b2a {
  font-size: 12px;
  color: #FFC107;
  font-weight: 500;
}
.test-page-btn .test-text.data-v-1cf27b2a {
  color: #2196F3;
}