/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 记账专用颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 记账本专用样式 */
/* 阴影 */
/* 通用样式类 */
.container.data-v-1cf27b2a {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.card.data-v-1cf27b2a {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  padding: 16px;
}
.flex-row.data-v-1cf27b2a {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column.data-v-1cf27b2a {
  display: flex;
  flex-direction: column;
}
.flex-between.data-v-1cf27b2a {
  justify-content: space-between;
}
.flex-center.data-v-1cf27b2a {
  justify-content: center;
  align-items: center;
}
.text-income.data-v-1cf27b2a {
  color: #4CAF50;
}
.text-expense.data-v-1cf27b2a {
  color: #F44336;
}
.text-bold.data-v-1cf27b2a {
  font-weight: bold;
}
.text-center.data-v-1cf27b2a {
  text-align: center;
}
.text-right.data-v-1cf27b2a {
  text-align: right;
}
.home-page.data-v-1cf27b2a {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 统计卡片 */
.stats-card.data-v-1cf27b2a {
  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}
.stats-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.stats-title.data-v-1cf27b2a {
  color: white;
  font-size: 18px;
  font-weight: 600;
}
.stats-date.data-v-1cf27b2a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}
.stats-content.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.stats-item.data-v-1cf27b2a {
  flex: 1;
  text-align: center;
}
.stats-label.data-v-1cf27b2a {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 8px;
}
.stats-amount.data-v-1cf27b2a {
  color: white;
  font-size: 20px;
  font-weight: bold;
}
.stats-amount.negative.data-v-1cf27b2a {
  color: #FFE0E0;
}
.stats-divider.data-v-1cf27b2a {
  width: 1px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 0 16px;
}

/* 快速操作 */
.quick-actions.data-v-1cf27b2a {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}
.quick-btn.data-v-1cf27b2a {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.quick-btn.data-v-1cf27b2a:active {
  transform: scale(0.98);
}
.expense-btn.data-v-1cf27b2a {
  border-left: 4px solid #F44336;
}
.income-btn.data-v-1cf27b2a {
  border-left: 4px solid #4CAF50;
}
.quick-icon.data-v-1cf27b2a {
  font-size: 32px;
  margin-bottom: 8px;
}
.quick-text.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 最近记录 */
.recent-section.data-v-1cf27b2a {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.section-header.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
}
.section-title.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.section-more.data-v-1cf27b2a {
  font-size: 14px;
  color: #4CAF50;
}
.recent-list .record-item.data-v-1cf27b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e5e5;
  transition: background-color 0.3s ease;
}
.recent-list .record-item.data-v-1cf27b2a:last-child {
  border-bottom: none;
}
.recent-list .record-item.data-v-1cf27b2a:active {
  background-color: #f1f1f1;
}
.record-left.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  flex: 1;
}
.record-icon.data-v-1cf27b2a {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}
.record-info.data-v-1cf27b2a {
  flex: 1;
}
.record-category.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}
.record-note.data-v-1cf27b2a {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 2px;
}
.record-time.data-v-1cf27b2a {
  font-size: 12px;
  color: #808080;
}
.record-right.data-v-1cf27b2a {
  text-align: right;
}
.record-amount.data-v-1cf27b2a {
  font-size: 16px;
  font-weight: 600;
}
.record-amount.income.data-v-1cf27b2a {
  color: #4CAF50;
}
.record-amount.expense.data-v-1cf27b2a {
  color: #F44336;
}

/* 空状态 */
.empty-state.data-v-1cf27b2a {
  padding: 40px 20px;
  text-align: center;
}
.empty-icon.data-v-1cf27b2a {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}
.empty-text.data-v-1cf27b2a {
  font-size: 16px;
  color: #999;
  margin-bottom: 8px;
  display: block;
}
.empty-desc.data-v-1cf27b2a {
  font-size: 14px;
  color: #808080;
}