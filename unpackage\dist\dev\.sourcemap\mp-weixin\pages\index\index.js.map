{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"home-page\">\r\n    <!-- 顶部统计卡片 -->\r\n    <view class=\"stats-card fade-in\">\r\n      <view class=\"stats-header\">\r\n        <text class=\"stats-title\">本月概览</text>\r\n        <text class=\"stats-date\">{{ currentMonth }}</text>\r\n      </view>\r\n\r\n      <view class=\"stats-content\">\r\n        <view class=\"stats-item income\">\r\n          <view class=\"stats-label\">收入</view>\r\n          <view class=\"stats-amount\">{{ formatAmount(monthlyIncome) }}</view>\r\n        </view>\r\n\r\n        <view class=\"stats-divider\"></view>\r\n\r\n        <view class=\"stats-item expense\">\r\n          <view class=\"stats-label\">支出</view>\r\n          <view class=\"stats-amount\">{{ formatAmount(monthlyExpense) }}</view>\r\n        </view>\r\n\r\n        <view class=\"stats-divider\"></view>\r\n\r\n        <view class=\"stats-item balance\">\r\n          <view class=\"stats-label\">结余</view>\r\n          <view class=\"stats-amount\" :class=\"{ 'negative': monthlyBalance < 0 }\">\r\n            {{ formatAmount(monthlyBalance) }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快速记账按钮 -->\r\n    <view class=\"quick-actions slide-in-up\">\r\n      <view class=\"quick-btn expense-btn\" @click=\"quickAdd('expense')\">\r\n        <view class=\"quick-icon\">💸</view>\r\n        <text class=\"quick-text\">支出</text>\r\n      </view>\r\n\r\n      <view class=\"quick-btn income-btn\" @click=\"quickAdd('income')\">\r\n        <view class=\"quick-icon\">💰</view>\r\n        <text class=\"quick-text\">收入</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 最近记录 -->\r\n    <view class=\"recent-section bounce-in\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">最近记录</text>\r\n        <text class=\"section-more\" @click=\"goToList\">查看全部</text>\r\n      </view>\r\n\r\n      <view class=\"recent-list\" v-if=\"recentRecords.length > 0\">\r\n        <view\r\n          class=\"record-item\"\r\n          v-for=\"record in recentRecords\"\r\n          :key=\"record.id\"\r\n          @click=\"editRecord(record)\"\r\n        >\r\n          <view class=\"record-left\">\r\n            <view class=\"record-icon\" :style=\"{ backgroundColor: record.categoryColor }\">\r\n              {{ record.categoryIcon }}\r\n            </view>\r\n            <view class=\"record-info\">\r\n              <text class=\"record-category\">{{ record.categoryName }}</text>\r\n              <text class=\"record-note\" v-if=\"record.note\">{{ record.note }}</text>\r\n              <text class=\"record-time\">{{ formatDateTime(record.date, record.time) }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"record-right\">\r\n            <text class=\"record-amount\" :class=\"record.type\">\r\n              {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}\r\n            </text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"empty-state\" v-else>\r\n        <view class=\"empty-icon\">📝</view>\r\n        <text class=\"empty-text\">暂无记录</text>\r\n        <text class=\"empty-desc\">点击下方按钮开始记账吧</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { computed, onMounted } from 'vue'\r\nimport store from '../../store/index.js'\r\nimport { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/helpers.js'\r\n\r\nexport default {\r\n  name: 'HomePage',\r\n  setup() {\r\n    // 当前月份\r\n    const currentMonth = computed(() => {\r\n      const now = new Date()\r\n      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`\r\n    })\r\n\r\n    // 本月记录\r\n    const monthlyRecords = computed(() => {\r\n      const start = getFirstDayOfMonth()\r\n      const end = getLastDayOfMonth()\r\n      return store.state.records.filter(record =>\r\n        record.date >= start && record.date <= end\r\n      )\r\n    })\r\n\r\n    // 本月收入\r\n    const monthlyIncome = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'income')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月支出\r\n    const monthlyExpense = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'expense')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月结余\r\n    const monthlyBalance = computed(() => monthlyIncome.value - monthlyExpense.value)\r\n\r\n    // 最近记录（最多显示5条）\r\n    const recentRecords = computed(() =>\r\n      store.state.records\r\n        .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))\r\n        .slice(0, 5)\r\n    )\r\n\r\n    // 格式化日期时间\r\n    const formatDateTime = (date, time) => {\r\n      const today = formatDate(new Date())\r\n      const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))\r\n\r\n      if (date === today) {\r\n        return `今天 ${time}`\r\n      } else if (date === yesterday) {\r\n        return `昨天 ${time}`\r\n      } else {\r\n        return `${formatDate(date, 'MM-DD')} ${time}`\r\n      }\r\n    }\r\n\r\n    // 快速记账\r\n    const quickAdd = (type) => {\r\n      uni.navigateTo({\r\n        url: `/pages/add/add?type=${type}`\r\n      })\r\n    }\r\n\r\n    // 编辑记录\r\n    const editRecord = (record) => {\r\n      uni.navigateTo({\r\n        url: `/pages/add/add?id=${record.id}`\r\n      })\r\n    }\r\n\r\n    // 跳转到账单列表\r\n    const goToList = () => {\r\n      uni.switchTab({\r\n        url: '/pages/list/list'\r\n      })\r\n    }\r\n\r\n    onMounted(() => {\r\n      // 页面加载时刷新数据\r\n      store.actions.loadRecords()\r\n    })\r\n\r\n    return {\r\n      currentMonth,\r\n      monthlyIncome,\r\n      monthlyExpense,\r\n      monthlyBalance,\r\n      recentRecords,\r\n      formatAmount,\r\n      formatDateTime,\r\n      quickAdd,\r\n      editRecord,\r\n      goToList\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-page {\r\n  padding: 16px;\r\n  background-color: $uni-bg-color-grey;\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 统计卡片 */\r\n.stats-card {\r\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\r\n  border-radius: 16px;\r\n  padding: 24px;\r\n  margin-bottom: 24px;\r\n  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);\r\n}\r\n\r\n.stats-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-title {\r\n  color: white;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.stats-date {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 14px;\r\n}\r\n\r\n.stats-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.stats-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stats-label {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.stats-amount {\r\n  color: white;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n\r\n  &.negative {\r\n    color: #FFE0E0;\r\n  }\r\n}\r\n\r\n.stats-divider {\r\n  width: 1px;\r\n  height: 40px;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  margin: 0 16px;\r\n}\r\n\r\n/* 快速操作 */\r\n.quick-actions {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.quick-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  background-color: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.98);\r\n  }\r\n}\r\n\r\n.expense-btn {\r\n  border-left: 4px solid #F44336;\r\n}\r\n\r\n.income-btn {\r\n  border-left: 4px solid #4CAF50;\r\n}\r\n\r\n.quick-icon {\r\n  font-size: 32px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.quick-text {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: $uni-text-color;\r\n}\r\n\r\n/* 最近记录 */\r\n.recent-section {\r\n  background-color: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 20px;\r\n  border-bottom: 1px solid $uni-border-color;\r\n}\r\n\r\n.section-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: $uni-text-color;\r\n}\r\n\r\n.section-more {\r\n  font-size: 14px;\r\n  color: $uni-color-primary;\r\n}\r\n\r\n.recent-list {\r\n  .record-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 16px 20px;\r\n    border-bottom: 1px solid $uni-border-color;\r\n    transition: background-color 0.3s ease;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    &:active {\r\n      background-color: $uni-bg-color-hover;\r\n    }\r\n  }\r\n}\r\n\r\n.record-left {\r\n  display: flex;\r\n  align-items: center;\r\n  flex: 1;\r\n}\r\n\r\n.record-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 18px;\r\n  margin-right: 12px;\r\n}\r\n\r\n.record-info {\r\n  flex: 1;\r\n}\r\n\r\n.record-category {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: $uni-text-color;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.record-note {\r\n  font-size: 12px;\r\n  color: $uni-text-color-grey;\r\n  display: block;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.record-time {\r\n  font-size: 12px;\r\n  color: $uni-text-color-placeholder;\r\n}\r\n\r\n.record-right {\r\n  text-align: right;\r\n}\r\n\r\n.record-amount {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n\r\n  &.income {\r\n    color: $income-color;\r\n  }\r\n\r\n  &.expense {\r\n    color: $expense-color;\r\n  }\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: $uni-text-color-grey;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.empty-desc {\r\n  font-size: 14px;\r\n  color: $uni-text-color-placeholder;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "getFirstDayOfMonth", "getLastDayOfMonth", "store", "formatDate", "uni", "onMounted", "formatAmount"], "mappings": ";;;;AA6FA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,eAAeA,cAAAA,SAAS,MAAM;AAClC,YAAM,MAAM,oBAAI,KAAK;AACrB,aAAO,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,KAC3E;AAGD,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,QAAQC,cAAAA,mBAAmB;AACjC,YAAM,MAAMC,cAAAA,kBAAkB;AAC9B,aAAOC,kBAAM,MAAM,QAAQ;AAAA,QAAO,YAChC,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAAA,MACzC;AAAA,KACD;AAGD,UAAM,gBAAgBH,cAAAA;AAAAA,MAAS,MAC7B,eAAe,MACZ,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,iBAAiBA,cAAAA;AAAAA,MAAS,MAC9B,eAAe,MACZ,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,cAAc,QAAQ,eAAe,KAAK;AAGhF,UAAM,gBAAgBA,cAAAA;AAAAA,MAAS,MAC7BG,YAAK,MAAC,MAAM,QACT,KAAK,CAAC,GAAG,MAAM,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,IAAI,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,CAAC,EAChF,MAAM,GAAG,CAAC;AAAA,IACf;AAGA,UAAM,iBAAiB,CAAC,MAAM,SAAS;AACrC,YAAM,QAAQC,cAAAA,WAAW,oBAAI,MAAM;AACnC,YAAM,YAAYA,cAAAA,WAAW,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI,CAAC;AAEvE,UAAI,SAAS,OAAO;AAClB,eAAO,MAAM,IAAI;AAAA,iBACR,SAAS,WAAW;AAC7B,eAAO,MAAM,IAAI;AAAA,aACZ;AACL,eAAO,GAAGA,cAAAA,WAAW,MAAM,OAAO,CAAC,IAAI,IAAI;AAAA,MAC7C;AAAA,IACF;AAGA,UAAM,WAAW,CAAC,SAAS;AACzBC,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,uBAAuB,IAAI;AAAA,OACjC;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,WAAW;AAC7BA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qBAAqB,OAAO,EAAE;AAAA,OACpC;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAEAC,kBAAAA,UAAU,MAAM;AAEdH,kBAAK,MAAC,QAAQ,YAAY;AAAA,KAC3B;AAED,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,oBACAI,cAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3LA,GAAG,WAAW,eAAe;"}