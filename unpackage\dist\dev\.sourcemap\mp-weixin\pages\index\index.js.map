{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"home-page\">\r\n    <!-- 顶部问候语 -->\r\n    <view class=\"greeting-section\">\r\n      <view class=\"greeting-content\">\r\n        <text class=\"greeting-title\">{{ greetingText }}</text>\r\n        <text class=\"greeting-subtitle\">{{ currentDateText }}</text>\r\n      </view>\r\n      <view class=\"profile-section\">\r\n        <view class=\"sync-status\" :class=\"{ syncing: state.syncing, offline: !state.isOnline }\">\r\n          <text class=\"sync-icon\">{{ syncStatusIcon }}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要统计卡片 -->\r\n    <view class=\"main-stats-card slide-in-down\">\r\n      <view class=\"balance-section\">\r\n        <view class=\"balance-header\">\r\n          <text class=\"balance-label\">本月结余</text>\r\n          <view class=\"trend-indicator\" :class=\"balanceTrend\">\r\n            <text class=\"trend-icon\">{{ balanceTrendIcon }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"balance-amount\">\r\n          <text class=\"currency\">¥</text>\r\n          <text class=\"amount\" :class=\"{ negative: monthlyBalance < 0 }\">\r\n            {{ formatAmount(Math.abs(monthlyBalance)) }}\r\n          </text>\r\n        </view>\r\n        <view class=\"balance-change\" v-if=\"balanceChange !== 0\">\r\n          <text class=\"change-text\" :class=\"{ positive: balanceChange > 0, negative: balanceChange < 0 }\">\r\n            {{ balanceChange > 0 ? '+' : '' }}{{ formatAmount(Math.abs(balanceChange)) }}\r\n          </text>\r\n          <text class=\"change-label\">较上月</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 收支统计卡片 -->\r\n    <view class=\"income-expense-cards\">\r\n      <view class=\"stat-card income-card slide-in-left\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-icon income-icon\">📈</view>\r\n          <text class=\"card-title\">收入</text>\r\n        </view>\r\n        <view class=\"card-amount income\">\r\n          <text class=\"amount-text\">{{ formatAmount(monthlyIncome) }}</text>\r\n        </view>\r\n        <view class=\"card-progress\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill income-progress\" :style=\"{ width: incomeProgress + '%' }\"></view>\r\n          </view>\r\n          <text class=\"progress-text\">{{ incomeTransactions }}笔交易</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"stat-card expense-card slide-in-right\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-icon expense-icon\">📉</view>\r\n          <text class=\"card-title\">支出</text>\r\n        </view>\r\n        <view class=\"card-amount expense\">\r\n          <text class=\"amount-text\">{{ formatAmount(monthlyExpense) }}</text>\r\n        </view>\r\n        <view class=\"card-progress\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill expense-progress\" :style=\"{ width: expenseProgress + '%' }\"></view>\r\n          </view>\r\n          <text class=\"progress-text\">{{ expenseTransactions }}笔交易</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快速操作区域 -->\r\n    <view class=\"quick-actions-section\">\r\n      <view class=\"section-title\">\r\n        <text class=\"title-text\">快速记账</text>\r\n        <text class=\"title-subtitle\">点击快速添加收支记录</text>\r\n      </view>\r\n\r\n      <view class=\"quick-actions bounce-in\">\r\n        <view class=\"quick-btn expense-btn\" @click=\"quickAdd('expense')\">\r\n          <view class=\"btn-content\">\r\n            <view class=\"btn-icon expense-icon\">💸</view>\r\n            <view class=\"btn-text\">\r\n              <text class=\"btn-title\">记支出</text>\r\n              <text class=\"btn-subtitle\">日常消费</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"btn-arrow\">→</view>\r\n        </view>\r\n\r\n        <view class=\"quick-btn income-btn\" @click=\"quickAdd('income')\">\r\n          <view class=\"btn-content\">\r\n            <view class=\"btn-icon income-icon\">💰</view>\r\n            <view class=\"btn-text\">\r\n              <text class=\"btn-title\">记收入</text>\r\n              <text class=\"btn-subtitle\">工资奖金</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"btn-arrow\">→</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 最近记录 -->\r\n    <view class=\"recent-section fade-in-up\">\r\n      <view class=\"section-header\">\r\n        <view class=\"header-left\">\r\n          <text class=\"section-title\">最近记录</text>\r\n          <text class=\"section-subtitle\">{{ recentRecords.length }}条记录</text>\r\n        </view>\r\n        <view class=\"header-right\" @click=\"goToList\">\r\n          <text class=\"more-text\">查看全部</text>\r\n          <text class=\"more-arrow\">→</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"recent-list\" v-if=\"recentRecords.length > 0\">\r\n        <view\r\n          class=\"record-item\"\r\n          v-for=\"(record, index) in recentRecords\"\r\n          :key=\"record.id\"\r\n          :style=\"{ animationDelay: (index * 0.1) + 's' }\"\r\n          @click=\"editRecord(record)\"\r\n        >\r\n          <view class=\"record-left\">\r\n            <view class=\"record-icon-wrapper\">\r\n              <view class=\"record-icon\" :style=\"{ backgroundColor: record.categoryColor }\">\r\n                <text class=\"icon-text\">{{ record.categoryIcon }}</text>\r\n              </view>\r\n              <view class=\"type-indicator\" :class=\"record.type\"></view>\r\n            </view>\r\n            <view class=\"record-info\">\r\n              <text class=\"record-category\">{{ record.categoryName }}</text>\r\n              <text class=\"record-note\" v-if=\"record.note\">{{ record.note }}</text>\r\n              <text class=\"record-time\">{{ formatDateTime(record.date, record.time) }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"record-right\">\r\n            <text class=\"record-amount\" :class=\"record.type\">\r\n              {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}\r\n            </text>\r\n            <view class=\"edit-indicator\">\r\n              <text class=\"edit-icon\">✏️</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"empty-state\" v-else>\r\n        <view class=\"empty-icon\">📝</view>\r\n        <text class=\"empty-text\">暂无记录</text>\r\n        <text class=\"empty-desc\">点击下方按钮开始记账吧</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { computed, onMounted } from 'vue'\r\nimport store from '../../store/index.js'\r\nimport { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/helpers.js'\r\n\r\nexport default {\r\n  name: 'HomePage',\r\n  setup() {\r\n    // 响应式状态\r\n    const state = computed(() => store.state)\r\n\r\n    // 问候语\r\n    const greetingText = computed(() => {\r\n      const hour = new Date().getHours()\r\n      if (hour < 6) return '夜深了'\r\n      if (hour < 12) return '早上好'\r\n      if (hour < 18) return '下午好'\r\n      return '晚上好'\r\n    })\r\n\r\n    // 当前日期文本\r\n    const currentDateText = computed(() => {\r\n      const now = new Date()\r\n      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n      return `${now.getMonth() + 1}月${now.getDate()}日 ${weekdays[now.getDay()]}`\r\n    })\r\n\r\n    // 同步状态图标\r\n    const syncStatusIcon = computed(() => {\r\n      if (state.value.syncing) return '🔄'\r\n      if (!state.value.isOnline) return '📴'\r\n      if (state.value.useCloudStorage) return '☁️'\r\n      return '📱'\r\n    })\r\n\r\n    // 当前月份\r\n    const currentMonth = computed(() => {\r\n      const now = new Date()\r\n      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`\r\n    })\r\n\r\n    // 本月记录\r\n    const monthlyRecords = computed(() => {\r\n      const start = getFirstDayOfMonth()\r\n      const end = getLastDayOfMonth()\r\n      return store.state.records.filter(record =>\r\n        record.date >= start && record.date <= end\r\n      )\r\n    })\r\n\r\n    // 上月记录（用于对比）\r\n    const lastMonthRecords = computed(() => {\r\n      const now = new Date()\r\n      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)\r\n      const start = formatDate(lastMonth)\r\n      const end = formatDate(new Date(now.getFullYear(), now.getMonth(), 0))\r\n      return store.state.records.filter(record =>\r\n        record.date >= start && record.date <= end\r\n      )\r\n    })\r\n\r\n    // 本月收入\r\n    const monthlyIncome = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'income')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月支出\r\n    const monthlyExpense = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'expense')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月结余\r\n    const monthlyBalance = computed(() => monthlyIncome.value - monthlyExpense.value)\r\n\r\n    // 上月结余\r\n    const lastMonthBalance = computed(() => {\r\n      const income = lastMonthRecords.value\r\n        .filter(record => record.type === 'income')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n      const expense = lastMonthRecords.value\r\n        .filter(record => record.type === 'expense')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n      return income - expense\r\n    })\r\n\r\n    // 结余变化\r\n    const balanceChange = computed(() => monthlyBalance.value - lastMonthBalance.value)\r\n\r\n    // 结余趋势\r\n    const balanceTrend = computed(() => {\r\n      if (balanceChange.value > 0) return 'up'\r\n      if (balanceChange.value < 0) return 'down'\r\n      return 'stable'\r\n    })\r\n\r\n    // 趋势图标\r\n    const balanceTrendIcon = computed(() => {\r\n      if (balanceChange.value > 0) return '📈'\r\n      if (balanceChange.value < 0) return '📉'\r\n      return '➖'\r\n    })\r\n\r\n    // 收入交易数量\r\n    const incomeTransactions = computed(() =>\r\n      monthlyRecords.value.filter(record => record.type === 'income').length\r\n    )\r\n\r\n    // 支出交易数量\r\n    const expenseTransactions = computed(() =>\r\n      monthlyRecords.value.filter(record => record.type === 'expense').length\r\n    )\r\n\r\n    // 收入进度（相对于最大值）\r\n    const incomeProgress = computed(() => {\r\n      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)\r\n      return maxAmount > 0 ? (monthlyIncome.value / maxAmount) * 100 : 0\r\n    })\r\n\r\n    // 支出进度（相对于最大值）\r\n    const expenseProgress = computed(() => {\r\n      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)\r\n      return maxAmount > 0 ? (monthlyExpense.value / maxAmount) * 100 : 0\r\n    })\r\n\r\n    // 最近记录（最多显示5条）\r\n    const recentRecords = computed(() =>\r\n      store.state.records\r\n        .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))\r\n        .slice(0, 5)\r\n    )\r\n\r\n    // 格式化日期时间\r\n    const formatDateTime = (date, time) => {\r\n      const today = formatDate(new Date())\r\n      const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))\r\n\r\n      if (date === today) {\r\n        return `今天 ${time}`\r\n      } else if (date === yesterday) {\r\n        return `昨天 ${time}`\r\n      } else {\r\n        return `${formatDate(date, 'MM-DD')} ${time}`\r\n      }\r\n    }\r\n\r\n    // 快速记账\r\n    const quickAdd = (type) => {\r\n      // 由于记账页面是tabbar页面，需要使用switchTab\r\n      // 但switchTab不支持传参，所以先存储类型到本地\r\n      uni.setStorageSync('quickAddType', type)\r\n      uni.switchTab({\r\n        url: '/pages/add/add'\r\n      })\r\n    }\r\n\r\n    // 编辑记录\r\n    const editRecord = (record) => {\r\n      // 编辑记录需要传递参数，但switchTab不支持传参\r\n      // 先存储记录ID到本地，然后跳转\r\n      uni.setStorageSync('editRecordId', record.id)\r\n      uni.switchTab({\r\n        url: '/pages/add/add'\r\n      })\r\n    }\r\n\r\n    // 跳转到账单列表\r\n    const goToList = () => {\r\n      // list页面不是tabbar页面，使用navigateTo\r\n      uni.navigateTo({\r\n        url: '/pages/list/list'\r\n      })\r\n    }\r\n\r\n    onMounted(() => {\r\n      // 页面加载时刷新数据\r\n      store.actions.loadRecords()\r\n    })\r\n\r\n    return {\r\n      // 状态\r\n      state,\r\n      // 问候语\r\n      greetingText,\r\n      currentDateText,\r\n      syncStatusIcon,\r\n      // 统计数据\r\n      currentMonth,\r\n      monthlyIncome,\r\n      monthlyExpense,\r\n      monthlyBalance,\r\n      balanceChange,\r\n      balanceTrend,\r\n      balanceTrendIcon,\r\n      incomeTransactions,\r\n      expenseTransactions,\r\n      incomeProgress,\r\n      expenseProgress,\r\n      // 记录列表\r\n      recentRecords,\r\n      // 工具函数\r\n      formatAmount,\r\n      formatDateTime,\r\n      // 操作方法\r\n      quickAdd,\r\n      editRecord,\r\n      goToList\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-page {\r\n  padding: 0;\r\n  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 问候语区域 */\r\n.greeting-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 16px 16px;\r\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\r\n  color: white;\r\n}\r\n\r\n.greeting-content {\r\n  flex: 1;\r\n}\r\n\r\n.greeting-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.greeting-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  display: block;\r\n}\r\n\r\n.profile-section {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.sync-status {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sync-status.syncing {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.sync-status.offline {\r\n  background: rgba(255, 87, 87, 0.3);\r\n}\r\n\r\n.sync-icon {\r\n  font-size: 18px;\r\n}\r\n\r\n/* 主要统计卡片 */\r\n.main-stats-card {\r\n  margin: -20px 16px 24px;\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 24px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.balance-section {\r\n  text-align: center;\r\n}\r\n\r\n.balance-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 12px;\r\n  gap: 8px;\r\n}\r\n\r\n.balance-label {\r\n  font-size: 16px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.trend-indicator {\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n}\r\n\r\n.trend-indicator.up {\r\n  background: rgba(76, 175, 80, 0.1);\r\n  color: #4CAF50;\r\n}\r\n\r\n.trend-indicator.down {\r\n  background: rgba(244, 67, 54, 0.1);\r\n  color: #f44336;\r\n}\r\n\r\n.trend-indicator.stable {\r\n  background: rgba(158, 158, 158, 0.1);\r\n  color: #9e9e9e;\r\n}\r\n\r\n.trend-icon {\r\n  font-size: 14px;\r\n}\r\n\r\n.balance-amount {\r\n  display: flex;\r\n  align-items: baseline;\r\n  justify-content: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.currency {\r\n  font-size: 20px;\r\n  color: #666;\r\n  margin-right: 4px;\r\n}\r\n\r\n.amount {\r\n  font-size: 36px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  letter-spacing: -1px;\r\n}\r\n\r\n.amount.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.balance-change {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n}\r\n\r\n.change-text {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.change-text.positive {\r\n  color: #4CAF50;\r\n}\r\n\r\n.change-text.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.change-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 收支统计卡片 */\r\n.income-expense-cards {\r\n  display: flex;\r\n  gap: 12px;\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  gap: 8px;\r\n}\r\n\r\n.card-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n}\r\n\r\n.income-icon {\r\n  background: rgba(76, 175, 80, 0.1);\r\n}\r\n\r\n.expense-icon {\r\n  background: rgba(244, 67, 54, 0.1);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.card-amount {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.amount-text {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  display: block;\r\n}\r\n\r\n.income .amount-text {\r\n  color: #4CAF50;\r\n}\r\n\r\n.expense .amount-text {\r\n  color: #f44336;\r\n}\r\n\r\n.card-progress {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 4px;\r\n  background: #f0f0f0;\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  border-radius: 2px;\r\n  transition: width 0.8s ease;\r\n}\r\n\r\n.income-progress {\r\n  background: linear-gradient(90deg, #4CAF50, #66BB6A);\r\n}\r\n\r\n.expense-progress {\r\n  background: linear-gradient(90deg, #f44336, #EF5350);\r\n}\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 快速操作区域 */\r\n.quick-actions-section {\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-title {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.title-subtitle {\r\n  font-size: 14px;\r\n  color: #666;\r\n  display: block;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-btn {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.quick-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.quick-btn:active::before {\r\n  left: 100%;\r\n}\r\n\r\n.quick-btn:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.expense-btn {\r\n  border-left: 4px solid #f44336;\r\n}\r\n\r\n.income-btn {\r\n  border-left: 4px solid #4CAF50;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.btn-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n}\r\n\r\n.expense-icon {\r\n  background: rgba(244, 67, 54, 0.1);\r\n}\r\n\r\n.income-icon {\r\n  background: rgba(76, 175, 80, 0.1);\r\n}\r\n\r\n.btn-text {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.btn-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.btn-subtitle {\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: block;\r\n}\r\n\r\n.btn-arrow {\r\n  font-size: 18px;\r\n  color: #ccc;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 最近记录区域 */\r\n.recent-section {\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  background: rgba(76, 175, 80, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.header-right:active {\r\n  background: rgba(76, 175, 80, 0.2);\r\n  transform: scale(0.95);\r\n}\r\n\r\n.more-text {\r\n  font-size: 14px;\r\n  color: #4CAF50;\r\n  font-weight: 500;\r\n}\r\n\r\n.more-arrow {\r\n  font-size: 12px;\r\n  color: #4CAF50;\r\n  font-weight: bold;\r\n}\r\n\r\n.recent-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.record-item {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  transition: all 0.3s ease;\r\n  animation: slideInUp 0.5s ease forwards;\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n}\r\n\r\n.record-item:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.record-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n}\r\n\r\n.record-icon-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.record-icon {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-text {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.type-indicator {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  border: 2px solid white;\r\n}\r\n\r\n.type-indicator.income {\r\n  background: #4CAF50;\r\n}\r\n\r\n.type-indicator.expense {\r\n  background: #f44336;\r\n}\r\n\r\n.record-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n  flex: 1;\r\n}\r\n\r\n.record-category {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.record-note {\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: block;\r\n  max-width: 150px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.record-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n.record-right {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 4px;\r\n}\r\n\r\n.record-amount {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n}\r\n\r\n.record-amount.income {\r\n  color: #4CAF50;\r\n}\r\n\r\n.record-amount.expense {\r\n  color: #f44336;\r\n}\r\n\r\n.edit-indicator {\r\n  opacity: 0.5;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.record-item:active .edit-indicator {\r\n  opacity: 1;\r\n}\r\n\r\n.edit-icon {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.empty-hint {\r\n  font-size: 14px;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes slideInDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes bounceIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.05);\r\n  }\r\n  70% {\r\n    transform: scale(0.9);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 应用动画 */\r\n.slide-in-down {\r\n  animation: slideInDown 0.6s ease-out;\r\n}\r\n\r\n.slide-in-left {\r\n  animation: slideInLeft 0.6s ease-out 0.2s both;\r\n}\r\n\r\n.slide-in-right {\r\n  animation: slideInRight 0.6s ease-out 0.3s both;\r\n}\r\n\r\n.bounce-in {\r\n  animation: bounceIn 0.8s ease-out 0.4s both;\r\n}\r\n\r\n.fade-in-up {\r\n  animation: fadeInUp 0.6s ease-out 0.5s both;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "store", "getFirstDayOfMonth", "getLastDayOfMonth", "formatDate", "uni", "onMounted", "formatAmount"], "mappings": ";;;;AAsKA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,QAAQA,cAAAA,SAAS,MAAMC,YAAAA,MAAM,KAAK;AAGxC,UAAM,eAAeD,cAAAA,SAAS,MAAM;AAClC,YAAM,QAAO,oBAAI,KAAM,GAAC,SAAS;AACjC,UAAI,OAAO;AAAG,eAAO;AACrB,UAAI,OAAO;AAAI,eAAO;AACtB,UAAI,OAAO;AAAI,eAAO;AACtB,aAAO;AAAA,KACR;AAGD,UAAM,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,aAAO,GAAG,IAAI,SAAQ,IAAK,CAAC,IAAI,IAAI,QAAS,CAAA,KAAK,SAAS,IAAI,OAAM,CAAE,CAAC;AAAA,KACzE;AAGD,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,UAAI,MAAM,MAAM;AAAS,eAAO;AAChC,UAAI,CAAC,MAAM,MAAM;AAAU,eAAO;AAClC,UAAI,MAAM,MAAM;AAAiB,eAAO;AACxC,aAAO;AAAA,KACR;AAGD,UAAM,eAAeA,cAAAA,SAAS,MAAM;AAClC,YAAM,MAAM,oBAAI,KAAK;AACrB,aAAO,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,KAC3E;AAGD,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,QAAQE,cAAAA,mBAAmB;AACjC,YAAM,MAAMC,cAAAA,kBAAkB;AAC9B,aAAOF,kBAAM,MAAM,QAAQ;AAAA,QAAO,YAChC,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAAA,MACzC;AAAA,KACD;AAGD,UAAM,mBAAmBD,cAAAA,SAAS,MAAM;AACtC,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,YAAY,IAAI,KAAK,IAAI,eAAe,IAAI,SAAQ,IAAK,GAAG,CAAC;AACnE,YAAM,QAAQI,cAAU,WAAC,SAAS;AAClC,YAAM,MAAMA,cAAAA,WAAW,IAAI,KAAK,IAAI,YAAa,GAAE,IAAI,YAAY,CAAC,CAAC;AACrE,aAAOH,kBAAM,MAAM,QAAQ;AAAA,QAAO,YAChC,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAAA,MACzC;AAAA,KACD;AAGD,UAAM,gBAAgBD,cAAAA;AAAAA,MAAS,MAC7B,eAAe,MACZ,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,iBAAiBA,cAAAA;AAAAA,MAAS,MAC9B,eAAe,MACZ,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IACvD;AAGA,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,cAAc,QAAQ,eAAe,KAAK;AAGhF,UAAM,mBAAmBA,cAAAA,SAAS,MAAM;AACtC,YAAM,SAAS,iBAAiB,MAC7B,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AACrD,YAAM,UAAU,iBAAiB,MAC9B,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AACrD,aAAO,SAAS;AAAA,KACjB;AAGD,UAAM,gBAAgBA,cAAAA,SAAS,MAAM,eAAe,QAAQ,iBAAiB,KAAK;AAGlF,UAAM,eAAeA,cAAAA,SAAS,MAAM;AAClC,UAAI,cAAc,QAAQ;AAAG,eAAO;AACpC,UAAI,cAAc,QAAQ;AAAG,eAAO;AACpC,aAAO;AAAA,KACR;AAGD,UAAM,mBAAmBA,cAAAA,SAAS,MAAM;AACtC,UAAI,cAAc,QAAQ;AAAG,eAAO;AACpC,UAAI,cAAc,QAAQ;AAAG,eAAO;AACpC,aAAO;AAAA,KACR;AAGD,UAAM,qBAAqBA,cAAAA;AAAAA,MAAS,MAClC,eAAe,MAAM,OAAO,YAAU,OAAO,SAAS,QAAQ,EAAE;AAAA,IAClE;AAGA,UAAM,sBAAsBA,cAAAA;AAAAA,MAAS,MACnC,eAAe,MAAM,OAAO,YAAU,OAAO,SAAS,SAAS,EAAE;AAAA,IACnE;AAGA,UAAM,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,YAAY,KAAK,IAAI,cAAc,OAAO,eAAe,KAAK;AACpE,aAAO,YAAY,IAAK,cAAc,QAAQ,YAAa,MAAM;AAAA,KAClE;AAGD,UAAM,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,YAAM,YAAY,KAAK,IAAI,cAAc,OAAO,eAAe,KAAK;AACpE,aAAO,YAAY,IAAK,eAAe,QAAQ,YAAa,MAAM;AAAA,KACnE;AAGD,UAAM,gBAAgBA,cAAAA;AAAAA,MAAS,MAC7BC,YAAK,MAAC,MAAM,QACT,KAAK,CAAC,GAAG,MAAM,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,IAAI,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,CAAC,EAChF,MAAM,GAAG,CAAC;AAAA,IACf;AAGA,UAAM,iBAAiB,CAAC,MAAM,SAAS;AACrC,YAAM,QAAQG,cAAAA,WAAW,oBAAI,MAAM;AACnC,YAAM,YAAYA,cAAAA,WAAW,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,GAAI,CAAC;AAEvE,UAAI,SAAS,OAAO;AAClB,eAAO,MAAM,IAAI;AAAA,iBACR,SAAS,WAAW;AAC7B,eAAO,MAAM,IAAI;AAAA,aACZ;AACL,eAAO,GAAGA,cAAAA,WAAW,MAAM,OAAO,CAAC,IAAI,IAAI;AAAA,MAC7C;AAAA,IACF;AAGA,UAAM,WAAW,CAAC,SAAS;AAGzBC,0BAAI,eAAe,gBAAgB,IAAI;AACvCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAGA,UAAM,aAAa,CAAC,WAAW;AAG7BA,oBAAAA,MAAI,eAAe,gBAAgB,OAAO,EAAE;AAC5CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,OACN;AAAA,IACH;AAGA,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAEAC,kBAAAA,UAAU,MAAM;AAEdL,kBAAK,MAAC,QAAQ,YAAY;AAAA,KAC3B;AAED,WAAO;AAAA;AAAA,MAEL;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,oBAEAM,cAAY;AAAA,MACZ;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpXA,GAAG,WAAW,eAAe;"}