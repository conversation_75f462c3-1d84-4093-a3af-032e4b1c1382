{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"home-page\">\r\n    <!-- 顶部问候语 -->\r\n    <view class=\"greeting-section\">\r\n      <view class=\"greeting-content\">\r\n        <text class=\"greeting-title\">{{ greetingText }}</text>\r\n        <text class=\"greeting-subtitle\">{{ currentDateText }}</text>\r\n      </view>\r\n      <view class=\"profile-section\">\r\n        <view class=\"sync-status\" :class=\"{ syncing: state.syncing, offline: !state.isOnline }\">\r\n          <text class=\"sync-icon\">{{ syncStatusIcon }}</text>\r\n        </view>\r\n        <!-- 开发测试按钮 -->\r\n        <view class=\"test-btn\" @click=\"handleTestCloud\" v-if=\"isDev\">\r\n          <text class=\"test-text\">测试</text>\r\n        </view>\r\n        <!-- 测试页面按钮 -->\r\n        <view class=\"test-page-btn\" @click=\"goToTestPage\" v-if=\"isDev\">\r\n          <text class=\"test-text\">测试页</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要统计卡片 -->\r\n    <view class=\"main-stats-card slide-in-down\">\r\n      <view class=\"balance-section\">\r\n        <view class=\"balance-header\">\r\n          <text class=\"balance-label\">本月结余</text>\r\n          <view class=\"trend-indicator\" :class=\"balanceTrend\">\r\n            <text class=\"trend-icon\">{{ balanceTrendIcon }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"balance-amount\">\r\n          <text class=\"currency\">¥</text>\r\n          <text class=\"amount\" :class=\"{ negative: monthlyBalance < 0 }\">\r\n            {{ formatAmount(Math.abs(monthlyBalance)) }}\r\n          </text>\r\n        </view>\r\n        <view class=\"balance-change\" v-if=\"balanceChange !== 0\">\r\n          <text class=\"change-text\" :class=\"{ positive: balanceChange > 0, negative: balanceChange < 0 }\">\r\n            {{ balanceChange > 0 ? '+' : '' }}{{ formatAmount(Math.abs(balanceChange)) }}\r\n          </text>\r\n          <text class=\"change-label\">较上月</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 收支统计卡片 -->\r\n    <view class=\"income-expense-cards\">\r\n      <view class=\"stat-card income-card slide-in-left\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-icon income-icon\">📈</view>\r\n          <text class=\"card-title\">收入</text>\r\n        </view>\r\n        <view class=\"card-amount income\">\r\n          <text class=\"amount-text\">{{ formatAmount(monthlyIncome) }}</text>\r\n        </view>\r\n        <view class=\"card-progress\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill income-progress\" :style=\"{ width: incomeProgress + '%' }\"></view>\r\n          </view>\r\n          <text class=\"progress-text\">{{ incomeTransactions }}笔交易</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"stat-card expense-card slide-in-right\">\r\n        <view class=\"card-header\">\r\n          <view class=\"card-icon expense-icon\">📉</view>\r\n          <text class=\"card-title\">支出</text>\r\n        </view>\r\n        <view class=\"card-amount expense\">\r\n          <text class=\"amount-text\">{{ formatAmount(monthlyExpense) }}</text>\r\n        </view>\r\n        <view class=\"card-progress\">\r\n          <view class=\"progress-bar\">\r\n            <view class=\"progress-fill expense-progress\" :style=\"{ width: expenseProgress + '%' }\"></view>\r\n          </view>\r\n          <text class=\"progress-text\">{{ expenseTransactions }}笔交易</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快速操作区域 -->\r\n    <view class=\"quick-actions-section\">\r\n      <view class=\"section-title\">\r\n        <text class=\"title-text\">快速记账</text>\r\n        <text class=\"title-subtitle\">点击快速添加收支记录</text>\r\n      </view>\r\n\r\n      <view class=\"quick-actions bounce-in\">\r\n        <view class=\"quick-btn expense-btn\" @click=\"quickAdd('expense')\">\r\n          <view class=\"btn-content\">\r\n            <view class=\"btn-icon expense-icon\">💸</view>\r\n            <view class=\"btn-text\">\r\n              <text class=\"btn-title\">记支出</text>\r\n              <text class=\"btn-subtitle\">日常消费</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"btn-arrow\">→</view>\r\n        </view>\r\n\r\n        <view class=\"quick-btn income-btn\" @click=\"quickAdd('income')\">\r\n          <view class=\"btn-content\">\r\n            <view class=\"btn-icon income-icon\">💰</view>\r\n            <view class=\"btn-text\">\r\n              <text class=\"btn-title\">记收入</text>\r\n              <text class=\"btn-subtitle\">工资奖金</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"btn-arrow\">→</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 最近记录 -->\r\n    <view class=\"recent-section fade-in-up\">\r\n      <view class=\"section-header\">\r\n        <view class=\"header-left\">\r\n          <text class=\"section-title\">最近记录</text>\r\n          <text class=\"section-subtitle\">{{ recentRecords.length }}条记录</text>\r\n        </view>\r\n        <view class=\"header-right\" @click=\"goToList\">\r\n          <text class=\"more-text\">查看全部</text>\r\n          <text class=\"more-arrow\">→</text>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"recent-list\" v-if=\"recentRecords.length > 0\">\r\n        <view\r\n          class=\"record-item\"\r\n          v-for=\"(record, index) in recentRecords\"\r\n          :key=\"record.id\"\r\n          :style=\"{ animationDelay: (index * 0.1) + 's' }\"\r\n          @click=\"editRecord(record)\"\r\n        >\r\n          <view class=\"record-left\">\r\n            <view class=\"record-icon-wrapper\">\r\n              <view class=\"record-icon\" :style=\"{ backgroundColor: record.categoryColor }\">\r\n                <text class=\"icon-text\">{{ record.categoryIcon }}</text>\r\n              </view>\r\n              <view class=\"type-indicator\" :class=\"record.type\"></view>\r\n            </view>\r\n            <view class=\"record-info\">\r\n              <text class=\"record-category\">{{ record.categoryName }}</text>\r\n              <text class=\"record-note\" v-if=\"record.note\">{{ record.note }}</text>\r\n              <text class=\"record-time\">{{ formatDateTime(record.date, record.time) }}</text>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"record-right\">\r\n            <text class=\"record-amount\" :class=\"record.type\">\r\n              {{ record.type === 'expense' ? '-' : '+' }}{{ formatAmount(record.amount) }}\r\n            </text>\r\n            <view class=\"edit-indicator\">\r\n              <text class=\"edit-icon\">✏️</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"empty-state\" v-else>\r\n        <view class=\"empty-icon\">📝</view>\r\n        <text class=\"empty-text\">暂无记录</text>\r\n        <text class=\"empty-desc\">点击下方按钮开始记账吧</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { computed, onMounted } from 'vue'\r\nimport store from '../../store/index.js'\r\nimport { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth } from '../../utils/helpers.js'\r\nimport { testCloudFunction, testCloudConnection } from '../../utils/test-cloud.js'\r\n\r\nexport default {\r\n  name: 'HomePage',\r\n  setup() {\r\n    // 响应式状态\r\n    const state = computed(() => store.state)\r\n\r\n    // 问候语\r\n    const greetingText = computed(() => {\r\n      const hour = new Date().getHours()\r\n      if (hour < 6) return '夜深了'\r\n      if (hour < 12) return '早上好'\r\n      if (hour < 18) return '下午好'\r\n      return '晚上好'\r\n    })\r\n\r\n    // 当前日期文本\r\n    const currentDateText = computed(() => {\r\n      const now = new Date()\r\n      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n      return `${now.getMonth() + 1}月${now.getDate()}日 ${weekdays[now.getDay()]}`\r\n    })\r\n\r\n    // 同步状态图标\r\n    const syncStatusIcon = computed(() => {\r\n      if (state.value.syncing) return '🔄'\r\n      if (!state.value.isOnline) return '📴'\r\n      if (state.value.useCloudStorage) return '☁️'\r\n      return '📱'\r\n    })\r\n\r\n    // 当前月份\r\n    const currentMonth = computed(() => {\r\n      const now = new Date()\r\n      return `${now.getFullYear()}年${String(now.getMonth() + 1).padStart(2, '0')}月`\r\n    })\r\n\r\n    // 本月记录\r\n    const monthlyRecords = computed(() => {\r\n      const start = getFirstDayOfMonth()\r\n      const end = getLastDayOfMonth()\r\n      return store.state.records.filter(record =>\r\n        record.date >= start && record.date <= end\r\n      )\r\n    })\r\n\r\n    // 上月记录（用于对比）\r\n    const lastMonthRecords = computed(() => {\r\n      const now = new Date()\r\n      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)\r\n      const start = formatDate(lastMonth)\r\n      const end = formatDate(new Date(now.getFullYear(), now.getMonth(), 0))\r\n      return store.state.records.filter(record =>\r\n        record.date >= start && record.date <= end\r\n      )\r\n    })\r\n\r\n    // 本月收入\r\n    const monthlyIncome = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'income')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月支出\r\n    const monthlyExpense = computed(() =>\r\n      monthlyRecords.value\r\n        .filter(record => record.type === 'expense')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n    )\r\n\r\n    // 本月结余\r\n    const monthlyBalance = computed(() => monthlyIncome.value - monthlyExpense.value)\r\n\r\n    // 上月结余\r\n    const lastMonthBalance = computed(() => {\r\n      const income = lastMonthRecords.value\r\n        .filter(record => record.type === 'income')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n      const expense = lastMonthRecords.value\r\n        .filter(record => record.type === 'expense')\r\n        .reduce((total, record) => total + record.amount, 0)\r\n      return income - expense\r\n    })\r\n\r\n    // 结余变化\r\n    const balanceChange = computed(() => monthlyBalance.value - lastMonthBalance.value)\r\n\r\n    // 结余趋势\r\n    const balanceTrend = computed(() => {\r\n      if (balanceChange.value > 0) return 'up'\r\n      if (balanceChange.value < 0) return 'down'\r\n      return 'stable'\r\n    })\r\n\r\n    // 趋势图标\r\n    const balanceTrendIcon = computed(() => {\r\n      if (balanceChange.value > 0) return '📈'\r\n      if (balanceChange.value < 0) return '📉'\r\n      return '➖'\r\n    })\r\n\r\n    // 收入交易数量\r\n    const incomeTransactions = computed(() =>\r\n      monthlyRecords.value.filter(record => record.type === 'income').length\r\n    )\r\n\r\n    // 支出交易数量\r\n    const expenseTransactions = computed(() =>\r\n      monthlyRecords.value.filter(record => record.type === 'expense').length\r\n    )\r\n\r\n    // 收入进度（相对于最大值）\r\n    const incomeProgress = computed(() => {\r\n      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)\r\n      return maxAmount > 0 ? (monthlyIncome.value / maxAmount) * 100 : 0\r\n    })\r\n\r\n    // 支出进度（相对于最大值）\r\n    const expenseProgress = computed(() => {\r\n      const maxAmount = Math.max(monthlyIncome.value, monthlyExpense.value)\r\n      return maxAmount > 0 ? (monthlyExpense.value / maxAmount) * 100 : 0\r\n    })\r\n\r\n    // 最近记录（最多显示5条）\r\n    const recentRecords = computed(() =>\r\n      store.state.records\r\n        .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))\r\n        .slice(0, 5)\r\n    )\r\n\r\n    // 格式化日期时间\r\n    const formatDateTime = (date, time) => {\r\n      const today = formatDate(new Date())\r\n      const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))\r\n\r\n      if (date === today) {\r\n        return `今天 ${time}`\r\n      } else if (date === yesterday) {\r\n        return `昨天 ${time}`\r\n      } else {\r\n        return `${formatDate(date, 'MM-DD')} ${time}`\r\n      }\r\n    }\r\n\r\n    // 快速记账\r\n    const quickAdd = (type) => {\r\n      // 由于记账页面是tabbar页面，需要使用switchTab\r\n      // 但switchTab不支持传参，所以先存储类型到本地\r\n      uni.setStorageSync('quickAddType', type)\r\n      uni.switchTab({\r\n        url: '/pages/add/add'\r\n      })\r\n    }\r\n\r\n    // 编辑记录\r\n    const editRecord = (record) => {\r\n      // 编辑记录需要传递参数，但switchTab不支持传参\r\n      // 先存储记录ID到本地，然后跳转\r\n      uni.setStorageSync('editRecordId', record.id)\r\n      uni.switchTab({\r\n        url: '/pages/add/add'\r\n      })\r\n    }\r\n\r\n    // 跳转到账单列表\r\n    const goToList = () => {\r\n      // list页面不是tabbar页面，使用navigateTo\r\n      uni.navigateTo({\r\n        url: '/pages/list/list'\r\n      })\r\n    }\r\n\r\n    // 测试云函数\r\n    const handleTestCloud = async () => {\r\n      uni.showLoading({\r\n        title: '测试中...'\r\n      })\r\n\r\n      try {\r\n        const result = await testCloudFunction()\r\n        uni.hideLoading()\r\n\r\n        if (result.success) {\r\n          uni.showToast({\r\n            title: '测试成功',\r\n            icon: 'success'\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: '测试失败',\r\n            icon: 'error'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        uni.showToast({\r\n          title: '测试出错',\r\n          icon: 'error'\r\n        })\r\n        console.error('测试云函数失败:', error)\r\n      }\r\n    }\r\n\r\n    // 跳转到测试页面\r\n    const goToTestPage = () => {\r\n      uni.navigateTo({\r\n        url: '/pages/test/test'\r\n      })\r\n    }\r\n\r\n    // 开发模式检测\r\n    const isDev = computed(() => {\r\n      // 在开发环境下显示测试按钮\r\n      return process.env.NODE_ENV === 'development' || true // 临时设为true方便测试\r\n    })\r\n\r\n    onMounted(() => {\r\n      // 页面加载时刷新数据\r\n      store.actions.loadRecords()\r\n    })\r\n\r\n    return {\r\n      // 状态\r\n      state,\r\n      // 问候语\r\n      greetingText,\r\n      currentDateText,\r\n      syncStatusIcon,\r\n      // 统计数据\r\n      currentMonth,\r\n      monthlyIncome,\r\n      monthlyExpense,\r\n      monthlyBalance,\r\n      balanceChange,\r\n      balanceTrend,\r\n      balanceTrendIcon,\r\n      incomeTransactions,\r\n      expenseTransactions,\r\n      incomeProgress,\r\n      expenseProgress,\r\n      // 记录列表\r\n      recentRecords,\r\n      // 工具函数\r\n      formatAmount,\r\n      formatDateTime,\r\n      // 操作方法\r\n      quickAdd,\r\n      editRecord,\r\n      goToList,\r\n      handleTestCloud,\r\n      goToTestPage,\r\n      // 开发相关\r\n      isDev\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-page {\r\n  padding: 0;\r\n  background: linear-gradient(180deg, #f8fffe 0%, #f0f9ff 100%);\r\n  min-height: 100vh;\r\n}\r\n\r\n/* 问候语区域 */\r\n.greeting-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 16px 16px;\r\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\r\n  color: white;\r\n}\r\n\r\n.greeting-content {\r\n  flex: 1;\r\n}\r\n\r\n.greeting-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.greeting-subtitle {\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n  display: block;\r\n}\r\n\r\n.profile-section {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.sync-status {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sync-status.syncing {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.sync-status.offline {\r\n  background: rgba(255, 87, 87, 0.3);\r\n}\r\n\r\n.sync-icon {\r\n  font-size: 18px;\r\n}\r\n\r\n/* 主要统计卡片 */\r\n.main-stats-card {\r\n  margin: -20px 16px 24px;\r\n  background: white;\r\n  border-radius: 20px;\r\n  padding: 24px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.balance-section {\r\n  text-align: center;\r\n}\r\n\r\n.balance-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 12px;\r\n  gap: 8px;\r\n}\r\n\r\n.balance-label {\r\n  font-size: 16px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.trend-indicator {\r\n  padding: 4px 8px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n}\r\n\r\n.trend-indicator.up {\r\n  background: rgba(76, 175, 80, 0.1);\r\n  color: #4CAF50;\r\n}\r\n\r\n.trend-indicator.down {\r\n  background: rgba(244, 67, 54, 0.1);\r\n  color: #f44336;\r\n}\r\n\r\n.trend-indicator.stable {\r\n  background: rgba(158, 158, 158, 0.1);\r\n  color: #9e9e9e;\r\n}\r\n\r\n.trend-icon {\r\n  font-size: 14px;\r\n}\r\n\r\n.balance-amount {\r\n  display: flex;\r\n  align-items: baseline;\r\n  justify-content: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.currency {\r\n  font-size: 20px;\r\n  color: #666;\r\n  margin-right: 4px;\r\n}\r\n\r\n.amount {\r\n  font-size: 36px;\r\n  font-weight: 700;\r\n  color: #333;\r\n  letter-spacing: -1px;\r\n}\r\n\r\n.amount.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.balance-change {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 4px;\r\n}\r\n\r\n.change-text {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n.change-text.positive {\r\n  color: #4CAF50;\r\n}\r\n\r\n.change-text.negative {\r\n  color: #f44336;\r\n}\r\n\r\n.change-label {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 收支统计卡片 */\r\n.income-expense-cards {\r\n  display: flex;\r\n  gap: 12px;\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.stat-card {\r\n  flex: 1;\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.stat-card:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  gap: 8px;\r\n}\r\n\r\n.card-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n}\r\n\r\n.income-icon {\r\n  background: rgba(76, 175, 80, 0.1);\r\n}\r\n\r\n.expense-icon {\r\n  background: rgba(244, 67, 54, 0.1);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  color: #666;\r\n  font-weight: 500;\r\n}\r\n\r\n.card-amount {\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.amount-text {\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  display: block;\r\n}\r\n\r\n.income .amount-text {\r\n  color: #4CAF50;\r\n}\r\n\r\n.expense .amount-text {\r\n  color: #f44336;\r\n}\r\n\r\n.card-progress {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 6px;\r\n}\r\n\r\n.progress-bar {\r\n  height: 4px;\r\n  background: #f0f0f0;\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  border-radius: 2px;\r\n  transition: width 0.8s ease;\r\n}\r\n\r\n.income-progress {\r\n  background: linear-gradient(90deg, #4CAF50, #66BB6A);\r\n}\r\n\r\n.expense-progress {\r\n  background: linear-gradient(90deg, #f44336, #EF5350);\r\n}\r\n\r\n.progress-text {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n/* 快速操作区域 */\r\n.quick-actions-section {\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-title {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.title-text {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.title-subtitle {\r\n  font-size: 14px;\r\n  color: #666;\r\n  display: block;\r\n}\r\n\r\n.quick-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.quick-btn {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.quick-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.quick-btn:active::before {\r\n  left: 100%;\r\n}\r\n\r\n.quick-btn:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n.expense-btn {\r\n  border-left: 4px solid #f44336;\r\n}\r\n\r\n.income-btn {\r\n  border-left: 4px solid #4CAF50;\r\n}\r\n\r\n.btn-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.btn-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 24px;\r\n}\r\n\r\n.expense-icon {\r\n  background: rgba(244, 67, 54, 0.1);\r\n}\r\n\r\n.income-icon {\r\n  background: rgba(76, 175, 80, 0.1);\r\n}\r\n\r\n.btn-text {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.btn-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.btn-subtitle {\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: block;\r\n}\r\n\r\n.btn-arrow {\r\n  font-size: 18px;\r\n  color: #ccc;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 最近记录区域 */\r\n.recent-section {\r\n  padding: 0 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.section-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #333;\r\n}\r\n\r\n.section-subtitle {\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  background: rgba(76, 175, 80, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.header-right:active {\r\n  background: rgba(76, 175, 80, 0.2);\r\n  transform: scale(0.95);\r\n}\r\n\r\n.more-text {\r\n  font-size: 14px;\r\n  color: #4CAF50;\r\n  font-weight: 500;\r\n}\r\n\r\n.more-arrow {\r\n  font-size: 12px;\r\n  color: #4CAF50;\r\n  font-weight: bold;\r\n}\r\n\r\n.recent-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.record-item {\r\n  background: white;\r\n  border-radius: 16px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  transition: all 0.3s ease;\r\n  animation: slideInUp 0.5s ease forwards;\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n}\r\n\r\n.record-item:active {\r\n  transform: scale(0.98);\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.record-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n}\r\n\r\n.record-icon-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.record-icon {\r\n  width: 44px;\r\n  height: 44px;\r\n  border-radius: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.icon-text {\r\n  font-size: 20px;\r\n  color: white;\r\n}\r\n\r\n.type-indicator {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 8px;\r\n  border: 2px solid white;\r\n}\r\n\r\n.type-indicator.income {\r\n  background: #4CAF50;\r\n}\r\n\r\n.type-indicator.expense {\r\n  background: #f44336;\r\n}\r\n\r\n.record-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n  flex: 1;\r\n}\r\n\r\n.record-category {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  display: block;\r\n}\r\n\r\n.record-note {\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: block;\r\n  max-width: 150px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.record-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n.record-right {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 4px;\r\n}\r\n\r\n.record-amount {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n}\r\n\r\n.record-amount.income {\r\n  color: #4CAF50;\r\n}\r\n\r\n.record-amount.expense {\r\n  color: #f44336;\r\n}\r\n\r\n.edit-indicator {\r\n  opacity: 0.5;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.record-item:active .edit-indicator {\r\n  opacity: 1;\r\n}\r\n\r\n.edit-icon {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  background: white;\r\n  border-radius: 16px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 16px;\r\n  color: #666;\r\n  margin-bottom: 8px;\r\n  display: block;\r\n}\r\n\r\n.empty-hint {\r\n  font-size: 14px;\r\n  color: #999;\r\n  display: block;\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes slideInDown {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInLeft {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(-30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes bounceIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: scale(0.3);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scale(1.05);\r\n  }\r\n  70% {\r\n    transform: scale(0.9);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 应用动画 */\r\n.slide-in-down {\r\n  animation: slideInDown 0.6s ease-out;\r\n}\r\n\r\n.slide-in-left {\r\n  animation: slideInLeft 0.6s ease-out 0.2s both;\r\n}\r\n\r\n.slide-in-right {\r\n  animation: slideInRight 0.6s ease-out 0.3s both;\r\n}\r\n\r\n.bounce-in {\r\n  animation: bounceIn 0.8s ease-out 0.4s both;\r\n}\r\n\r\n.fade-in-up {\r\n  animation: fadeInUp 0.6s ease-out 0.5s both;\r\n}\r\n\r\n/* 测试按钮样式 */\r\n.test-btn, .test-page-btn {\r\n  padding: 6px 12px;\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border-radius: 12px;\r\n  margin-left: 8px;\r\n  transition: all 0.3s ease;\r\n  margin-left: 8px;\r\n}\r\n\r\n.test-page-btn {\r\n  background: rgba(33, 150, 243, 0.1);\r\n  border: 1px solid rgba(33, 150, 243, 0.3);\r\n}\r\n\r\n.test-btn:active, .test-page-btn:active {\r\n  background: rgba(255, 193, 7, 0.2);\r\n  transform: scale(0.95);\r\n}\r\n\r\n.test-page-btn:active {\r\n  background: rgba(33, 150, 243, 0.2);\r\n}\r\n\r\n.test-text {\r\n  font-size: 12px;\r\n  color: #FFC107;\r\n  font-weight: 500;\r\n}\r\n\r\n.test-page-btn .test-text {\r\n  color: #2196F3;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/workspace/cocos/node/app/bookkeepingBook/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "store", "getFirstDayOfMonth", "getLastDayOfMonth", "formatDate", "uni", "testCloudFunction", "onMounted", "formatAmount"], "mappings": ";;;;;AA+KA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAEN,UAAM,QAAQA,cAAA,SAAS,MAAMC,YAAA,MAAM,KAAK;AAGlC,UAAA,eAAeD,cAAAA,SAAS,MAAM;AAClC,YAAM,QAAO,oBAAI,KAAK,GAAE,SAAS;AACjC,UAAI,OAAO;AAAU,eAAA;AACrB,UAAI,OAAO;AAAW,eAAA;AACtB,UAAI,OAAO;AAAW,eAAA;AACf,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,kBAAkBA,cAAAA,SAAS,MAAM;AAC/B,YAAA,0BAAU;AACV,YAAA,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,aAAO,GAAG,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,SAAS,IAAI,OAAQ,CAAA,CAAC;AAAA,IAAA,CACzE;AAGK,UAAA,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,UAAI,MAAM,MAAM;AAAgB,eAAA;AAC5B,UAAA,CAAC,MAAM,MAAM;AAAiB,eAAA;AAClC,UAAI,MAAM,MAAM;AAAwB,eAAA;AACjC,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,eAAeA,cAAAA,SAAS,MAAM;AAC5B,YAAA,0BAAU;AAChB,aAAO,GAAG,IAAI,YAAa,CAAA,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IAAA,CAC3E;AAGK,UAAA,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,QAAQE,cAAAA;AACd,YAAM,MAAMC,cAAAA;AACL,aAAAF,YAAA,MAAM,MAAM,QAAQ;AAAA,QAAO,CAChC,WAAA,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAAA,MAAA;AAAA,IACzC,CACD;AAGK,UAAA,mBAAmBD,cAAAA,SAAS,MAAM;AAChC,YAAA,0BAAU;AACV,YAAA,YAAY,IAAI,KAAK,IAAI,YAAA,GAAe,IAAI,SAAa,IAAA,GAAG,CAAC;AAC7D,YAAA,QAAQI,yBAAW,SAAS;AAC5B,YAAA,MAAMA,cAAAA,WAAW,IAAI,KAAK,IAAI,YAAY,GAAG,IAAI,YAAY,CAAC,CAAC;AAC9D,aAAAH,YAAA,MAAM,MAAM,QAAQ;AAAA,QAAO,CAChC,WAAA,OAAO,QAAQ,SAAS,OAAO,QAAQ;AAAA,MAAA;AAAA,IACzC,CACD;AAGD,UAAM,gBAAgBD,cAAA;AAAA,MAAS,MAC7B,eAAe,MACZ,OAAO,CAAA,WAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAAA;AAIvD,UAAM,iBAAiBA,cAAA;AAAA,MAAS,MAC9B,eAAe,MACZ,OAAO,CAAA,WAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,IAAA;AAIvD,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,cAAc,QAAQ,eAAe,KAAK;AAG1E,UAAA,mBAAmBA,cAAAA,SAAS,MAAM;AACtC,YAAM,SAAS,iBAAiB,MAC7B,OAAO,CAAA,WAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AACrD,YAAM,UAAU,iBAAiB,MAC9B,OAAO,CAAA,WAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AACrD,aAAO,SAAS;AAAA,IAAA,CACjB;AAGD,UAAM,gBAAgBA,cAAAA,SAAS,MAAM,eAAe,QAAQ,iBAAiB,KAAK;AAG5E,UAAA,eAAeA,cAAAA,SAAS,MAAM;AAClC,UAAI,cAAc,QAAQ;AAAU,eAAA;AACpC,UAAI,cAAc,QAAQ;AAAU,eAAA;AAC7B,aAAA;AAAA,IAAA,CACR;AAGK,UAAA,mBAAmBA,cAAAA,SAAS,MAAM;AACtC,UAAI,cAAc,QAAQ;AAAU,eAAA;AACpC,UAAI,cAAc,QAAQ;AAAU,eAAA;AAC7B,aAAA;AAAA,IAAA,CACR;AAGD,UAAM,qBAAqBA,cAAA;AAAA,MAAS,MAClC,eAAe,MAAM,OAAO,YAAU,OAAO,SAAS,QAAQ,EAAE;AAAA,IAAA;AAIlE,UAAM,sBAAsBA,cAAA;AAAA,MAAS,MACnC,eAAe,MAAM,OAAO,YAAU,OAAO,SAAS,SAAS,EAAE;AAAA,IAAA;AAI7D,UAAA,iBAAiBA,cAAAA,SAAS,MAAM;AACpC,YAAM,YAAY,KAAK,IAAI,cAAc,OAAO,eAAe,KAAK;AACpE,aAAO,YAAY,IAAK,cAAc,QAAQ,YAAa,MAAM;AAAA,IAAA,CAClE;AAGK,UAAA,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,YAAM,YAAY,KAAK,IAAI,cAAc,OAAO,eAAe,KAAK;AACpE,aAAO,YAAY,IAAK,eAAe,QAAQ,YAAa,MAAM;AAAA,IAAA,CACnE;AAGD,UAAM,gBAAgBA,cAAA;AAAA,MAAS,MAC7BC,kBAAM,MAAM,QACT,KAAK,CAAC,GAAG,MAAM,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,IAAI,oBAAI,KAAK,EAAE,OAAO,MAAM,EAAE,IAAI,CAAC,EAChF,MAAM,GAAG,CAAC;AAAA,IAAA;AAIT,UAAA,iBAAiB,CAAC,MAAM,SAAS;AACrC,YAAM,QAAQG,cAAAA,WAAe,oBAAA,KAAM,CAAA;AAC7B,YAAA,YAAYA,cAAAA,WAAW,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,KAAK,GAAI,CAAC;AAEvE,UAAI,SAAS,OAAO;AAClB,eAAO,MAAM,IAAI;AAAA,MAAA,WACR,SAAS,WAAW;AAC7B,eAAO,MAAM,IAAI;AAAA,MAAA,OACZ;AACL,eAAO,GAAGA,cAAAA,WAAW,MAAM,OAAO,CAAC,IAAI,IAAI;AAAA,MAC7C;AAAA,IAAA;AAII,UAAA,WAAW,CAAC,SAAS;AAGrBC,oBAAAA,MAAA,eAAe,gBAAgB,IAAI;AACvCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIG,UAAA,aAAa,CAAC,WAAW;AAGzBA,oBAAAA,MAAA,eAAe,gBAAgB,OAAO,EAAE;AAC5CA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIH,UAAM,WAAW,MAAM;AAErBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIH,UAAM,kBAAkB,YAAY;AAClCA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MAAA,CACR;AAEG,UAAA;AACI,cAAA,SAAS,MAAMC,gBAAAA;AACrBD,sBAAA,MAAI,YAAY;AAEhB,YAAI,OAAO,SAAS;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QAAA,OACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,eACO,OAAO;AACdA,sBAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACDA,sBAAA,MAAc,MAAA,SAAA,gCAAA,YAAY,KAAK;AAAA,MACjC;AAAA,IAAA;AAIF,UAAM,eAAe,MAAM;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;AAIG,UAAA,QAAQL,cAAAA,SAAS,MAAM;AAEpB,aAAA;AAAA,IAAA,CACR;AAEDO,kBAAAA,UAAU,MAAM;AAEdN,wBAAM,QAAQ;IAAY,CAC3B;AAEM,WAAA;AAAA;AAAA,MAEL;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA;AAAA,MAAA,cAEAO,cAAA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,IAAA;AAAA,EAEJ;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7aA,GAAG,WAAW,eAAe;"}