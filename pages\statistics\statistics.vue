<template>
  <view class="statistics-page">
    <!-- 时间选择 -->
    <view class="time-selector">
      <view 
        class="time-tab" 
        v-for="tab in timeTabs" 
        :key="tab.value"
        :class="{ active: currentTimeRange === tab.value }"
        @click="switchTimeRange(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
      </view>
    </view>

    <!-- 总览卡片 -->
    <view class="overview-card">
      <view class="overview-item">
        <text class="overview-label">总收入</text>
        <text class="overview-value income">{{ formatAmount(totalIncome) }}</text>
      </view>
      <view class="overview-divider"></view>
      <view class="overview-item">
        <text class="overview-label">总支出</text>
        <text class="overview-value expense">{{ formatAmount(totalExpense) }}</text>
      </view>
      <view class="overview-divider"></view>
      <view class="overview-item">
        <text class="overview-label">净收入</text>
        <text class="overview-value" :class="{ 'negative': netIncome < 0 }">
          {{ formatAmount(netIncome) }}
        </text>
      </view>
    </view>

    <!-- 图表区域 -->
    <view class="chart-section">
      <view class="section-title">支出分类占比</view>
      <view class="chart-container">
        <!-- 这里应该是饼图，暂时用简单的条形图代替 -->
        <view class="simple-chart">
          <view 
            class="chart-bar" 
            v-for="item in expenseByCategory" 
            :key="item.categoryId"
          >
            <view class="bar-info">
              <view class="bar-icon" :style="{ backgroundColor: item.categoryColor }">
                {{ item.categoryIcon }}
              </view>
              <text class="bar-name">{{ item.categoryName }}</text>
              <text class="bar-amount">{{ formatAmount(item.amount) }}</text>
            </view>
            <view class="bar-container">
              <view 
                class="bar-fill" 
                :style="{ 
                  width: (item.amount / maxExpenseAmount * 100) + '%',
                  backgroundColor: item.categoryColor 
                }"
              ></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 趋势分析 -->
    <view class="trend-section">
      <view class="section-title">收支趋势</view>
      <view class="trend-chart">
        <view 
          class="trend-item" 
          v-for="item in trendData" 
          :key="item.date"
        >
          <text class="trend-date">{{ formatTrendDate(item.date) }}</text>
          <view class="trend-bars">
            <view class="trend-bar income">
              <view 
                class="trend-bar-fill" 
                :style="{ height: (item.income / maxTrendAmount * 100) + '%' }"
              ></view>
            </view>
            <view class="trend-bar expense">
              <view 
                class="trend-bar-fill" 
                :style="{ height: (item.expense / maxTrendAmount * 100) + '%' }"
              ></view>
            </view>
          </view>
          <view class="trend-values">
            <text class="trend-income">{{ formatAmount(item.income) }}</text>
            <text class="trend-expense">{{ formatAmount(item.expense) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分类详情 -->
    <view class="category-section">
      <view class="section-title">分类统计</view>
      <view class="category-tabs">
        <view 
          class="category-tab" 
          :class="{ active: currentCategoryType === 'expense' }"
          @click="switchCategoryType('expense')"
        >
          <text class="tab-text">支出</text>
        </view>
        <view 
          class="category-tab" 
          :class="{ active: currentCategoryType === 'income' }"
          @click="switchCategoryType('income')"
        >
          <text class="tab-text">收入</text>
        </view>
      </view>
      
      <view class="category-list">
        <view 
          class="category-item" 
          v-for="item in currentCategoryStats" 
          :key="item.categoryId"
        >
          <view class="category-left">
            <view class="category-icon" :style="{ backgroundColor: item.categoryColor }">
              {{ item.categoryIcon }}
            </view>
            <view class="category-info">
              <text class="category-name">{{ item.categoryName }}</text>
              <text class="category-count">{{ item.count }}笔</text>
            </view>
          </view>
          <view class="category-right">
            <text class="category-amount">{{ formatAmount(item.amount) }}</text>
            <text class="category-percent">
              {{ ((item.amount / (currentCategoryType === 'expense' ? totalExpense : totalIncome)) * 100).toFixed(1) }}%
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import store from '../../store/index.js'
import { formatAmount, formatDate, getFirstDayOfMonth, getLastDayOfMonth, getFirstDayOfWeek, getLastDayOfWeek } from '../../utils/helpers.js'

export default {
  name: 'StatisticsPage',
  setup() {
    // 时间范围选择
    const currentTimeRange = ref('month')
    const currentCategoryType = ref('expense')
    
    const timeTabs = [
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' },
      { label: '本年', value: 'year' }
    ]

    // 根据时间范围过滤记录
    const filteredRecords = computed(() => {
      const now = new Date()
      let startDate, endDate

      switch (currentTimeRange.value) {
        case 'week':
          startDate = getFirstDayOfWeek()
          endDate = getLastDayOfWeek()
          break
        case 'month':
          startDate = getFirstDayOfMonth()
          endDate = getLastDayOfMonth()
          break
        case 'year':
          startDate = formatDate(new Date(now.getFullYear(), 0, 1))
          endDate = formatDate(new Date(now.getFullYear(), 11, 31))
          break
        default:
          return store.state.records
      }

      return store.state.records.filter(record => 
        record.date >= startDate && record.date <= endDate
      )
    })

    // 总收入
    const totalIncome = computed(() => 
      filteredRecords.value
        .filter(record => record.type === 'income')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 总支出
    const totalExpense = computed(() => 
      filteredRecords.value
        .filter(record => record.type === 'expense')
        .reduce((total, record) => total + record.amount, 0)
    )

    // 净收入
    const netIncome = computed(() => totalIncome.value - totalExpense.value)

    // 按分类统计支出
    const expenseByCategory = computed(() => {
      const stats = {}
      
      filteredRecords.value
        .filter(record => record.type === 'expense')
        .forEach(record => {
          if (!stats[record.categoryId]) {
            stats[record.categoryId] = {
              categoryId: record.categoryId,
              categoryName: record.categoryName,
              categoryIcon: record.categoryIcon,
              categoryColor: record.categoryColor,
              amount: 0,
              count: 0
            }
          }
          stats[record.categoryId].amount += record.amount
          stats[record.categoryId].count += 1
        })

      return Object.values(stats)
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 8) // 只显示前8个分类
    })

    // 最大支出金额（用于计算条形图比例）
    const maxExpenseAmount = computed(() => 
      Math.max(...expenseByCategory.value.map(item => item.amount), 1)
    )

    // 趋势数据
    const trendData = computed(() => {
      const stats = {}
      
      filteredRecords.value.forEach(record => {
        let key = record.date
        
        // 根据时间范围调整分组方式
        if (currentTimeRange.value === 'year') {
          key = record.date.substring(0, 7) // 按月分组
        }
        
        if (!stats[key]) {
          stats[key] = {
            date: key,
            income: 0,
            expense: 0
          }
        }
        
        if (record.type === 'income') {
          stats[key].income += record.amount
        } else {
          stats[key].expense += record.amount
        }
      })

      return Object.values(stats)
        .sort((a, b) => a.date.localeCompare(b.date))
        .slice(-7) // 只显示最近7个时间点
    })

    // 趋势图最大值
    const maxTrendAmount = computed(() => {
      const amounts = trendData.value.flatMap(item => [item.income, item.expense])
      return Math.max(...amounts, 1)
    })

    // 当前分类统计
    const currentCategoryStats = computed(() => {
      const stats = {}
      
      filteredRecords.value
        .filter(record => record.type === currentCategoryType.value)
        .forEach(record => {
          if (!stats[record.categoryId]) {
            stats[record.categoryId] = {
              categoryId: record.categoryId,
              categoryName: record.categoryName,
              categoryIcon: record.categoryIcon,
              categoryColor: record.categoryColor,
              amount: 0,
              count: 0
            }
          }
          stats[record.categoryId].amount += record.amount
          stats[record.categoryId].count += 1
        })

      return Object.values(stats).sort((a, b) => b.amount - a.amount)
    })

    // 方法
    const switchTimeRange = (range) => {
      currentTimeRange.value = range
    }

    const switchCategoryType = (type) => {
      currentCategoryType.value = type
    }

    const formatTrendDate = (date) => {
      if (currentTimeRange.value === 'year') {
        return date.substring(5) // MM-DD
      }
      return formatDate(date, 'MM-DD')
    }

    onMounted(() => {
      store.actions.loadRecords()
    })

    return {
      timeTabs,
      currentTimeRange,
      currentCategoryType,
      totalIncome,
      totalExpense,
      netIncome,
      expenseByCategory,
      maxExpenseAmount,
      trendData,
      maxTrendAmount,
      currentCategoryStats,
      formatAmount,
      formatTrendDate,
      switchTimeRange,
      switchCategoryType
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics-page {
  background-color: $uni-bg-color-grey;
  min-height: 100vh;
}

/* 时间选择器 */
.time-selector {
  display: flex;
  background-color: white;
  padding: 16px;
  margin-bottom: 16px;
}

.time-tab {
  flex: 1;
  padding: 12px;
  text-align: center;
  border-radius: 8px;
  margin: 0 4px;
  transition: all 0.3s ease;

  &.active {
    background-color: $uni-color-primary;

    .tab-text {
      color: white;
      font-weight: 600;
    }
  }
}

.tab-text {
  font-size: 14px;
  color: $uni-text-color;
}

/* 总览卡片 */
.overview-card {
  display: flex;
  background-color: white;
  padding: 24px 20px;
  margin: 0 16px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.overview-item {
  flex: 1;
  text-align: center;
}

.overview-label {
  display: block;
  font-size: 12px;
  color: $uni-text-color-grey;
  margin-bottom: 8px;
}

.overview-value {
  font-size: 20px;
  font-weight: bold;

  &.income {
    color: $income-color;
  }

  &.expense {
    color: $expense-color;
  }

  &.negative {
    color: $expense-color;
  }
}

.overview-divider {
  width: 1px;
  background-color: $uni-border-color;
  margin: 0 16px;
}

/* 图表区域 */
.chart-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: $uni-text-color;
  margin-bottom: 16px;
}

.simple-chart {
  .chart-bar {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.bar-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.bar-icon {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 8px;
}

.bar-name {
  flex: 1;
  font-size: 14px;
  color: $uni-text-color;
}

.bar-amount {
  font-size: 14px;
  font-weight: 600;
  color: $uni-text-color;
}

.bar-container {
  height: 8px;
  background-color: $uni-bg-color-grey;
  border-radius: 4px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 趋势分析 */
.trend-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.trend-chart {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 120px;
  padding: 0 8px;
}

.trend-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 2px;
}

.trend-date {
  font-size: 10px;
  color: $uni-text-color-grey;
  margin-bottom: 8px;
}

.trend-bars {
  display: flex;
  align-items: flex-end;
  height: 60px;
  margin-bottom: 8px;
}

.trend-bar {
  width: 8px;
  margin: 0 1px;
  border-radius: 4px 4px 0 0;

  &.income {
    background-color: rgba(76, 175, 80, 0.2);
  }

  &.expense {
    background-color: rgba(244, 67, 54, 0.2);
  }
}

.trend-bar-fill {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;

  .trend-bar.income & {
    background-color: $income-color;
  }

  .trend-bar.expense & {
    background-color: $expense-color;
  }
}

.trend-values {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.trend-income, .trend-expense {
  font-size: 8px;
  line-height: 1.2;
}

.trend-income {
  color: $income-color;
}

.trend-expense {
  color: $expense-color;
}

/* 分类详情 */
.category-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-tabs {
  display: flex;
  border-bottom: 1px solid $uni-border-color;
}

.category-tab {
  flex: 1;
  padding: 16px;
  text-align: center;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;

  &.active {
    border-bottom-color: $uni-color-primary;

    .tab-text {
      color: $uni-color-primary;
      font-weight: 600;
    }
  }
}

.category-list {
  padding: 0 20px 20px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid $uni-border-color;

  &:last-child {
    border-bottom: none;
  }
}

.category-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-icon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 16px;
  font-weight: 500;
  color: $uni-text-color;
  display: block;
  margin-bottom: 4px;
}

.category-count {
  font-size: 12px;
  color: $uni-text-color-grey;
}

.category-right {
  text-align: right;
}

.category-amount {
  font-size: 16px;
  font-weight: 600;
  color: $uni-text-color;
  display: block;
  margin-bottom: 4px;
}

.category-percent {
  font-size: 12px;
  color: $uni-text-color-grey;
}
</style>
