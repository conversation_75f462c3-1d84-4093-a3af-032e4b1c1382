{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["/**\n * 状态管理 - 使用Vue3的reactive实现简单状态管理\n */\nimport { reactive, computed } from 'vue'\nimport Storage from '../utils/storage.js'\nimport { Record, Statistics } from '../utils/models.js'\nimport { showToast } from '../utils/helpers.js'\n\n// 创建响应式状态\nconst state = reactive({\n  // 记账记录\n  records: [],\n  // 分类数据\n  categories: {\n    expense: [],\n    income: []\n  },\n  // 用户设置\n  settings: {},\n  // 当前选中的日期范围\n  dateRange: {\n    start: '',\n    end: ''\n  },\n  // 加载状态\n  loading: false\n})\n\n// 计算属性\nconst getters = {\n  // 获取所有记录\n  allRecords: computed(() => state.records),\n  \n  // 获取支出记录\n  expenseRecords: computed(() => \n    state.records.filter(record => record.type === 'expense')\n  ),\n  \n  // 获取收入记录\n  incomeRecords: computed(() => \n    state.records.filter(record => record.type === 'income')\n  ),\n  \n  // 获取支出分类\n  expenseCategories: computed(() => state.categories.expense),\n  \n  // 获取收入分类\n  incomeCategories: computed(() => state.categories.income),\n  \n  // 获取统计数据\n  statistics: computed(() => new Statistics(state.records)),\n  \n  // 获取总收入\n  totalIncome: computed(() => \n    state.records\n      .filter(record => record.type === 'income')\n      .reduce((total, record) => total + record.amount, 0)\n  ),\n  \n  // 获取总支出\n  totalExpense: computed(() => \n    state.records\n      .filter(record => record.type === 'expense')\n      .reduce((total, record) => total + record.amount, 0)\n  ),\n  \n  // 获取净收入\n  netIncome: computed(() => getters.totalIncome.value - getters.totalExpense.value),\n  \n  // 根据日期范围过滤记录\n  filteredRecords: computed(() => {\n    if (!state.dateRange.start || !state.dateRange.end) {\n      return state.records\n    }\n    return state.records.filter(record => \n      record.date >= state.dateRange.start && record.date <= state.dateRange.end\n    )\n  })\n}\n\n// 操作方法\nconst actions = {\n  // 初始化数据\n  init() {\n    this.loadRecords()\n    this.loadCategories()\n    this.loadSettings()\n  },\n  \n  // 加载记账记录\n  loadRecords() {\n    try {\n      const records = Storage.getRecords()\n      state.records = records.map(record => new Record(record))\n    } catch (error) {\n      console.error('加载记录失败:', error)\n      showToast('加载记录失败')\n    }\n  },\n  \n  // 保存记账记录\n  saveRecords() {\n    try {\n      const records = state.records.map(record => record.toObject())\n      Storage.setRecords(records)\n      return true\n    } catch (error) {\n      console.error('保存记录失败:', error)\n      showToast('保存记录失败')\n      return false\n    }\n  },\n  \n  // 添加记录\n  addRecord(recordData) {\n    try {\n      const record = new Record(recordData)\n      state.records.unshift(record)\n      this.saveRecords()\n      showToast('记录添加成功', 'success')\n      return record\n    } catch (error) {\n      console.error('添加记录失败:', error)\n      showToast('添加记录失败')\n      return null\n    }\n  },\n  \n  // 更新记录\n  updateRecord(id, recordData) {\n    try {\n      const index = state.records.findIndex(record => record.id === id)\n      if (index !== -1) {\n        state.records[index].update(recordData)\n        this.saveRecords()\n        showToast('记录更新成功', 'success')\n        return state.records[index]\n      }\n      throw new Error('记录不存在')\n    } catch (error) {\n      console.error('更新记录失败:', error)\n      showToast('更新记录失败')\n      return null\n    }\n  },\n  \n  // 删除记录\n  deleteRecord(id) {\n    try {\n      const index = state.records.findIndex(record => record.id === id)\n      if (index !== -1) {\n        state.records.splice(index, 1)\n        this.saveRecords()\n        showToast('记录删除成功', 'success')\n        return true\n      }\n      throw new Error('记录不存在')\n    } catch (error) {\n      console.error('删除记录失败:', error)\n      showToast('删除记录失败')\n      return false\n    }\n  },\n  \n  // 根据ID获取记录\n  getRecordById(id) {\n    return state.records.find(record => record.id === id)\n  },\n  \n  // 加载分类数据\n  loadCategories() {\n    try {\n      const categories = Storage.getCategories()\n      state.categories = categories\n    } catch (error) {\n      console.error('加载分类失败:', error)\n      showToast('加载分类失败')\n    }\n  },\n  \n  // 保存分类数据\n  saveCategories() {\n    try {\n      Storage.setCategories(state.categories)\n      return true\n    } catch (error) {\n      console.error('保存分类失败:', error)\n      showToast('保存分类失败')\n      return false\n    }\n  },\n  \n  // 根据ID获取分类\n  getCategoryById(id, type = 'expense') {\n    return state.categories[type].find(category => category.id === id)\n  },\n\n  // 获取默认分类\n  getDefaultCategories() {\n    return Storage.getDefaultCategories()\n  },\n  \n  // 加载设置\n  loadSettings() {\n    try {\n      state.settings = Storage.getSettings()\n    } catch (error) {\n      console.error('加载设置失败:', error)\n      showToast('加载设置失败')\n    }\n  },\n  \n  // 保存设置\n  saveSettings() {\n    try {\n      Storage.setSettings(state.settings)\n      showToast('设置保存成功', 'success')\n      return true\n    } catch (error) {\n      console.error('保存设置失败:', error)\n      showToast('保存设置失败')\n      return false\n    }\n  },\n  \n  // 更新设置\n  updateSettings(newSettings) {\n    Object.assign(state.settings, newSettings)\n    this.saveSettings()\n  },\n  \n  // 设置日期范围\n  setDateRange(start, end) {\n    state.dateRange.start = start\n    state.dateRange.end = end\n  },\n  \n  // 清空日期范围\n  clearDateRange() {\n    state.dateRange.start = ''\n    state.dateRange.end = ''\n  },\n  \n  // 设置加载状态\n  setLoading(loading) {\n    state.loading = loading\n  }\n}\n\n// 创建store实例\nconst store = {\n  state,\n  getters,\n  actions\n}\n\nexport default store\n"], "names": ["reactive", "computed", "Statistics", "Storage", "Record", "uni", "showToast"], "mappings": ";;;;;AASA,MAAM,QAAQA,cAAAA,SAAS;AAAA;AAAA,EAErB,SAAS,CAAE;AAAA;AAAA,EAEX,YAAY;AAAA,IACV,SAAS,CAAE;AAAA,IACX,QAAQ,CAAE;AAAA,EACX;AAAA;AAAA,EAED,UAAU,CAAE;AAAA;AAAA,EAEZ,WAAW;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA;AAAA,EAED,SAAS;AACX,CAAC;AAGD,MAAM,UAAU;AAAA;AAAA,EAEd,YAAYC,cAAQ,SAAC,MAAM,MAAM,OAAO;AAAA;AAAA,EAGxC,gBAAgBA,cAAAA;AAAAA,IAAS,MACvB,MAAM,QAAQ,OAAO,YAAU,OAAO,SAAS,SAAS;AAAA,EACzD;AAAA;AAAA,EAGD,eAAeA,cAAAA;AAAAA,IAAS,MACtB,MAAM,QAAQ,OAAO,YAAU,OAAO,SAAS,QAAQ;AAAA,EACxD;AAAA;AAAA,EAGD,mBAAmBA,cAAAA,SAAS,MAAM,MAAM,WAAW,OAAO;AAAA;AAAA,EAG1D,kBAAkBA,cAAAA,SAAS,MAAM,MAAM,WAAW,MAAM;AAAA;AAAA,EAGxD,YAAYA,cAAQ,SAAC,MAAM,IAAIC,aAAU,WAAC,MAAM,OAAO,CAAC;AAAA;AAAA,EAGxD,aAAaD,cAAAA;AAAAA,IAAS,MACpB,MAAM,QACH,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,cAAcA,cAAAA;AAAAA,IAAS,MACrB,MAAM,QACH,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,WAAWA,cAAAA,SAAS,MAAM,QAAQ,YAAY,QAAQ,QAAQ,aAAa,KAAK;AAAA;AAAA,EAGhF,iBAAiBA,cAAQ,SAAC,MAAM;AAC9B,QAAI,CAAC,MAAM,UAAU,SAAS,CAAC,MAAM,UAAU,KAAK;AAClD,aAAO,MAAM;AAAA,IACd;AACD,WAAO,MAAM,QAAQ;AAAA,MAAO,YAC1B,OAAO,QAAQ,MAAM,UAAU,SAAS,OAAO,QAAQ,MAAM,UAAU;AAAA,IACxE;AAAA,EACL,CAAG;AACH;AAGA,MAAM,UAAU;AAAA;AAAA,EAEd,OAAO;AACL,SAAK,YAAa;AAClB,SAAK,eAAgB;AACrB,SAAK,aAAc;AAAA,EACpB;AAAA;AAAA,EAGD,cAAc;AACZ,QAAI;AACF,YAAM,UAAUE,cAAO,QAAC,WAAY;AACpC,YAAM,UAAU,QAAQ,IAAI,YAAU,IAAIC,aAAAA,OAAO,MAAM,CAAC;AAAA,IACzD,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,wBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,cAAc;AACZ,QAAI;AACF,YAAM,UAAU,MAAM,QAAQ,IAAI,YAAU,OAAO,UAAU;AAC7DH,oBAAO,QAAC,WAAW,OAAO;AAC1B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdE,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,UAAU,YAAY;AACpB,QAAI;AACF,YAAM,SAAS,IAAIF,aAAM,OAAC,UAAU;AACpC,YAAM,QAAQ,QAAQ,MAAM;AAC5B,WAAK,YAAa;AAClBE,oBAAS,UAAC,UAAU,SAAS;AAC7B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdD,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,IAAI,YAAY;AAC3B,QAAI;AACF,YAAM,QAAQ,MAAM,QAAQ,UAAU,YAAU,OAAO,OAAO,EAAE;AAChE,UAAI,UAAU,IAAI;AAChB,cAAM,QAAQ,KAAK,EAAE,OAAO,UAAU;AACtC,aAAK,YAAa;AAClBA,sBAAS,UAAC,UAAU,SAAS;AAC7B,eAAO,MAAM,QAAQ,KAAK;AAAA,MAC3B;AACD,YAAM,IAAI,MAAM,OAAO;AAAA,IACxB,SAAQ,OAAO;AACdD,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,IAAI;AACf,QAAI;AACF,YAAM,QAAQ,MAAM,QAAQ,UAAU,YAAU,OAAO,OAAO,EAAE;AAChE,UAAI,UAAU,IAAI;AAChB,cAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,aAAK,YAAa;AAClBA,sBAAS,UAAC,UAAU,SAAS;AAC7B,eAAO;AAAA,MACR;AACD,YAAM,IAAI,MAAM,OAAO;AAAA,IACxB,SAAQ,OAAO;AACdD,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,IAAI;AAChB,WAAO,MAAM,QAAQ,KAAK,YAAU,OAAO,OAAO,EAAE;AAAA,EACrD;AAAA;AAAA,EAGD,iBAAiB;AACf,QAAI;AACF,YAAM,aAAaH,cAAO,QAAC,cAAe;AAC1C,YAAM,aAAa;AAAA,IACpB,SAAQ,OAAO;AACdE,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB;AACf,QAAI;AACFH,4BAAQ,cAAc,MAAM,UAAU;AACtC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdE,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB,IAAI,OAAO,WAAW;AACpC,WAAO,MAAM,WAAW,IAAI,EAAE,KAAK,cAAY,SAAS,OAAO,EAAE;AAAA,EAClE;AAAA;AAAA,EAGD,uBAAuB;AACrB,WAAOH,cAAAA,QAAQ,qBAAsB;AAAA,EACtC;AAAA;AAAA,EAGD,eAAe;AACb,QAAI;AACF,YAAM,WAAWA,cAAO,QAAC,YAAa;AAAA,IACvC,SAAQ,OAAO;AACdE,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,eAAe;AACb,QAAI;AACFH,4BAAQ,YAAY,MAAM,QAAQ;AAClCG,oBAAS,UAAC,UAAU,SAAS;AAC7B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdD,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BC,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,aAAa;AAC1B,WAAO,OAAO,MAAM,UAAU,WAAW;AACzC,SAAK,aAAc;AAAA,EACpB;AAAA;AAAA,EAGD,aAAa,OAAO,KAAK;AACvB,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAGD,iBAAiB;AACf,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAGD,WAAW,SAAS;AAClB,UAAM,UAAU;AAAA,EACjB;AACH;AAGK,MAAC,QAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF;;"}