{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["/**\n * 状态管理 - 使用Vue3的reactive实现简单状态管理\n * 支持云端存储和本地存储双模式\n */\nimport { reactive, computed } from 'vue'\nimport Storage from '../utils/storage.js'\nimport CloudStorage from '../utils/cloud-storage.js'\nimport { Record, Statistics } from '../utils/models.js'\nimport { showToast } from '../utils/helpers.js'\n\n// 创建响应式状态\nconst state = reactive({\n  // 记账记录\n  records: [],\n  // 分类数据\n  categories: {\n    expense: [],\n    income: []\n  },\n  // 用户设置\n  settings: {},\n  // 当前选中的日期范围\n  dateRange: {\n    start: '',\n    end: ''\n  },\n  // 加载状态\n  loading: false,\n  // 云端同步状态\n  syncing: false,\n  // 是否使用云端存储\n  useCloudStorage: true,\n  // 最后同步时间\n  lastSyncTime: null,\n  // 网络状态\n  isOnline: true\n})\n\n// 计算属性\nconst getters = {\n  // 获取所有记录\n  allRecords: computed(() => state.records),\n  \n  // 获取支出记录\n  expenseRecords: computed(() => \n    state.records.filter(record => record.type === 'expense')\n  ),\n  \n  // 获取收入记录\n  incomeRecords: computed(() => \n    state.records.filter(record => record.type === 'income')\n  ),\n  \n  // 获取支出分类\n  expenseCategories: computed(() => state.categories.expense),\n  \n  // 获取收入分类\n  incomeCategories: computed(() => state.categories.income),\n  \n  // 获取统计数据\n  statistics: computed(() => new Statistics(state.records)),\n  \n  // 获取总收入\n  totalIncome: computed(() => \n    state.records\n      .filter(record => record.type === 'income')\n      .reduce((total, record) => total + record.amount, 0)\n  ),\n  \n  // 获取总支出\n  totalExpense: computed(() => \n    state.records\n      .filter(record => record.type === 'expense')\n      .reduce((total, record) => total + record.amount, 0)\n  ),\n  \n  // 获取净收入\n  netIncome: computed(() => getters.totalIncome.value - getters.totalExpense.value),\n  \n  // 根据日期范围过滤记录\n  filteredRecords: computed(() => {\n    if (!state.dateRange.start || !state.dateRange.end) {\n      return state.records\n    }\n    return state.records.filter(record => \n      record.date >= state.dateRange.start && record.date <= state.dateRange.end\n    )\n  })\n}\n\n// 操作方法\nconst actions = {\n  // 初始化数据\n  async init() {\n    try {\n      // 检查网络状态\n      state.isOnline = await CloudStorage.isOnline()\n\n      if (state.useCloudStorage && state.isOnline) {\n        // 云端模式\n        await this.initCloudMode()\n      } else {\n        // 本地模式\n        await this.initLocalMode()\n      }\n    } catch (error) {\n      console.error('初始化失败:', error)\n      // 降级到本地模式\n      await this.initLocalMode()\n    }\n  },\n\n  // 初始化云端模式\n  async initCloudMode() {\n    try {\n      state.loading = true\n\n      // 初始化云端存储\n      await CloudStorage.init()\n\n      // 处理离线数据\n      await CloudStorage.handleOfflineData()\n\n      // 加载云端数据\n      await this.loadCloudData()\n\n      // 更新最后同步时间\n      state.lastSyncTime = new Date()\n      uni.setStorageSync('lastSyncTime', state.lastSyncTime)\n\n    } catch (error) {\n      console.error('云端模式初始化失败:', error)\n      throw error\n    } finally {\n      state.loading = false\n    }\n  },\n\n  // 初始化本地模式\n  async initLocalMode() {\n    try {\n      state.loading = true\n      state.useCloudStorage = false\n\n      this.loadRecords()\n      this.loadCategories()\n      this.loadSettings()\n\n    } finally {\n      state.loading = false\n    }\n  },\n\n  // 加载云端数据\n  async loadCloudData() {\n    try {\n      // 并行加载数据\n      const [recordsResult, categoriesResult, settingsResult] = await Promise.all([\n        CloudStorage.getRecords(),\n        CloudStorage.getCategories(),\n        CloudStorage.getUserSettings()\n      ])\n\n      // 处理记录数据\n      if (recordsResult.success) {\n        state.records = recordsResult.data.records.map(record => new Record(record))\n      }\n\n      // 处理分类数据\n      if (categoriesResult.success) {\n        const categories = categoriesResult.data\n        state.categories = {\n          expense: categories.filter(cat => cat.type === 'expense'),\n          income: categories.filter(cat => cat.type === 'income')\n        }\n      }\n\n      // 处理设置数据\n      if (settingsResult.success) {\n        state.settings = settingsResult.data || CloudStorage.getDefaultSettings()\n      }\n\n    } catch (error) {\n      console.error('加载云端数据失败:', error)\n      throw error\n    }\n  },\n  \n  // 加载记账记录\n  loadRecords() {\n    try {\n      const records = Storage.getRecords()\n      state.records = records.map(record => new Record(record))\n    } catch (error) {\n      console.error('加载记录失败:', error)\n      showToast('加载记录失败')\n    }\n  },\n  \n  // 保存记账记录\n  saveRecords() {\n    try {\n      const records = state.records.map(record => record.toObject())\n      Storage.setRecords(records)\n      return true\n    } catch (error) {\n      console.error('保存记录失败:', error)\n      showToast('保存记录失败')\n      return false\n    }\n  },\n  \n  // 添加记录\n  async addRecord(recordData) {\n    try {\n      const record = new Record(recordData)\n\n      if (state.useCloudStorage && state.isOnline) {\n        // 云端模式\n        const result = await CloudStorage.addRecord(record.toObject())\n        if (result.success) {\n          // 更新本地状态\n          record.id = result.data.id || record.id\n          state.records.unshift(record)\n          showToast('记录添加成功', 'success')\n          return record\n        } else {\n          throw new Error(result.error)\n        }\n      } else {\n        // 本地模式或离线模式\n        state.records.unshift(record)\n        this.saveRecords()\n\n        // 如果是离线状态，保存到离线队列\n        if (state.useCloudStorage && !state.isOnline) {\n          CloudStorage.saveOfflineRecord(record.toObject(), 'add')\n          showToast('记录已保存，将在联网后同步', 'success')\n        } else {\n          showToast('记录添加成功', 'success')\n        }\n        return record\n      }\n    } catch (error) {\n      console.error('添加记录失败:', error)\n      showToast('添加记录失败')\n      return null\n    }\n  },\n  \n  // 更新记录\n  async updateRecord(id, recordData) {\n    try {\n      const index = state.records.findIndex(record => record.id === id)\n      if (index === -1) {\n        throw new Error('记录不存在')\n      }\n\n      if (state.useCloudStorage && state.isOnline) {\n        // 云端模式\n        const result = await CloudStorage.updateRecord(id, recordData)\n        if (result.success) {\n          state.records[index].update(recordData)\n          showToast('记录更新成功', 'success')\n          return state.records[index]\n        } else {\n          throw new Error(result.error)\n        }\n      } else {\n        // 本地模式或离线模式\n        state.records[index].update(recordData)\n        this.saveRecords()\n\n        // 如果是离线状态，保存到离线队列\n        if (state.useCloudStorage && !state.isOnline) {\n          CloudStorage.saveOfflineRecord({ id, ...recordData }, 'update')\n          showToast('记录已更新，将在联网后同步', 'success')\n        } else {\n          showToast('记录更新成功', 'success')\n        }\n        return state.records[index]\n      }\n    } catch (error) {\n      console.error('更新记录失败:', error)\n      showToast('更新记录失败')\n      return null\n    }\n  },\n  \n  // 删除记录\n  async deleteRecord(id) {\n    try {\n      const index = state.records.findIndex(record => record.id === id)\n      if (index === -1) {\n        throw new Error('记录不存在')\n      }\n\n      if (state.useCloudStorage && state.isOnline) {\n        // 云端模式\n        const result = await CloudStorage.deleteRecord(id)\n        if (result.success) {\n          state.records.splice(index, 1)\n          showToast('记录删除成功', 'success')\n          return true\n        } else {\n          throw new Error(result.error)\n        }\n      } else {\n        // 本地模式或离线模式\n        const deletedRecord = state.records[index]\n        state.records.splice(index, 1)\n        this.saveRecords()\n\n        // 如果是离线状态，保存到离线队列\n        if (state.useCloudStorage && !state.isOnline) {\n          CloudStorage.saveOfflineRecord({ id }, 'delete')\n          showToast('记录已删除，将在联网后同步', 'success')\n        } else {\n          showToast('记录删除成功', 'success')\n        }\n        return true\n      }\n    } catch (error) {\n      console.error('删除记录失败:', error)\n      showToast('删除记录失败')\n      return false\n    }\n  },\n  \n  // 根据ID获取记录\n  getRecordById(id) {\n    return state.records.find(record => record.id === id)\n  },\n  \n  // 加载分类数据\n  loadCategories() {\n    try {\n      const categories = Storage.getCategories()\n      state.categories = categories\n    } catch (error) {\n      console.error('加载分类失败:', error)\n      showToast('加载分类失败')\n    }\n  },\n  \n  // 保存分类数据\n  saveCategories() {\n    try {\n      Storage.setCategories(state.categories)\n      return true\n    } catch (error) {\n      console.error('保存分类失败:', error)\n      showToast('保存分类失败')\n      return false\n    }\n  },\n  \n  // 根据ID获取分类\n  getCategoryById(id, type = 'expense') {\n    return state.categories[type].find(category => category.id === id)\n  },\n\n  // 获取默认分类\n  getDefaultCategories() {\n    return Storage.getDefaultCategories()\n  },\n  \n  // 加载设置\n  loadSettings() {\n    try {\n      state.settings = Storage.getSettings()\n    } catch (error) {\n      console.error('加载设置失败:', error)\n      showToast('加载设置失败')\n    }\n  },\n  \n  // 保存设置\n  saveSettings() {\n    try {\n      Storage.setSettings(state.settings)\n      showToast('设置保存成功', 'success')\n      return true\n    } catch (error) {\n      console.error('保存设置失败:', error)\n      showToast('保存设置失败')\n      return false\n    }\n  },\n  \n  // 更新设置\n  updateSettings(newSettings) {\n    Object.assign(state.settings, newSettings)\n    this.saveSettings()\n  },\n  \n  // 设置日期范围\n  setDateRange(start, end) {\n    state.dateRange.start = start\n    state.dateRange.end = end\n  },\n  \n  // 清空日期范围\n  clearDateRange() {\n    state.dateRange.start = ''\n    state.dateRange.end = ''\n  },\n  \n  // 设置加载状态\n  setLoading(loading) {\n    state.loading = loading\n  },\n\n  // 云端同步\n  async syncWithCloud() {\n    if (!state.useCloudStorage) {\n      return { success: false, message: '未启用云端存储' }\n    }\n\n    try {\n      state.syncing = true\n\n      // 检查网络状态\n      state.isOnline = await CloudStorage.isOnline()\n      if (!state.isOnline) {\n        throw new Error('网络连接不可用')\n      }\n\n      // 处理离线数据\n      const offlineResult = await CloudStorage.handleOfflineData()\n\n      // 同步云端数据\n      const syncResult = await CloudStorage.syncData(state.lastSyncTime)\n      if (syncResult.success) {\n        // 更新本地数据\n        await this.loadCloudData()\n\n        // 更新同步时间\n        state.lastSyncTime = new Date()\n        uni.setStorageSync('lastSyncTime', state.lastSyncTime)\n\n        showToast('数据同步成功', 'success')\n        return { success: true, message: '数据同步成功' }\n      } else {\n        throw new Error(syncResult.error)\n      }\n    } catch (error) {\n      console.error('数据同步失败:', error)\n      showToast('数据同步失败')\n      return { success: false, message: error.message }\n    } finally {\n      state.syncing = false\n    }\n  },\n\n  // 切换存储模式\n  async switchStorageMode(useCloud = true) {\n    try {\n      state.loading = true\n\n      if (useCloud && !state.useCloudStorage) {\n        // 切换到云端模式\n        state.useCloudStorage = true\n        await this.initCloudMode()\n        showToast('已切换到云端存储', 'success')\n      } else if (!useCloud && state.useCloudStorage) {\n        // 切换到本地模式\n        state.useCloudStorage = false\n        await this.initLocalMode()\n        showToast('已切换到本地存储', 'success')\n      }\n\n      // 保存设置\n      uni.setStorageSync('useCloudStorage', state.useCloudStorage)\n\n    } catch (error) {\n      console.error('切换存储模式失败:', error)\n      showToast('切换存储模式失败')\n    } finally {\n      state.loading = false\n    }\n  },\n\n  // 导出数据\n  async exportData() {\n    try {\n      if (state.useCloudStorage && state.isOnline) {\n        const result = await CloudStorage.exportData()\n        if (result.success) {\n          return result.data\n        } else {\n          throw new Error(result.error)\n        }\n      } else {\n        // 本地导出\n        return {\n          records: state.records.map(record => record.toObject()),\n          categories: state.categories,\n          settings: state.settings,\n          exportTime: new Date(),\n          version: '1.0.0'\n        }\n      }\n    } catch (error) {\n      console.error('导出数据失败:', error)\n      showToast('导出数据失败')\n      return null\n    }\n  },\n\n  // 导入数据\n  async importData(data) {\n    try {\n      if (state.useCloudStorage && state.isOnline) {\n        const result = await CloudStorage.importData(data)\n        if (result.success) {\n          // 重新加载数据\n          await this.loadCloudData()\n          showToast('数据导入成功', 'success')\n          return true\n        } else {\n          throw new Error(result.error)\n        }\n      } else {\n        // 本地导入\n        if (data.records) {\n          state.records = data.records.map(record => new Record(record))\n        }\n        if (data.categories) {\n          state.categories = data.categories\n        }\n        if (data.settings) {\n          state.settings = data.settings\n        }\n\n        // 保存到本地\n        this.saveRecords()\n        this.saveCategories()\n        this.saveSettings()\n\n        showToast('数据导入成功', 'success')\n        return true\n      }\n    } catch (error) {\n      console.error('导入数据失败:', error)\n      showToast('导入数据失败')\n      return false\n    }\n  },\n\n  // 清空所有数据\n  async clearAllData() {\n    try {\n      // 清空本地状态\n      state.records = []\n      state.categories = { expense: [], income: [] }\n      state.settings = {}\n\n      if (state.useCloudStorage && state.isOnline) {\n        // 云端清空（需要逐个删除）\n        const recordsResult = await CloudStorage.getRecords()\n        if (recordsResult.success) {\n          for (const record of recordsResult.data.records) {\n            await CloudStorage.deleteRecord(record._id)\n          }\n        }\n      } else {\n        // 本地清空\n        uni.removeStorageSync('bookkeeping_records')\n        uni.removeStorageSync('bookkeeping_categories')\n        uni.removeStorageSync('bookkeeping_settings')\n      }\n\n      showToast('数据清空成功', 'success')\n      return true\n    } catch (error) {\n      console.error('清空数据失败:', error)\n      showToast('清空数据失败')\n      return false\n    }\n  }\n}\n\n// 创建store实例\nconst store = {\n  state,\n  getters,\n  actions\n}\n\nexport default store\n"], "names": ["reactive", "computed", "Statistics", "CloudStorage", "uni", "Record", "Storage", "showToast"], "mappings": ";;;;;;AAWA,MAAM,QAAQA,cAAAA,SAAS;AAAA;AAAA,EAErB,SAAS,CAAE;AAAA;AAAA,EAEX,YAAY;AAAA,IACV,SAAS,CAAE;AAAA,IACX,QAAQ,CAAE;AAAA,EACX;AAAA;AAAA,EAED,UAAU,CAAE;AAAA;AAAA,EAEZ,WAAW;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA;AAAA,EAED,SAAS;AAAA;AAAA,EAET,SAAS;AAAA;AAAA,EAET,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA;AAAA,EAEd,UAAU;AACZ,CAAC;AAGD,MAAM,UAAU;AAAA;AAAA,EAEd,YAAYC,cAAQ,SAAC,MAAM,MAAM,OAAO;AAAA;AAAA,EAGxC,gBAAgBA,cAAAA;AAAAA,IAAS,MACvB,MAAM,QAAQ,OAAO,YAAU,OAAO,SAAS,SAAS;AAAA,EACzD;AAAA;AAAA,EAGD,eAAeA,cAAAA;AAAAA,IAAS,MACtB,MAAM,QAAQ,OAAO,YAAU,OAAO,SAAS,QAAQ;AAAA,EACxD;AAAA;AAAA,EAGD,mBAAmBA,cAAAA,SAAS,MAAM,MAAM,WAAW,OAAO;AAAA;AAAA,EAG1D,kBAAkBA,cAAAA,SAAS,MAAM,MAAM,WAAW,MAAM;AAAA;AAAA,EAGxD,YAAYA,cAAQ,SAAC,MAAM,IAAIC,aAAU,WAAC,MAAM,OAAO,CAAC;AAAA;AAAA,EAGxD,aAAaD,cAAAA;AAAAA,IAAS,MACpB,MAAM,QACH,OAAO,YAAU,OAAO,SAAS,QAAQ,EACzC,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,cAAcA,cAAAA;AAAAA,IAAS,MACrB,MAAM,QACH,OAAO,YAAU,OAAO,SAAS,SAAS,EAC1C,OAAO,CAAC,OAAO,WAAW,QAAQ,OAAO,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA,EAGD,WAAWA,cAAAA,SAAS,MAAM,QAAQ,YAAY,QAAQ,QAAQ,aAAa,KAAK;AAAA;AAAA,EAGhF,iBAAiBA,cAAQ,SAAC,MAAM;AAC9B,QAAI,CAAC,MAAM,UAAU,SAAS,CAAC,MAAM,UAAU,KAAK;AAClD,aAAO,MAAM;AAAA,IACd;AACD,WAAO,MAAM,QAAQ;AAAA,MAAO,YAC1B,OAAO,QAAQ,MAAM,UAAU,SAAS,OAAO,QAAQ,MAAM,UAAU;AAAA,IACxE;AAAA,EACL,CAAG;AACH;AAGA,MAAM,UAAU;AAAA;AAAA,EAEd,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,WAAW,MAAME,mBAAY,aAAC,SAAU;AAE9C,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAE3C,cAAM,KAAK,cAAe;AAAA,MAClC,OAAa;AAEL,cAAM,KAAK,cAAe;AAAA,MAC3B;AAAA,IACF,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,yBAAA,UAAU,KAAK;AAE7B,YAAM,KAAK,cAAe;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,UAAU;AAGhB,YAAMD,mBAAAA,aAAa,KAAM;AAGzB,YAAMA,mBAAAA,aAAa,kBAAmB;AAGtC,YAAM,KAAK,cAAe;AAG1B,YAAM,eAAe,oBAAI,KAAM;AAC/BC,oBAAAA,MAAI,eAAe,gBAAgB,MAAM,YAAY;AAAA,IAEtD,SAAQ,OAAO;AACdA,oBAAAA,8CAAc,cAAc,KAAK;AACjC,YAAM;AAAA,IACZ,UAAc;AACR,YAAM,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,UAAU;AAChB,YAAM,kBAAkB;AAExB,WAAK,YAAa;AAClB,WAAK,eAAgB;AACrB,WAAK,aAAc;AAAA,IAEzB,UAAc;AACR,YAAM,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,QAAI;AAEF,YAAM,CAAC,eAAe,kBAAkB,cAAc,IAAI,MAAM,QAAQ,IAAI;AAAA,QAC1ED,mBAAAA,aAAa,WAAY;AAAA,QACzBA,mBAAAA,aAAa,cAAe;AAAA,QAC5BA,mBAAAA,aAAa,gBAAiB;AAAA,MACtC,CAAO;AAGD,UAAI,cAAc,SAAS;AACzB,cAAM,UAAU,cAAc,KAAK,QAAQ,IAAI,YAAU,IAAIE,oBAAO,MAAM,CAAC;AAAA,MAC5E;AAGD,UAAI,iBAAiB,SAAS;AAC5B,cAAM,aAAa,iBAAiB;AACpC,cAAM,aAAa;AAAA,UACjB,SAAS,WAAW,OAAO,SAAO,IAAI,SAAS,SAAS;AAAA,UACxD,QAAQ,WAAW,OAAO,SAAO,IAAI,SAAS,QAAQ;AAAA,QACvD;AAAA,MACF;AAGD,UAAI,eAAe,SAAS;AAC1B,cAAM,WAAW,eAAe,QAAQF,mBAAAA,aAAa,mBAAoB;AAAA,MAC1E;AAAA,IAEF,SAAQ,OAAO;AACdC,oBAAAA,8CAAc,aAAa,KAAK;AAChC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,cAAc;AACZ,QAAI;AACF,YAAM,UAAUE,cAAO,QAAC,WAAY;AACpC,YAAM,UAAU,QAAQ,IAAI,YAAU,IAAID,aAAAA,OAAO,MAAM,CAAC;AAAA,IACzD,SAAQ,OAAO;AACdD,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,cAAc;AACZ,QAAI;AACF,YAAM,UAAU,MAAM,QAAQ,IAAI,YAAU,OAAO,UAAU;AAC7DD,oBAAO,QAAC,WAAW,OAAO;AAC1B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdF,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,UAAU,YAAY;AAC1B,QAAI;AACF,YAAM,SAAS,IAAIF,aAAM,OAAC,UAAU;AAEpC,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAE3C,cAAM,SAAS,MAAMF,mBAAY,aAAC,UAAU,OAAO,SAAQ,CAAE;AAC7D,YAAI,OAAO,SAAS;AAElB,iBAAO,KAAK,OAAO,KAAK,MAAM,OAAO;AACrC,gBAAM,QAAQ,QAAQ,MAAM;AAC5BI,wBAAS,UAAC,UAAU,SAAS;AAC7B,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAC7B;AAAA,MACT,OAAa;AAEL,cAAM,QAAQ,QAAQ,MAAM;AAC5B,aAAK,YAAa;AAGlB,YAAI,MAAM,mBAAmB,CAAC,MAAM,UAAU;AAC5CJ,6BAAAA,aAAa,kBAAkB,OAAO,SAAQ,GAAI,KAAK;AACvDI,wBAAS,UAAC,iBAAiB,SAAS;AAAA,QAC9C,OAAe;AACLA,wBAAS,UAAC,UAAU,SAAS;AAAA,QAC9B;AACD,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,IAAI,YAAY;AACjC,QAAI;AACF,YAAM,QAAQ,MAAM,QAAQ,UAAU,YAAU,OAAO,OAAO,EAAE;AAChE,UAAI,UAAU,IAAI;AAChB,cAAM,IAAI,MAAM,OAAO;AAAA,MACxB;AAED,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAE3C,cAAM,SAAS,MAAMJ,mBAAAA,aAAa,aAAa,IAAI,UAAU;AAC7D,YAAI,OAAO,SAAS;AAClB,gBAAM,QAAQ,KAAK,EAAE,OAAO,UAAU;AACtCI,wBAAS,UAAC,UAAU,SAAS;AAC7B,iBAAO,MAAM,QAAQ,KAAK;AAAA,QACpC,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAC7B;AAAA,MACT,OAAa;AAEL,cAAM,QAAQ,KAAK,EAAE,OAAO,UAAU;AACtC,aAAK,YAAa;AAGlB,YAAI,MAAM,mBAAmB,CAAC,MAAM,UAAU;AAC5CJ,6BAAY,aAAC,kBAAkB,EAAE,IAAI,GAAG,WAAU,GAAI,QAAQ;AAC9DI,wBAAS,UAAC,iBAAiB,SAAS;AAAA,QAC9C,OAAe;AACLA,wBAAS,UAAC,UAAU,SAAS;AAAA,QAC9B;AACD,eAAO,MAAM,QAAQ,KAAK;AAAA,MAC3B;AAAA,IACF,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,IAAI;AACrB,QAAI;AACF,YAAM,QAAQ,MAAM,QAAQ,UAAU,YAAU,OAAO,OAAO,EAAE;AAChE,UAAI,UAAU,IAAI;AAChB,cAAM,IAAI,MAAM,OAAO;AAAA,MACxB;AAED,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAE3C,cAAM,SAAS,MAAMJ,gCAAa,aAAa,EAAE;AACjD,YAAI,OAAO,SAAS;AAClB,gBAAM,QAAQ,OAAO,OAAO,CAAC;AAC7BI,wBAAS,UAAC,UAAU,SAAS;AAC7B,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAC7B;AAAA,MACT,OAAa;AAEL,cAAM,gBAAgB,MAAM,QAAQ,KAAK;AACzC,cAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,aAAK,YAAa;AAGlB,YAAI,MAAM,mBAAmB,CAAC,MAAM,UAAU;AAC5CJ,6BAAAA,aAAa,kBAAkB,EAAE,GAAE,GAAI,QAAQ;AAC/CI,wBAAS,UAAC,iBAAiB,SAAS;AAAA,QAC9C,OAAe;AACLA,wBAAS,UAAC,UAAU,SAAS;AAAA,QAC9B;AACD,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,cAAc,IAAI;AAChB,WAAO,MAAM,QAAQ,KAAK,YAAU,OAAO,OAAO,EAAE;AAAA,EACrD;AAAA;AAAA,EAGD,iBAAiB;AACf,QAAI;AACF,YAAM,aAAaD,cAAO,QAAC,cAAe;AAC1C,YAAM,aAAa;AAAA,IACpB,SAAQ,OAAO;AACdF,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,iBAAiB;AACf,QAAI;AACFD,4BAAQ,cAAc,MAAM,UAAU;AACtC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdF,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,gBAAgB,IAAI,OAAO,WAAW;AACpC,WAAO,MAAM,WAAW,IAAI,EAAE,KAAK,cAAY,SAAS,OAAO,EAAE;AAAA,EAClE;AAAA;AAAA,EAGD,uBAAuB;AACrB,WAAOD,cAAAA,QAAQ,qBAAsB;AAAA,EACtC;AAAA;AAAA,EAGD,eAAe;AACb,QAAI;AACF,YAAM,WAAWA,cAAO,QAAC,YAAa;AAAA,IACvC,SAAQ,OAAO;AACdF,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA;AAAA,EAGD,eAAe;AACb,QAAI;AACFD,4BAAQ,YAAY,MAAM,QAAQ;AAClCC,oBAAS,UAAC,UAAU,SAAS;AAC7B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,eAAe,aAAa;AAC1B,WAAO,OAAO,MAAM,UAAU,WAAW;AACzC,SAAK,aAAc;AAAA,EACpB;AAAA;AAAA,EAGD,aAAa,OAAO,KAAK;AACvB,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAGD,iBAAiB;AACf,UAAM,UAAU,QAAQ;AACxB,UAAM,UAAU,MAAM;AAAA,EACvB;AAAA;AAAA,EAGD,WAAW,SAAS;AAClB,UAAM,UAAU;AAAA,EACjB;AAAA;AAAA,EAGD,MAAM,gBAAgB;AACpB,QAAI,CAAC,MAAM,iBAAiB;AAC1B,aAAO,EAAE,SAAS,OAAO,SAAS,UAAW;AAAA,IAC9C;AAED,QAAI;AACF,YAAM,UAAU;AAGhB,YAAM,WAAW,MAAMJ,mBAAY,aAAC,SAAU;AAC9C,UAAI,CAAC,MAAM,UAAU;AACnB,cAAM,IAAI,MAAM,SAAS;AAAA,MAC1B;AAGD,YAAM,gBAAgB,MAAMA,mBAAY,aAAC,kBAAmB;AAG5D,YAAM,aAAa,MAAMA,mBAAAA,aAAa,SAAS,MAAM,YAAY;AACjE,UAAI,WAAW,SAAS;AAEtB,cAAM,KAAK,cAAe;AAG1B,cAAM,eAAe,oBAAI,KAAM;AAC/BC,sBAAAA,MAAI,eAAe,gBAAgB,MAAM,YAAY;AAErDG,sBAAS,UAAC,UAAU,SAAS;AAC7B,eAAO,EAAE,SAAS,MAAM,SAAS,SAAU;AAAA,MACnD,OAAa;AACL,cAAM,IAAI,MAAM,WAAW,KAAK;AAAA,MACjC;AAAA,IACF,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAS;AAAA,IACvD,UAAc;AACR,YAAM,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,kBAAkB,WAAW,MAAM;AACvC,QAAI;AACF,YAAM,UAAU;AAEhB,UAAI,YAAY,CAAC,MAAM,iBAAiB;AAEtC,cAAM,kBAAkB;AACxB,cAAM,KAAK,cAAe;AAC1BA,sBAAS,UAAC,YAAY,SAAS;AAAA,MAChC,WAAU,CAAC,YAAY,MAAM,iBAAiB;AAE7C,cAAM,kBAAkB;AACxB,cAAM,KAAK,cAAe;AAC1BA,sBAAS,UAAC,YAAY,SAAS;AAAA,MAChC;AAGDH,oBAAAA,MAAI,eAAe,mBAAmB,MAAM,eAAe;AAAA,IAE5D,SAAQ,OAAO;AACdA,oBAAAA,8CAAc,aAAa,KAAK;AAChCG,oBAAAA,UAAU,UAAU;AAAA,IAC1B,UAAc;AACR,YAAM,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa;AACjB,QAAI;AACF,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAC3C,cAAM,SAAS,MAAMJ,mBAAY,aAAC,WAAY;AAC9C,YAAI,OAAO,SAAS;AAClB,iBAAO,OAAO;AAAA,QACxB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAC7B;AAAA,MACT,OAAa;AAEL,eAAO;AAAA,UACL,SAAS,MAAM,QAAQ,IAAI,YAAU,OAAO,UAAU;AAAA,UACtD,YAAY,MAAM;AAAA,UAClB,UAAU,MAAM;AAAA,UAChB,YAAY,oBAAI,KAAM;AAAA,UACtB,SAAS;AAAA,QACV;AAAA,MACF;AAAA,IACF,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,WAAW,MAAM;AACrB,QAAI;AACF,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAC3C,cAAM,SAAS,MAAMJ,gCAAa,WAAW,IAAI;AACjD,YAAI,OAAO,SAAS;AAElB,gBAAM,KAAK,cAAe;AAC1BI,wBAAS,UAAC,UAAU,SAAS;AAC7B,iBAAO;AAAA,QACjB,OAAe;AACL,gBAAM,IAAI,MAAM,OAAO,KAAK;AAAA,QAC7B;AAAA,MACT,OAAa;AAEL,YAAI,KAAK,SAAS;AAChB,gBAAM,UAAU,KAAK,QAAQ,IAAI,YAAU,IAAIF,oBAAO,MAAM,CAAC;AAAA,QAC9D;AACD,YAAI,KAAK,YAAY;AACnB,gBAAM,aAAa,KAAK;AAAA,QACzB;AACD,YAAI,KAAK,UAAU;AACjB,gBAAM,WAAW,KAAK;AAAA,QACvB;AAGD,aAAK,YAAa;AAClB,aAAK,eAAgB;AACrB,aAAK,aAAc;AAEnBE,sBAAS,UAAC,UAAU,SAAS;AAC7B,eAAO;AAAA,MACR;AAAA,IACF,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,eAAe;AACnB,QAAI;AAEF,YAAM,UAAU,CAAE;AAClB,YAAM,aAAa,EAAE,SAAS,CAAA,GAAI,QAAQ,CAAA,EAAI;AAC9C,YAAM,WAAW,CAAE;AAEnB,UAAI,MAAM,mBAAmB,MAAM,UAAU;AAE3C,cAAM,gBAAgB,MAAMJ,mBAAY,aAAC,WAAY;AACrD,YAAI,cAAc,SAAS;AACzB,qBAAW,UAAU,cAAc,KAAK,SAAS;AAC/C,kBAAMA,gCAAa,aAAa,OAAO,GAAG;AAAA,UAC3C;AAAA,QACF;AAAA,MACT,OAAa;AAELC,sBAAG,MAAC,kBAAkB,qBAAqB;AAC3CA,sBAAG,MAAC,kBAAkB,wBAAwB;AAC9CA,sBAAG,MAAC,kBAAkB,sBAAsB;AAAA,MAC7C;AAEDG,oBAAS,UAAC,UAAU,SAAS;AAC7B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,WAAW,KAAK;AAC9BG,oBAAAA,UAAU,QAAQ;AAClB,aAAO;AAAA,IACR;AAAA,EACF;AACH;AAGK,MAAC,QAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF;;"}