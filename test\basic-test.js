/**
 * 基础功能测试
 * 这个文件用于测试记账本的核心功能
 */

// 模拟uni对象
global.uni = {
  setStorageSync: (key, data) => {
    console.log(`存储数据: ${key}`, data)
  },
  getStorageSync: (key) => {
    console.log(`获取数据: ${key}`)
    return null
  },
  showToast: (options) => {
    console.log(`显示提示: ${options.title}`)
  },
  showLoading: (options) => {
    console.log(`显示加载: ${options.title}`)
  },
  hideLoading: () => {
    console.log('隐藏加载')
  }
}

// 导入测试模块
import { Record, Category, Statistics } from '../utils/models.js'
import Storage from '../utils/storage.js'
import { formatAmount, formatDate, getToday } from '../utils/helpers.js'

// 测试数据
const testRecordData = {
  type: 'expense',
  amount: 100.50,
  categoryId: 'cat1',
  categoryName: '餐饮',
  categoryIcon: '🍽️',
  categoryColor: '#FF9800',
  note: '午餐',
  date: '2024-01-15',
  time: '12:30'
}

const testCategoryData = {
  id: 'cat1',
  name: '餐饮',
  icon: '🍽️',
  color: '#FF9800',
  type: 'expense'
}

// 测试函数
function testRecord() {
  console.log('\n=== 测试 Record 类 ===')
  
  try {
    // 创建记录
    const record = new Record(testRecordData)
    console.log('✓ 记录创建成功:', record.id)
    
    // 测试更新
    record.update({ amount: 120.00, note: '晚餐' })
    console.log('✓ 记录更新成功:', record.amount, record.note)
    
    // 测试转换为对象
    const obj = record.toObject()
    console.log('✓ 转换为对象成功:', obj.amount)
    
    // 测试验证
    const isValid = record.validate()
    console.log('✓ 记录验证:', isValid ? '通过' : '失败')
    
  } catch (error) {
    console.error('✗ Record 测试失败:', error.message)
  }
}

function testCategory() {
  console.log('\n=== 测试 Category 类 ===')
  
  try {
    // 创建分类
    const category = new Category(testCategoryData)
    console.log('✓ 分类创建成功:', category.name)
    
    // 测试更新
    category.update({ name: '美食', color: '#E91E63' })
    console.log('✓ 分类更新成功:', category.name, category.color)
    
    // 测试转换为对象
    const obj = category.toObject()
    console.log('✓ 转换为对象成功:', obj.name)
    
  } catch (error) {
    console.error('✗ Category 测试失败:', error.message)
  }
}

function testStatistics() {
  console.log('\n=== 测试 Statistics 类 ===')
  
  try {
    // 创建测试记录
    const records = [
      new Record({ ...testRecordData, amount: 100, type: 'expense' }),
      new Record({ ...testRecordData, amount: 200, type: 'income' }),
      new Record({ ...testRecordData, amount: 50, type: 'expense' })
    ]
    
    // 创建统计
    const stats = new Statistics(records)
    console.log('✓ 统计创建成功')
    
    // 测试总收入
    const totalIncome = stats.getTotalIncome()
    console.log('✓ 总收入:', totalIncome)
    
    // 测试总支出
    const totalExpense = stats.getTotalExpense()
    console.log('✓ 总支出:', totalExpense)
    
    // 测试净收入
    const netIncome = stats.getNetIncome()
    console.log('✓ 净收入:', netIncome)
    
    // 测试按分类统计
    const byCategory = stats.getByCategory('expense')
    console.log('✓ 按分类统计:', byCategory)
    
  } catch (error) {
    console.error('✗ Statistics 测试失败:', error.message)
  }
}

function testHelpers() {
  console.log('\n=== 测试工具函数 ===')
  
  try {
    // 测试金额格式化
    const amount = formatAmount(1234.56)
    console.log('✓ 金额格式化:', amount)
    
    // 测试日期格式化
    const date = formatDate(new Date(), 'YYYY年MM月DD日')
    console.log('✓ 日期格式化:', date)
    
    // 测试获取今天
    const today = getToday()
    console.log('✓ 获取今天:', today)
    
  } catch (error) {
    console.error('✗ 工具函数测试失败:', error.message)
  }
}

function testStorage() {
  console.log('\n=== 测试存储功能 ===')
  
  try {
    // 测试默认分类
    const defaultCategories = Storage.getDefaultCategories()
    console.log('✓ 获取默认分类成功:', Object.keys(defaultCategories))
    
    // 测试默认设置
    const defaultSettings = Storage.getDefaultSettings()
    console.log('✓ 获取默认设置成功:', defaultSettings.currency)
    
  } catch (error) {
    console.error('✗ Storage 测试失败:', error.message)
  }
}

// 运行所有测试
function runAllTests() {
  console.log('开始运行记账本功能测试...\n')
  
  testRecord()
  testCategory()
  testStatistics()
  testHelpers()
  testStorage()
  
  console.log('\n=== 测试完成 ===')
  console.log('如果看到 ✓ 表示测试通过，✗ 表示测试失败')
  console.log('请检查失败的测试并修复相关问题')
}

// 导出测试函数
export {
  testRecord,
  testCategory,
  testStatistics,
  testHelpers,
  testStorage,
  runAllTests
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  runAllTests()
}
