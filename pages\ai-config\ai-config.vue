<template>
  <view class="ai-config-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="icon">←</text>
        </view>
        <view class="nav-title">AI配置检查</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 配置状态 -->
    <view class="config-status">
      <view class="status-card" :class="{ success: configValid, error: !configValid }">
        <view class="status-icon">
          <text>{{ configValid ? '✅' : '❌' }}</text>
        </view>
        <view class="status-content">
          <text class="status-title">
            {{ configValid ? 'AI配置正常' : 'AI配置异常' }}
          </text>
          <text class="status-desc">
            {{ configValid ? '可以正常使用AI记账功能' : '需要配置阿里云百炼API密钥' }}
          </text>
        </view>
      </view>
    </view>

    <!-- 配置详情 -->
    <view class="config-details">
      <view class="section-title">配置详情</view>
      
      <view class="detail-item">
        <view class="detail-label">API密钥</view>
        <view class="detail-value" :class="{ success: hasApiKey, error: !hasApiKey }">
          {{ hasApiKey ? '已配置' : '未配置' }}
        </view>
      </view>
      
      <view class="detail-item">
        <view class="detail-label">文本模型</view>
        <view class="detail-value">{{ textModel }}</view>
      </view>
      
      <view class="detail-item">
        <view class="detail-label">图像模型</view>
        <view class="detail-value">{{ imageModel }}</view>
      </view>
      
      <view class="detail-item">
        <view class="detail-label">API地址</view>
        <view class="detail-value">{{ apiBaseURL }}</view>
      </view>
    </view>

    <!-- 测试功能 -->
    <view class="test-section">
      <view class="section-title">功能测试</view>
      
      <view class="test-buttons">
        <button 
          class="test-btn" 
          :disabled="!configValid || testing"
          @click="testTextAnalysis"
        >
          {{ testing === 'text' ? '测试中...' : '测试文本分析' }}
        </button>
        
        <button 
          class="test-btn" 
          :disabled="!configValid || testing"
          @click="testImageAnalysis"
        >
          {{ testing === 'image' ? '测试中...' : '测试图像分析' }}
        </button>
      </view>
      
      <!-- 测试结果 -->
      <view v-if="testResult" class="test-result">
        <view class="result-title">测试结果</view>
        <view class="result-content">
          <text>{{ testResult }}</text>
        </view>
      </view>
    </view>

    <!-- 配置指南 -->
    <view class="guide-section">
      <view class="section-title">配置指南</view>
      
      <view class="guide-steps">
        <view class="step-item">
          <view class="step-number">1</view>
          <view class="step-content">
            <text class="step-title">获取API密钥</text>
            <text class="step-desc">访问阿里云百炼控制台获取API密钥</text>
          </view>
        </view>
        
        <view class="step-item">
          <view class="step-number">2</view>
          <view class="step-content">
            <text class="step-title">配置密钥</text>
            <text class="step-desc">在utils/ai-service.js中配置您的API密钥</text>
          </view>
        </view>
        
        <view class="step-item">
          <view class="step-number">3</view>
          <view class="step-content">
            <text class="step-title">测试功能</text>
            <text class="step-desc">使用上方的测试按钮验证配置是否正确</text>
          </view>
        </view>
      </view>
      
      <view class="guide-actions">
        <button class="guide-btn" @click="openGuide">查看详细指南</button>
        <button class="guide-btn" @click="refreshConfig">刷新配置</button>
      </view>
    </view>
  </view>
</template>

<script>
import AIService from '../../utils/ai-service.js'

export default {
  data() {
    return {
      configValid: false,
      hasApiKey: false,
      textModel: '',
      imageModel: '',
      apiBaseURL: '',
      testing: null, // 'text' | 'image' | null
      testResult: ''
    }
  },

  onLoad() {
    this.checkConfig()
  },

  methods: {
    // 检查配置
    checkConfig() {
      try {
        this.configValid = AIService.isConfigValid()
        this.hasApiKey = !!AIService.apiKey && AIService.apiKey !== 'YOUR_BAILIAN_API_KEY_HERE'
        this.textModel = AIService.textModel || 'qwen-plus'
        this.imageModel = AIService.imageModel || 'qwen-vl-plus'
        this.apiBaseURL = AIService.baseURL || 'https://dashscope.aliyuncs.com/api/v1'
      } catch (error) {
        console.error('检查配置失败:', error)
        this.configValid = false
        this.hasApiKey = false
      }
    },

    // 测试文本分析
    async testTextAnalysis() {
      this.testing = 'text'
      this.testResult = ''
      
      try {
        const result = await AIService.analyzeTextForBookkeeping('今天在麦当劳花了25块钱吃午餐')
        
        if (result.success) {
          this.testResult = `文本分析成功！识别结果：${result.data.amount}元，${result.data.categoryName}，${result.data.note}`
        } else {
          this.testResult = `文本分析失败：${result.error}`
        }
      } catch (error) {
        this.testResult = `文本分析异常：${error.message}`
      } finally {
        this.testing = null
      }
    },

    // 测试图像分析
    async testImageAnalysis() {
      this.testing = 'image'
      this.testResult = ''
      
      try {
        // 使用一个简单的测试图片base64
        const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        const result = await AIService.analyzeImageForBookkeeping(testImageBase64)
        
        if (result.success) {
          this.testResult = `图像分析成功！识别结果：${result.data.amount}元，${result.data.categoryName}，${result.data.note}`
        } else {
          this.testResult = `图像分析失败：${result.error}`
        }
      } catch (error) {
        this.testResult = `图像分析异常：${error.message}`
      } finally {
        this.testing = null
      }
    },

    // 刷新配置
    refreshConfig() {
      this.checkConfig()
      uni.showToast({
        title: '配置已刷新',
        icon: 'success'
      })
    },

    // 打开指南
    openGuide() {
      uni.showModal({
        title: '配置指南',
        content: '请查看项目根目录的 AI_SETUP_GUIDE.md 文件获取详细的配置指南',
        showCancel: false
      })
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.ai-config-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 20px;
}

/* 顶部导航 */
.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
  padding-top: var(--status-bar-height);
}

.nav-left, .nav-right {
  width: 60px;
}

.nav-left .icon {
  font-size: 20px;
  color: white;
  font-weight: bold;
}

.nav-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

/* 配置状态 */
.config-status {
  padding: 20px 15px;
}

.status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-card.success {
  border-left: 4px solid #4CAF50;
}

.status-card.error {
  border-left: 4px solid #f44336;
}

.status-icon text {
  font-size: 32px;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 14px;
  color: #666;
  display: block;
}

/* 配置详情 */
.config-details {
  margin: 0 15px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: #666;
  font-size: 14px;
}

.detail-value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.detail-value.success {
  color: #4CAF50;
}

.detail-value.error {
  color: #f44336;
}

/* 测试功能 */
.test-section {
  margin: 0 15px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.test-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.test-btn {
  flex: 1;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
}

.test-btn:disabled {
  background: #ccc;
}

.test-result {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #667eea;
}

.result-title {
  font-size: 14px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 8px;
}

.result-content {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
}

/* 配置指南 */
.guide-section {
  margin: 0 15px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
}

.guide-steps {
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #667eea;
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 12px;
  color: #666;
  display: block;
  line-height: 1.4;
}

.guide-actions {
  display: flex;
  gap: 15px;
}

.guide-btn {
  flex: 1;
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid #667eea;
  border-radius: 8px;
  padding: 10px;
  font-size: 14px;
}
</style>
