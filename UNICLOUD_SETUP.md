# uniCloud 云函数配置指南

## 问题描述
项目接入uniCloud进行云开发时，出现云函数调用失败的问题。主要原因是云函数没有正确配置和部署。

## 解决方案

### 1. 云函数文件结构
已创建完整的云函数结构：

```
uniCloud-alipay/
├── cloudfunctions/
│   ├── bookkeeping/           # 主要业务云函数
│   │   ├── index.js          # 云函数入口文件
│   │   └── package.json      # 依赖配置
│   └── init-db/              # 数据库初始化云函数
│       ├── index.js          # 初始化脚本
│       └── package.json      # 依赖配置
└── database/                 # 数据库Schema定义
    ├── records.schema.json   # 记录表结构
    ├── categories.schema.json # 分类表结构
    └── userSettings.schema.json # 用户设置表结构
```

### 2. 云函数功能说明

#### bookkeeping 云函数
主要的业务逻辑云函数，支持以下操作：
- `addRecord` - 添加记账记录
- `updateRecord` - 更新记账记录
- `deleteRecord` - 删除记账记录
- `getRecords` - 获取记录列表（支持分页和筛选）
- `getStatistics` - 获取统计数据
- `addCategory` - 添加分类
- `updateCategory` - 更新分类
- `deleteCategory` - 删除分类
- `getCategories` - 获取分类列表
- `getUserSettings` - 获取用户设置
- `updateUserSettings` - 更新用户设置

#### init-db 云函数
数据库初始化云函数，用于：
- 创建默认分类数据
- 初始化数据库集合
- 设置基础数据

### 3. 部署步骤

#### 步骤1：配置uniCloud服务空间
1. 在HBuilderX中打开项目
2. 右键点击 `uniCloud-alipay` 目录
3. 选择"关联云服务空间"
4. 创建或选择已有的服务空间

#### 步骤2：上传云函数
1. 右键点击 `uniCloud-alipay/cloudfunctions/bookkeeping` 目录
2. 选择"上传并运行"
3. 等待上传完成

4. 右键点击 `uniCloud-alipay/cloudfunctions/init-db` 目录
5. 选择"上传并运行"
6. 等待上传完成

#### 步骤3：创建数据库集合
1. 在uniCloud web控制台中创建以下集合：
   - `records` - 记账记录
   - `categories` - 分类数据
   - `userSettings` - 用户设置

2. 或者使用Schema文件自动创建：
   - 右键点击 `uniCloud-alipay/database` 目录
   - 选择"上传Schema及扩展校验函数"

#### 步骤4：初始化数据
1. 在HBuilderX中运行 `init-db` 云函数
2. 或者在应用中调用初始化功能

### 4. 测试云函数

#### 方法1：使用HBuilderX测试
1. 右键点击云函数目录
2. 选择"本地运行"
3. 在弹出的测试界面中输入测试参数

#### 方法2：使用应用内测试
1. 运行应用到微信开发者工具
2. 在首页点击"测试"按钮
3. 查看控制台输出结果

#### 方法3：使用测试脚本
```bash
node test-app.js
```

### 5. 常见问题解决

#### 问题1：云函数调用失败
- 检查manifest.json中的uniCloud配置
- 确认云函数已正确上传
- 检查网络连接

#### 问题2：数据库操作失败
- 确认数据库集合已创建
- 检查数据格式是否符合Schema定义
- 查看云函数日志

#### 问题3：权限问题
- 检查服务空间配置
- 确认用户身份认证状态

### 6. 调试技巧

#### 查看云函数日志
1. 在uniCloud web控制台中
2. 进入"云函数" -> "日志"
3. 查看详细的执行日志

#### 本地调试
1. 使用HBuilderX的本地运行功能
2. 在云函数代码中添加console.log
3. 查看调试输出

### 7. 性能优化建议

1. **批量操作**：对于大量数据操作，使用批量接口
2. **缓存策略**：对于不经常变化的数据（如分类），使用本地缓存
3. **分页查询**：避免一次性查询大量数据
4. **索引优化**：为常用查询字段创建数据库索引

### 8. 安全注意事项

1. **数据验证**：在云函数中进行严格的数据验证
2. **权限控制**：实现用户级别的数据隔离
3. **SQL注入防护**：使用参数化查询
4. **敏感信息保护**：不在客户端存储敏感信息

## 总结

通过以上配置，uniCloud云函数应该能够正常工作。如果仍然遇到问题，请：

1. 检查HBuilderX版本是否支持uniCloud
2. 确认网络连接正常
3. 查看详细的错误日志
4. 参考uniCloud官方文档

完成配置后，应用将能够：
- ✅ 正常调用云函数
- ✅ 进行数据的增删改查
- ✅ 实现云端数据同步
- ✅ 支持离线数据缓存
