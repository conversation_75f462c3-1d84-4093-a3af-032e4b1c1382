'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'addRecord':
        return await addRecord(data);
      case 'updateRecord':
        return await updateRecord(data);
      case 'deleteRecord':
        return await deleteRecord(data);
      case 'getRecords':
        return await getRecords(data);
      case 'getStatistics':
        return await getStatistics(data);
      case 'addCategory':
        return await addCategory(data);
      case 'updateCategory':
        return await updateCategory(data);
      case 'deleteCategory':
        return await deleteCategory(data);
      case 'getCategories':
        return await getCategories(data);
      case 'getUserSettings':
        return await getUserSettings(data);
      case 'updateUserSettings':
        return await updateUserSettings(data);
      default:
        return {
          code: 400,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 添加记录
async function addRecord(data) {
  const record = {
    ...data,
    id: generateId(),
    createTime: new Date(),
    updateTime: new Date()
  };
  
  const result = await db.collection('records').add(record);
  
  return {
    code: 200,
    message: '添加成功',
    data: { id: result.id, ...record }
  };
}

// 更新记录
async function updateRecord(data) {
  const { id, ...updateData } = data;
  updateData.updateTime = new Date();
  
  const result = await db.collection('records').doc(id).update(updateData);
  
  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 删除记录
async function deleteRecord(data) {
  const { id } = data;
  
  const result = await db.collection('records').doc(id).remove();
  
  return {
    code: 200,
    message: '删除成功',
    data: result
  };
}

// 获取记录列表
async function getRecords(data) {
  const { 
    page = 1, 
    limit = 20, 
    type, 
    categoryId, 
    startDate, 
    endDate,
    keyword 
  } = data;
  
  let query = db.collection('records');
  
  // 添加筛选条件
  if (type) {
    query = query.where({ type });
  }
  
  if (categoryId) {
    query = query.where({ categoryId });
  }
  
  if (startDate && endDate) {
    query = query.where({
      date: db.command.gte(startDate).and(db.command.lte(endDate))
    });
  }
  
  if (keyword) {
    query = query.where({
      note: new RegExp(keyword, 'i')
    });
  }
  
  // 分页查询
  const result = await query
    .orderBy('date', 'desc')
    .orderBy('createTime', 'desc')
    .skip((page - 1) * limit)
    .limit(limit)
    .get();
  
  // 获取总数
  const countResult = await query.count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      records: result.data,
      total: countResult.total,
      page,
      limit
    }
  };
}

// 获取统计数据
async function getStatistics(data) {
  const { startDate, endDate, type } = data;
  
  let query = db.collection('records');
  
  if (startDate && endDate) {
    query = query.where({
      date: db.command.gte(startDate).and(db.command.lte(endDate))
    });
  }
  
  if (type) {
    query = query.where({ type });
  }
  
  const result = await query.get();
  const records = result.data;
  
  // 计算统计数据
  const statistics = {
    totalIncome: 0,
    totalExpense: 0,
    totalBalance: 0,
    recordCount: records.length,
    categoryStats: {},
    dailyStats: {}
  };
  
  records.forEach(record => {
    if (record.type === 'income') {
      statistics.totalIncome += record.amount;
    } else {
      statistics.totalExpense += record.amount;
    }
    
    // 分类统计
    if (!statistics.categoryStats[record.categoryId]) {
      statistics.categoryStats[record.categoryId] = {
        categoryName: record.categoryName,
        categoryIcon: record.categoryIcon,
        categoryColor: record.categoryColor,
        amount: 0,
        count: 0
      };
    }
    statistics.categoryStats[record.categoryId].amount += record.amount;
    statistics.categoryStats[record.categoryId].count += 1;
    
    // 日期统计
    if (!statistics.dailyStats[record.date]) {
      statistics.dailyStats[record.date] = {
        income: 0,
        expense: 0,
        count: 0
      };
    }
    if (record.type === 'income') {
      statistics.dailyStats[record.date].income += record.amount;
    } else {
      statistics.dailyStats[record.date].expense += record.amount;
    }
    statistics.dailyStats[record.date].count += 1;
  });
  
  statistics.totalBalance = statistics.totalIncome - statistics.totalExpense;
  
  return {
    code: 200,
    message: '获取成功',
    data: statistics
  };
}

// 添加分类
async function addCategory(data) {
  const category = {
    ...data,
    id: generateId(),
    createTime: new Date(),
    updateTime: new Date()
  };
  
  const result = await db.collection('categories').add(category);
  
  return {
    code: 200,
    message: '添加成功',
    data: { id: result.id, ...category }
  };
}

// 更新分类
async function updateCategory(data) {
  const { id, ...updateData } = data;
  updateData.updateTime = new Date();
  
  const result = await db.collection('categories').doc(id).update(updateData);
  
  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 删除分类
async function deleteCategory(data) {
  const { id } = data;
  
  const result = await db.collection('categories').doc(id).remove();
  
  return {
    code: 200,
    message: '删除成功',
    data: result
  };
}

// 获取分类列表
async function getCategories(data) {
  const { type } = data;
  
  let query = db.collection('categories');
  
  if (type) {
    query = query.where({ type });
  }
  
  const result = await query.orderBy('sort', 'asc').get();
  
  return {
    code: 200,
    message: '获取成功',
    data: result.data
  };
}

// 获取用户设置
async function getUserSettings(data) {
  const { userId } = data;
  
  const result = await db.collection('userSettings').where({ userId }).get();
  
  if (result.data.length === 0) {
    // 创建默认设置
    const defaultSettings = {
      userId,
      currency: 'CNY',
      budgetAlert: true,
      monthlyBudget: 0,
      theme: 'light',
      createTime: new Date(),
      updateTime: new Date()
    };
    
    await db.collection('userSettings').add(defaultSettings);
    
    return {
      code: 200,
      message: '获取成功',
      data: defaultSettings
    };
  }
  
  return {
    code: 200,
    message: '获取成功',
    data: result.data[0]
  };
}

// 更新用户设置
async function updateUserSettings(data) {
  const { userId, ...updateData } = data;
  updateData.updateTime = new Date();
  
  const result = await db.collection('userSettings')
    .where({ userId })
    .update(updateData);
  
  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 生成唯一ID
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
