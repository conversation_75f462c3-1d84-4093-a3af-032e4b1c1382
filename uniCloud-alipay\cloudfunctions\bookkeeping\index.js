'use strict';

const db = uniCloud.database();

exports.main = async (event, context) => {
  console.log('云函数调用参数:', event);

  const { action, data } = event || {};

  if (!action) {
    return {
      code: 400,
      message: '缺少action参数'
    };
  }

  try {
    switch (action) {
      case 'addRecord':
        return await addRecord(data);
      case 'updateRecord':
        return await updateRecord(data);
      case 'deleteRecord':
        return await deleteRecord(data);
      case 'getRecords':
        return await getRecords(data);
      case 'getStatistics':
        return await getStatistics(data);
      case 'addCategory':
        return await addCategory(data);
      case 'updateCategory':
        return await updateCategory(data);
      case 'deleteCategory':
        return await deleteCategory(data);
      case 'getCategories':
        return await getCategories(data);
      case 'getUserSettings':
        return await getUserSettings(data);
      case 'updateUserSettings':
        return await updateUserSettings(data);
      default:
        return {
          code: 400,
          message: '未知操作类型: ' + action
        };
    }
  } catch (error) {
    console.error('云函数执行错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 添加记录
async function addRecord(data) {
  const record = {
    ...data,
    id: generateId(),
    createTime: new Date(),
    updateTime: new Date()
  };
  
  const result = await db.collection('records').add(record);
  
  return {
    code: 200,
    message: '添加成功',
    data: { id: result.id, ...record }
  };
}

// 更新记录
async function updateRecord(data) {
  const { id, ...updateData } = data;
  updateData.updateTime = new Date();
  
  const result = await db.collection('records').doc(id).update(updateData);
  
  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 删除记录
async function deleteRecord(data) {
  const { id } = data;
  
  const result = await db.collection('records').doc(id).remove();
  
  return {
    code: 200,
    message: '删除成功',
    data: result
  };
}

// 获取记录列表
async function getRecords(data) {
  const {
    page = 1,
    limit = 20,
    type
  } = data || {};

  let query = db.collection('records');

  // 简单的类型筛选
  if (type) {
    query = query.where({ type: type });
  }

  // 分页查询
  const result = await query
    .orderBy('createTime', 'desc')
    .skip((page - 1) * limit)
    .limit(limit)
    .get();

  return {
    code: 200,
    message: '获取成功',
    data: {
      records: result.data || [],
      total: result.data ? result.data.length : 0,
      page: page,
      limit: limit
    }
  };
}

// 获取统计数据
async function getStatistics(data) {
  const result = await db.collection('records').get();
  const records = result.data || [];

  // 计算基本统计数据
  let totalIncome = 0;
  let totalExpense = 0;

  records.forEach(record => {
    if (record.type === 'income') {
      totalIncome += record.amount || 0;
    } else if (record.type === 'expense') {
      totalExpense += record.amount || 0;
    }
  });

  return {
    code: 200,
    message: '获取成功',
    data: {
      totalIncome: totalIncome,
      totalExpense: totalExpense,
      totalBalance: totalIncome - totalExpense,
      recordCount: records.length
    }
  };
}

// 添加分类
async function addCategory(data) {
  const category = {
    ...data,
    createTime: new Date()
  };

  const result = await db.collection('categories').add(category);

  return {
    code: 200,
    message: '添加成功',
    data: result
  };
}

// 更新分类
async function updateCategory(data) {
  const { id, ...updateData } = data;

  const result = await db.collection('categories').doc(id).update(updateData);

  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 删除分类
async function deleteCategory(data) {
  const { id } = data;

  const result = await db.collection('categories').doc(id).remove();

  return {
    code: 200,
    message: '删除成功',
    data: result
  };
}

// 获取分类列表
async function getCategories(data) {
  const { type } = data || {};

  let query = db.collection('categories');

  if (type) {
    query = query.where({ type: type });
  }

  const result = await query.get();

  return {
    code: 200,
    message: '获取成功',
    data: result.data || []
  };
}

// 获取用户设置
async function getUserSettings(data) {
  const { userId } = data || {};

  if (!userId) {
    return {
      code: 400,
      message: '缺少userId参数'
    };
  }

  const result = await db.collection('userSettings').where({ userId: userId }).get();

  if (result.data && result.data.length > 0) {
    return {
      code: 200,
      message: '获取成功',
      data: result.data[0]
    };
  }

  // 返回默认设置
  const defaultSettings = {
    userId: userId,
    currency: 'CNY',
    budgetAlert: true,
    monthlyBudget: 0,
    theme: 'light'
  };

  return {
    code: 200,
    message: '获取成功',
    data: defaultSettings
  };
}

// 更新用户设置
async function updateUserSettings(data) {
  const { userId, ...updateData } = data || {};

  if (!userId) {
    return {
      code: 400,
      message: '缺少userId参数'
    };
  }

  const result = await db.collection('userSettings')
    .where({ userId: userId })
    .update(updateData);

  return {
    code: 200,
    message: '更新成功',
    data: result
  };
}

// 生成唯一ID
function generateId() {
  return 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}
