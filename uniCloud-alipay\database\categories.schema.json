{"bsonType": "object", "description": "分类表", "required": ["id", "name", "type"], "properties": {"_id": {"description": "存储文档 ID（自动生成）"}, "id": {"bsonType": "string", "description": "分类唯一标识"}, "name": {"bsonType": "string", "description": "分类名称"}, "icon": {"bsonType": "string", "description": "分类图标"}, "color": {"bsonType": "string", "description": "分类颜色"}, "type": {"bsonType": "string", "enum": ["income", "expense"], "description": "分类类型：收入或支出"}, "sort": {"bsonType": "number", "description": "排序序号"}, "createTime": {"bsonType": "date", "description": "创建时间"}, "updateTime": {"bsonType": "date", "description": "更新时间"}}}