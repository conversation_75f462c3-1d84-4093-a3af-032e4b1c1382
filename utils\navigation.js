/**
 * 导航工具函数
 * 统一处理页面跳转逻辑，自动判断使用navigateTo还是switchTab
 */

// tabbar页面列表
const TABBAR_PAGES = [
  'pages/index/index',
  'pages/add/add', 
  'pages/statistics/statistics',
  'pages/settings/settings'
]

/**
 * 智能导航函数
 * @param {string} url - 目标页面路径
 * @param {object} options - 导航选项
 */
export function navigateTo(url, options = {}) {
  // 提取页面路径（去掉参数）
  const pagePath = url.split('?')[0]
  
  // 判断是否为tabbar页面
  const isTabbarPage = TABBAR_PAGES.includes(pagePath)
  
  if (isTabbarPage) {
    // tabbar页面使用switchTab
    return uni.switchTab({
      url: pagePath,
      ...options
    })
  } else {
    // 普通页面使用navigateTo
    return uni.navigateTo({
      url,
      ...options
    })
  }
}

/**
 * 跳转到记账页面
 * @param {string} type - 记账类型 ('income' | 'expense')
 * @param {string} recordId - 编辑记录的ID（可选）
 */
export function navigateToAdd(type = null, recordId = null) {
  if (type) {
    // 快速记账，存储类型
    uni.setStorageSync('quickAddType', type)
  }
  
  if (recordId) {
    // 编辑记录，存储记录ID
    uni.setStorageSync('editRecordId', recordId)
  }
  
  return uni.switchTab({
    url: '/pages/add/add'
  })
}

/**
 * 跳转到账单列表页面
 * @param {object} filters - 筛选条件（可选）
 */
export function navigateToList(filters = null) {
  if (filters) {
    // 存储筛选条件
    uni.setStorageSync('listFilters', filters)
  }
  
  return uni.navigateTo({
    url: '/pages/list/list'
  })
}

/**
 * 跳转到统计页面
 * @param {string} timeRange - 时间范围（可选）
 */
export function navigateToStatistics(timeRange = null) {
  if (timeRange) {
    uni.setStorageSync('statisticsTimeRange', timeRange)
  }
  
  return uni.switchTab({
    url: '/pages/statistics/statistics'
  })
}

/**
 * 跳转到设置页面
 */
export function navigateToSettings() {
  return uni.switchTab({
    url: '/pages/settings/settings'
  })
}

/**
 * 跳转到首页
 */
export function navigateToHome() {
  return uni.switchTab({
    url: '/pages/index/index'
  })
}

/**
 * 返回上一页
 * @param {number} delta - 返回层数，默认1
 */
export function navigateBack(delta = 1) {
  return uni.navigateBack({
    delta
  })
}

/**
 * 重定向到指定页面
 * @param {string} url - 目标页面路径
 * @param {object} options - 重定向选项
 */
export function redirectTo(url, options = {}) {
  return uni.redirectTo({
    url,
    ...options
  })
}

/**
 * 重新启动到指定页面
 * @param {string} url - 目标页面路径
 * @param {object} options - 重启选项
 */
export function reLaunch(url, options = {}) {
  return uni.reLaunch({
    url,
    ...options
  })
}

/**
 * 获取当前页面路径
 */
export function getCurrentPagePath() {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    return currentPage.route
  }
  return ''
}

/**
 * 检查是否为tabbar页面
 * @param {string} pagePath - 页面路径
 */
export function isTabbarPage(pagePath) {
  return TABBAR_PAGES.includes(pagePath)
}

/**
 * 获取页面参数
 * @param {string} key - 参数键名
 * @param {any} defaultValue - 默认值
 */
export function getPageParam(key, defaultValue = null) {
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    return currentPage.options[key] || defaultValue
  }
  return defaultValue
}

/**
 * 清理导航相关的临时存储
 */
export function clearNavigationStorage() {
  uni.removeStorageSync('quickAddType')
  uni.removeStorageSync('editRecordId')
  uni.removeStorageSync('listFilters')
  uni.removeStorageSync('statisticsTimeRange')
}

export default {
  navigateTo,
  navigateToAdd,
  navigateToList,
  navigateToStatistics,
  navigateToSettings,
  navigateToHome,
  navigateBack,
  redirectTo,
  reLaunch,
  getCurrentPagePath,
  isTabbarPage,
  getPageParam,
  clearNavigationStorage
}
