/**
 * 启动测试脚本
 * 检查应用是否能正常启动
 */

// 模拟uni环境
global.uni = {
  getSystemInfoSync: () => ({
    deviceId: 'test_device_123',
    system: 'test_system'
  }),
  setStorageSync: (key, value) => {
    console.log('setStorageSync:', key, value);
  },
  getStorageSync: (key) => {
    console.log('getStorageSync:', key);
    return null;
  },
  getNetworkType: (options) => {
    setTimeout(() => {
      options.success({ networkType: 'wifi' });
    }, 100);
  }
};

// 模拟uniCloud环境
global.uniCloud = {
  callFunction: async (options) => {
    console.log('callFunction:', options.name, options.data);
    return {
      result: {
        code: 200,
        message: '测试成功',
        data: {}
      }
    };
  }
};

// 简单的语法检查
function testSyntax() {
  try {
    console.log('开始语法检查...');

    // 测试基本的ES6语法
    const testObj = {
      async testMethod() {
        return { success: true };
      }
    };

    console.log('✅ ES6对象方法语法正常');

    // 测试箭头函数
    const arrowFunc = () => {
      return 'arrow function works';
    };

    console.log('✅ 箭头函数语法正常:', arrowFunc());

    // 测试模板字符串
    const template = `模板字符串测试: ${Date.now()}`;
    console.log('✅ 模板字符串语法正常:', template);

    console.log('🎉 语法检查完成');

  } catch (error) {
    console.error('❌ 语法检查失败:', error);
  }
}

// 运行测试
testSyntax();
